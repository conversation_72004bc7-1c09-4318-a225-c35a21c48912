# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PersonalyApiService do
  let(:mock_advertiser_id) { 'test_advertiser_123' }
  let(:mock_api_key) { 'test_api_key_456' }
  let(:start_date) { Date.parse('2025-06-16') }
  let(:end_date) { Date.parse('2025-06-16') }
  let(:group_by) { ['date', 'bundle'] }

  # 使用自定义参数创建服务实例，避免使用真实ENV数据
  subject(:api_service) { described_class.new(advertiser_id: mock_advertiser_id, api_key: mock_api_key) }

  around do |example|
    WebMock.enable!
    example.run
    WebMock.disable!
  end

  describe '#initialize' do
    context 'with custom parameters' do
      let(:custom_service) { described_class.new(advertiser_id: 'custom_id', api_key: 'custom_key') }

      it 'uses provided advertiser_id and api_key' do
        expect(custom_service.send(:advertiser_id)).to eq('custom_id')
        expect(custom_service.send(:api_key)).to eq('custom_key')
      end
    end

    context 'without custom parameters' do
      before do
        # 模拟ENV变量，不使用真实数据
        allow(ENV).to receive(:[]).with('PERSONALY_ADVERTISER_ID').and_return('mock_env_id')
        allow(ENV).to receive(:[]).with('PERSONALY_API_KEY').and_return('mock_env_key')
      end

      it 'uses ENV variables' do
        service = described_class.new
        expect(service.send(:advertiser_id)).to eq('mock_env_id')
        expect(service.send(:api_key)).to eq('mock_env_key')
      end
    end

    context 'when ENV variables are not set' do
      before do
        allow(ENV).to receive(:[]).with('PERSONALY_ADVERTISER_ID').and_return(nil)
        allow(ENV).to receive(:[]).with('PERSONALY_API_KEY').and_return(nil)
      end

      it 'uses nil values when ENV variables are not set' do
        service = described_class.new
        expect(service.send(:advertiser_id)).to be_nil
        expect(service.send(:api_key)).to be_nil
      end
    end
  end

  describe '#fetch_personaly_data' do
    let(:expected_query_params) do
      {
        advertiserId: mock_advertiser_id,
        apiKey: mock_api_key,
        groupBy: 'date,bundle',
        startDate: '16-06-2025',
        endDate: '16-06-2025',
        timezone: 'utc'
      }
    end

    let(:successful_response_body) do
      [
        {
          'campaign_name' => 'Test_Campaign_123',
          'date' => '16-06-2025',
          'impressions' => 17936,
          'clicks' => 15172,
          'installs' => 9,
          'cost' => 193.3189
        }
      ]
    end

    context 'when API request is successful' do
      before do
        stub_request(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_query_params)
          .to_return(
            status: 200,
            body: successful_response_body.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'returns parsed response data' do
        result = api_service.fetch_personaly_data(start_date: start_date, end_date: end_date)

        expect(result).to eq(successful_response_body)
      end

      it 'makes request with correct parameters' do
        api_service.fetch_personaly_data(start_date: start_date, end_date: end_date)

        expect(WebMock).to have_requested(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_query_params)
      end
    end

    context 'when API request fails with non-200 status' do
      before do
        stub_request(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_query_params)
          .to_return(
            status: 500,
            body: 'Internal Server Error',
            headers: { 'Content-Type' => 'text/plain' }
          )

        # Mock the notification methods to avoid real notifications
        allow(api_service).to receive(:raise_sentry_and_slack_notification!) do |subject, e, more_info|
          # Re-raise the error to be caught by the test
          raise PersonalyApiService::PersonalyApiError, e.message
        end

        allow(SlackService).to receive(:send_notification_to_channel)
      end

      it 'raises PersonalyApiError' do
        expect { api_service.fetch_personaly_data(start_date: start_date, end_date: end_date) }
          .to raise_error(PersonalyApiService::PersonalyApiError, /HTTParty response code: 500/)
      end
    end

    context 'when network error occurs' do
      before do
        # Stub the HTTP request to simulate a network error
        stub_request(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_query_params)
          .to_timeout

        # Mock notification services to avoid real notifications
        allow(SlackService).to receive(:send_notification_to_channel)
        allow(api_service).to receive(:raise_sentry_and_slack_notification!) do |subject, e, more_info|
          raise PersonalyApiService::PersonalyApiError, e.message
        end
      end

      it 'raises a PersonalyApiError and sends notification' do
        expect { api_service.fetch_personaly_data(start_date: start_date, end_date: end_date) }
          .to raise_error(PersonalyApiService::PersonalyApiError)

        expect(SlackService).to have_received(:send_notification_to_channel)
          .with(anything, :star)
      end
    end

    context 'with custom advertiser_id and api_key' do
      let(:custom_service) { described_class.new(advertiser_id: 'custom_id', api_key: 'custom_key') }
      let(:expected_custom_query_params) do
        {
          advertiserId: 'custom_id',
          apiKey: 'custom_key',
          groupBy: 'date,bundle',
          startDate: '16-06-2025',
          endDate: '16-06-2025',
          timezone: 'utc'
        }
      end

      before do
        stub_request(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_custom_query_params)
          .to_return(
            status: 200,
            body: successful_response_body.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'makes a request with custom credentials' do
        custom_service.fetch_personaly_data(start_date: start_date, end_date: end_date)
        expect(WebMock).to have_requested(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_custom_query_params)
      end
    end

    context 'with custom group_by parameters' do
      let(:custom_group_by) { ['date', 'bundle', 'campaign'] }
      let(:expected_query_params_custom) do
        {
          advertiserId: mock_advertiser_id,
          apiKey: mock_api_key,
          groupBy: 'date,bundle,campaign',
          startDate: '16-06-2025',
          endDate: '16-06-2025',
          timezone: 'utc'
        }
      end

      before do
        stub_request(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_query_params_custom)
          .to_return(
            status: 200,
            body: successful_response_body.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'uses custom group_by parameters' do
        api_service.fetch_personaly_data(
          start_date: start_date,
          end_date: end_date,
          group_by: custom_group_by
        )

        expect(WebMock).to have_requested(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_query_params_custom)
      end
    end

    context 'with default parameters' do
      let(:yesterday) { Date.yesterday }
      let(:expected_default_params) do
        {
          advertiserId: mock_advertiser_id,
          apiKey: mock_api_key,
          groupBy: 'date,bundle',
          startDate: yesterday.strftime('%d-%m-%Y'),
          endDate: yesterday.strftime('%d-%m-%Y'),
          timezone: 'utc'
        }
      end

      before do
        stub_request(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_default_params)
          .to_return(
            status: 200,
            body: successful_response_body.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
      end

      it 'uses default date parameters when none provided' do
        api_service.fetch_personaly_data

        expect(WebMock).to have_requested(:get, 'http://reporting.personaly.bid/rtb/singular')
          .with(query: expected_default_params)
      end
    end
  end

  describe '#format_date' do
    it 'formats Date object correctly' do
      formatted_date = api_service.send(:format_date, Date.parse('2025-06-16'))
      expect(formatted_date).to eq('16-06-2025')
    end

    it 'formats string date correctly' do
      formatted_date = api_service.send(:format_date, '2025-06-16')
      expect(formatted_date).to eq('16-06-2025')
    end

    it 'handles different date formats' do
      formatted_date = api_service.send(:format_date, '16/06/2025')
      expect(formatted_date).to eq('16-06-2025')
    end
  end

  describe '#build_query_params' do
    it 'builds correct query parameters' do
      params = api_service.send(:build_query_params, start_date, end_date, group_by)

      expect(params).to eq({
        advertiserId: mock_advertiser_id,
        apiKey: mock_api_key,
        groupBy: 'date,bundle',
        startDate: '16-06-2025',
        endDate: '16-06-2025'
      })
    end

    it 'handles single group_by parameter' do
      params = api_service.send(:build_query_params, start_date, end_date, ['date'])

      expect(params[:groupBy]).to eq('date')
    end

    it 'handles multiple group_by parameters' do
      params = api_service.send(:build_query_params, start_date, end_date, ['date', 'bundle', 'campaign'])

      expect(params[:groupBy]).to eq('date,bundle,campaign')
    end
  end

  describe '#check_httparty_response!' do
    let(:response_200) { double('Response', code: 200) }
    let(:response_500) { double('Response', code: 500, body: 'Server Error') }

    it 'does not raise error for 200 status' do
      expect { api_service.send(:check_httparty_response!, response_200) }
        .not_to raise_error
    end

    it 'raises PersonalyApiError for non-200 status' do
      expect { api_service.send(:check_httparty_response!, response_500) }
        .to raise_error(PersonalyApiService::PersonalyApiError, /HTTParty response code: 500/)
    end
  end

  describe 'error handling and notifications' do
    let(:mock_job_stat) { double('JobStat', id: 123) }
    let(:mock_click_urls) { ['click_url_1', 'click_url_2'] }
    let(:mock_pa_users) { '@test_user' }

    before do
      # Mock Thread.current to avoid real job context
      allow(Thread.current).to receive(:[]).with(:current_job).and_return('TestJob')

      # Mock all notification services
      allow(SlackService).to receive(:send_notification_to_channel)

      # Mock JobStatsConcern methods to avoid database calls
      allow(api_service).to receive(:get_current_job_stat).and_return(mock_job_stat)
      allow(api_service).to receive(:get_current_job_click_urls).and_return(mock_click_urls)
      allow(api_service).to receive(:get_current_job_assign_pa_users).and_return(mock_pa_users)
    end

    it 'includes job information in error messages' do
      stub_request(:get, 'http://reporting.personaly.bid/rtb/singular')
        .with(query: hash_including(advertiserId: mock_advertiser_id))
        .to_return(status: 500, body: 'Internal Server Error')

      expect { api_service.fetch_personaly_data(start_date: start_date, end_date: end_date) }
        .to raise_error(PersonalyApiService::PersonalyApiError, /HTTParty response code: 500/)

      expect(SlackService).to have_received(:send_notification_to_channel)
        .with(anything, :star)
    end

    it 'handles missing job context gracefully' do
      allow(Thread.current).to receive(:[]).with(:current_job).and_return(nil)
      allow(api_service).to receive(:get_current_job_stat).and_return(nil)

      stub_request(:get, 'http://reporting.personaly.bid/rtb/singular')
        .with(query: hash_including(advertiserId: mock_advertiser_id))
        .to_return(status: 404, body: 'Not Found')

      expect { api_service.fetch_personaly_data(start_date: start_date, end_date: end_date) }
        .to raise_error(PersonalyApiService::PersonalyApiError, /HTTParty response code: 404/)
    end
  end
end