---
version: 2

jobs:
  build:
    parallelism: 1
    working_directory: ~/worker
    resource_class: feed-mob/self-hosted
    docker:
      - image: cimg/ruby:3.1.4-browsers
        environment:
          RAILS_ENV: test
          ELASTICSEARCH_HOST: "localhost:9250"
          LAMBDA_API_REDIS_URL: "redis://127.0.0.1:6379"
          LAMBDA_API_REDIS_REPLICA_URL: "redis://127.0.0.1:6379/1"
          LAMBDA_API_REDIS_STANDBY_URL: "redis://127.0.0.1:6379/2"
          LAMBDA_API_REDIS_REPLICA_STANDBY_URL: "redis://127.0.0.1:6379/3"
          REALTIME_REDIS_URL: "redis://127.0.0.1:6379/5"
          CONVERSION_WORKER_ACTION_REDIS_URL: "redis://127.0.0.1:6379/4"
          NEW_SENTRY_API_ENDPOINT: https://sentry.io/api/0
          NEW_SENTRY_ORG_SLUG: "sentry"
          NEW_SENTRY_API_TOKEN: ""
          SENDGRID_API_KEY: ""
          ENABLE_SENDGRID: "false"
          YNWA_SLACK_CHANNEL: "ynwa-team"
          SHSF_SLACK_CHANNEL: "shsf_team"
          APPSFLYER_SLACK_CHANNEL_IDS: "123"
          CI_LOG_ENDPOINT: "https://ci-api.feedmob.com"
          APPLE_PARTNERIZE_APPLICATION_KEY: ""
          APPLE_PARTNERIZE_API_KEY: ""
          FEEDMOB_WORKER_TEST: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_worker_test"
          FEEDMOB_WORKER_TEST_CONVERSION: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_worker_test_conversion"
          REDSHIFT_FEEDMOB_DATABASE_URL: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_worker_test_redshift"
          FEEDMOB_WORKER_TEST_REALTIME: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_worker_test_realtime"
          HISTORICAL_REDSHIFT_DATABASE_URL: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_worker_test_redshift_historical"
          FEEDMOB_WORKER_TEST_AGENCY_DATA: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_worker_test_agency_data"
          ALL_PING_DATABASE_URL: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_ua_conversion_test"
          MEDIA_DELIVERY_DATABASE_URL: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_media_delivery_test"
          AGENCY_CONVERSION_DATABASE_URL: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_agency_conversion_test"
          TIME_OFF_DATABASE_URL: "postgres://app:superinsecure@127.0.0.1:5432/feedmob_time_off_test"

      - image: cimg/postgres:15.5-postgis
        environment:
          POSTGRES_USER: app
          POSTGRES_DB: app
          POSTGRES_PASSWORD: superinsecure

      - image: redis
      - image: docker.elastic.co/elasticsearch/elasticsearch:6.8.23
        environment:
          - cluster.name: feedmob-worker-test-cluster
          - xpack.security.enabled: false
          - transport.host: localhost
          - network.host: 127.0.0.1
          - http.port: 9250
          - discovery.type: single-node

    steps:
      - run: mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts

      - checkout
      - run: echo `grep -c processor /proc/cpuinfo`

      - run: sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 4EB27DB2A3B88B8B
      - run: sudo apt-get update
      - run: sudo apt-get install postgresql-client

      - run:
          name: Configure Bundler
          command: |
            echo 'export BUNDLER_VERSION=$(cat Gemfile.lock | tail -1 | tr -d " ")' >> $BASH_ENV
            source $BASH_ENV
            gem install bundler

      # Restore Cached Dependencies
      - type: cache-restore
        name: Restore bundle cache
        key: feedmob-worker-gem-cache-{{ checksum "Gemfile.lock" }}

      # Bundle Install Dependencies
      - run: bundle config gems.contribsys.com $SIDEKIQ_PRO_NAME:$SIDEKIQ_PRO_PASS
      - run: bundle install --path vendor/bundle

      #开始
      - run: bundle exec rake workflow:generate_circleci_start workflow_key=$CIRCLE_WORKFLOW_ID branch=$CIRCLE_BRANCH

      # Cache Dependencies
      - type: cache-save
        name: Store bundle cache
        key: feedmob-worker-gem-cache-{{ checksum "Gemfile.lock" }}
        paths:
          - vendor/bundle

      # Setup the Database
      - run: bundle exec rails db:setup

      # run Brakeman
      - run: bundle exec brakeman -o coverage/brakeman/output.html -o coverage/brakeman/output.json 2>&1 || true

      - store_artifacts:
          path: coverage/brakeman

      # Run the Tests1
      - run:
          name: Run Rspec tests1
          command: |
            bundle exec rspec --pattern "spec/jobs1/**/*_spec.rb"

      - store_artifacts:
          path: coverage/rspec

      # Run the Tests
      - run:
          name: Run Rspec tests
          command: |
            bundle exec rspec --pattern 'spec/**/*_spec.rb' --exclude-pattern 'spec/(jobs1)/**'

      - store_artifacts:
          path: coverage/rspec

      #结束
      - run: bundle exec rake workflow:generate_circleci_end workflow_key=$CIRCLE_WORKFLOW_ID branch=$CIRCLE_BRANCH
