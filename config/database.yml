default: &default
  adapter: postgresql
  encoding: unicode
  # For details on connection pooling, see Rails configuration guide
  # http://guides.rubyonrails.org/configuring.html#database-pooling
  # puma 最大线程是 8，所以这里设置的大一点
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 10 } %>
  port: 5432

test:
  primary:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_main_test' %>
  conversion_main:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_conversion' %>
  conversion_replica:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_conversion' %>
  new_conversion_main:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_conversion' %>
  new_conversion_replica:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_conversion' %>
  redshift_main:
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_redshift' %>
  redshift_stage:
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_redshift' %>
  realtime:
    <<: *default
    url: <%= 'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_realtime' %>
  historical_main:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_conversion' %>
  historical_redshift:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_redshift_historical' %>
  redshift_impressions:
    url: <%= 'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_redshift_impression' %>
  redshift_clicks:
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_redshift_click' %>
  agency_data:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_test_agency_data' %>
  all_ping_conversion:
    <<: *default
    url: <%='postgresql://postgres:postgres@localhost:5432/feedmob_worker_ua_conversion' %>
  agency_conversion:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_agency_conversion' %>
  time_off:
    <<: *default
    url: <%=  'postgresql://postgres:postgres@localhost:5432/feedmob_worker_time_off' %>


