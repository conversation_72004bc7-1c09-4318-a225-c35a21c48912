# frozen_string_literal: true

class RefreshStatRecordsV4ByRedshiftJob < ApplicationJob
  queue_as :heavy_job

  include RefreshDataService
  include ErbBinding
  include BillableConversionDataChecker

  CLIENT_ID_IPVANISH = 161

  attr_reader :start_date, :end_date, :click_url_ids

  BASIC_TABLE = 'v4_base_stat_records'.freeze
  TABLE = 'v4_stat_records'.freeze

  def perform(start_date: Date.yesterday.to_s, end_date: Time.zone.today.to_s, click_url_ids: [])
    @start_date = start_date
    @end_date   = end_date
    @click_url_ids = click_url_ids

    # Generate click and impression view
    RefreshPrepareStatRecordsJob.perform_now(
      start_date: start_date,
      end_date: end_date,
      click_more_than: 30,
      table: 'v4_app_click_and_impression',
      click_url_ids: click_url_ids
    )

    # Generate v4 base stat_records
    generate_base_stat_record

    # Generate cohort_and_roas data and v4_stat_records
    generate_stat_record
  rescue PG::InternalError, ActiveRecord::StatementInvalid, TableConflictsError => e
    SlackService.send_notification("[#{Rails.env}]#{self.class}##{__method__}: #{e.message}")
    SlackService.send_notification_to_star("[#{Rails.env}]#{self.class}##{__method__}: #{e.message}")
    RefreshStatRecordsV4ByRedshiftJob.set(wait: 15.minutes).perform_later(start_date: @start_date, end_date: @end_date, click_url_ids: click_url_ids)
  end

  def non_purchase_revenue_track_types
    TrackingConfig.find_by(config_type: 'system', name: 'NON_PURCHASE_REVENUE_TRACK_TYPES')&.value.with_indifferent_access || {}
  end

  def generate_stat_record
    if table_exist?('ConversionRecordRedshift', TABLE)
      sql = [
        read_sql_from_file(sql_file_name('s3'), {recent_day: 60, non_purchase_revenue_track_types: non_purchase_revenue_track_types}),
        read_sql_from_file(sql_file_name('s4'), {recent_day: 60, click_url_ids: click_url_ids}),
        delete_table_data_by_date(TABLE, 60, click_url_ids),
        copy_table_data("tmp_#{TABLE}", TABLE),
        drop_table("tmp_#{TABLE}"),
        drop_table('tmp_v4_stat_records_cohort_and_roas')
      ].join('; ')
    else
      sql =[
        read_sql_from_file(sql_file_name('s3'), {recent_day: 60, non_purchase_revenue_track_types: non_purchase_revenue_track_types}),
        read_sql_from_file(sql_file_name('s4'), {recent_day: 60, click_url_ids: click_url_ids}),
        rename_table("tmp_#{TABLE}", TABLE),
        drop_table("tmp_#{TABLE}"),
        drop_table('tmp_v4_stat_records_cohort_and_roas')
      ].join('; ')
    end
    tables = {
      conversion_records: :read,
      v4_base_stat_records: :read,
      v4_stat_records: :update
    }

    command = RaiseTableConflictsCommand.new(message: conflict_info(tables.keys.join(',')))

    $feedmob_fm_api_client.v2_table_conflict(command, command.v2_table_infos(tables)) do
      ConversionRecordRedshift.connection.execute(sql)
    end
  end

  def conflict_info(table)
    "#{self.class} redshift table Conflict,Re-execute in 15 minutes。It may be that the #{table} is in use。"
  end

  def generate_base_stat_record
    if table_exist?('ConversionRecordRedshift', BASIC_TABLE)
      sql = [
        read_sql_from_file(
          sql_file_name('s1'),
          { start_date: start_date, end_date: end_date, click_url_ids: click_url_ids}
        ),
        read_sql_from_file(sql_file_name('s2'), {
          calculate_event_spend_click_urls: calculate_event_spend_click_url_ids, 
        }),
        delete_table_data_by_date_range(BASIC_TABLE, start_date, end_date, click_url_ids),
        copy_table_data("tmp_group_#{BASIC_TABLE}", BASIC_TABLE),
        drop_table("tmp_group_#{BASIC_TABLE}"),
        drop_table("tmp_#{BASIC_TABLE}")
      ].join('; ')
    else
      sql = [
        read_sql_from_file(
          sql_file_name('s1'),
          { start_date: (Time.zone.today - 60.days).to_s, end_date: Time.zone.today.to_s, click_url_ids: click_url_ids}
        ),
        read_sql_from_file(sql_file_name('s2'), {
          calculate_event_spend_click_urls: calculate_event_spend_click_url_ids, 
        }),
        rename_table("tmp_group_#{BASIC_TABLE}", BASIC_TABLE),
        drop_table("tmp_group_#{BASIC_TABLE}"),
        drop_table("tmp_#{BASIC_TABLE}")
      ].join('; ')
    end

    tables = {
      conversion_records: :read,
      v4_app_click_and_impression: :read,
      click_url_infos: :read,
      new_net_spends: :read,
      v4_base_stat_records: :update
    }

    command = RaiseTableConflictsCommand.new(message: conflict_info(tables.keys.join(',')))

    $feedmob_fm_api_client.v2_table_conflict(command, command.v2_table_infos(tables)) do
      ConversionRecordRedshift.connection.execute(sql)
    end
  end

  def sql_file_name(type)
    "app/data_views/#{TABLE}_#{type}.sql"
  end

  def calculate_event_spend_click_url_ids
    ClickUrl.joins(:campaign).where("campaigns.client_id = ?", CLIENT_ID_IPVANISH).where(link_type: :fm_link, direct_spend_input: true).where.not(bind_action: :impression).pluck("click_urls.id")
  end
end
