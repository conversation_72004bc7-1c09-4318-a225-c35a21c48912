# frozen_string_literal: true

class RefreshStatRecordsSpendJob < ApplicationJob
  queue_as :heavy_job

  include RefreshDataService
  include ConflictCheckConcern

  def perform(start_date, end_date, click_url_ids: [])
    if (Date.today - start_date.to_date).to_i > 60
      (start_date.to_date..end_date.to_date).each do |job_date|
        SyncRedshiftClickInfoJob.perform_now(job_date.to_s)
      end
    end

    v2_check_conflict(notification_clazz: 'SendConflictsToSlackCommand', table_names: {
      v4_base_stat_records: :update,
      v4_stat_records: :update,
      v5_base_stat_records: :update,
      v5_stat_records: :update
    }) do
      if table_exist?('ConversionRecordRedshift', 'v4_base_stat_records')
        base_sql = [
          read_sql_from_file('app/data_views/refresh_base_stat_records_spend.sql', {
            table_name: 'v4_base_stat_records', 
            include_creative: false, 
            include_additional_events: true, 
            include_memo: true, 
            start_date: start_date, 
            end_date: end_date.to_date + 1.day, 
            click_url_ids: click_url_ids, 
          }),
          delete_table_data_by('v4_base_stat_records', start_date, end_date.to_date + 1.day, click_url_ids),
          copy_table_data("tmp_v4_base_stat_records_spend", "v4_base_stat_records"),
          drop_table('tmp_v4_base_stat_records_spend')
        ].join('; ')

        ConversionRecordRedshift.connection.execute(base_sql)

        if table_exist?('ConversionRecordRedshift', 'v4_stat_records')
          # only update spend related values in v4_stat_records
          # other fields should not be changed
          v4_sql = [
            read_sql_from_file('app/data_views/refresh_stat_records_spend.sql', {
              table_name: 'v4_stat_records', 
              include_additional_events: true, 
              include_memo: true, 
              start_date: start_date, 
              end_date: end_date.to_date + 1.day, 
              click_url_ids: click_url_ids,
            }),
            delete_table_data_by('v4_stat_records', start_date, end_date.to_date + 1.day, click_url_ids),
            copy_table_data("tmp_v4_stat_records_spend", "v4_stat_records"),
            drop_table('tmp_v4_stat_records_spend')
          ].join('; ')

          ConversionRecordRedshift.connection.execute(v4_sql)
          RefreshV4CampaignsViewJob.perform_later
        end
      end

      if table_exist?('ConversionRecordRedshift', 'v5_base_stat_records')
        base_sql = [
          read_sql_from_file('app/data_views/refresh_base_stat_records_spend.sql', {
            table_name: 'v5_base_stat_records', 
            include_creative: true, 
            include_additional_events: true, 
            start_date: start_date, 
            end_date: end_date.to_date + 1.day, 
            click_url_ids: click_url_ids,
          }),
          delete_table_data_by('v5_base_stat_records', start_date, end_date.to_date + 1.day, click_url_ids),
          copy_table_data("tmp_v5_base_stat_records_spend", "v5_base_stat_records"),
          drop_table('tmp_v5_base_stat_records_spend')
        ].join('; ')

        ConversionRecordRedshift.connection.execute(base_sql)

        if table_exist?('ConversionRecordRedshift', 'v5_stat_records')
          # only update spend related values in v5_stat_records
          # other fields should not be changed
          v5_sql = [
            read_sql_from_file('app/data_views/refresh_stat_records_spend.sql', {
              table_name: 'v5_stat_records', 
              include_additional_events: true, 
              start_date: start_date, 
              end_date: end_date.to_date + 1.day, 
              click_url_ids: click_url_ids, 
            }),
            delete_table_data_by('v5_stat_records', start_date, end_date.to_date + 1.day, click_url_ids),
            copy_table_data("tmp_v5_stat_records_spend", "v5_stat_records"),
            drop_table('tmp_v5_stat_records_spend')
          ].join('; ')

          ConversionRecordRedshift.connection.execute(v5_sql)
        end
      end
    end
  end
end
