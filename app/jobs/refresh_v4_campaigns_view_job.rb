# frozen_string_literal: true

class RefreshV4CampaignsViewJob < ApplicationJob
  include RefreshDataService
  include NetSpendsChecker

  queue_as :heavy_job

  TABLE = 'v4_campaigns_view'

  def perform
    return unless data_ready?

    if related_jobs_running?
      RefreshV4CampaignsViewJob.set(wait: 15.minutes).perform_later
      return
    end

    sql = [new_data,
           drop_table(TABLE),
           rename_table("#{TABLE}_tmp", TABLE)].join(';')

    command = SendConflictsToEmailCommand.new(job_name: self.class.name, message: "[#{Rails.env} #{self.class}] #{TABLE} is not ready yet.")
    $feedmob_fm_api_client.v2_table_conflict(command, [command.v2_table_info(TABLE, :update)]) do
      ConversionRecordRedshift.connection.execute(sql)
    end

    RedisClient.fb_tracking_redis.set(self.class.name.demodulize, Time.zone.now.strftime('%Y-%m-%d %H:%M:%S UTC'))
  end

  def new_data
    File.read('app/data_views/v4_campaigns_view.sql').squish
  end

  def data_ready?
    cache_value = RedisClient.fb_tracking_redis.get('SyncRedshiftNetSpendsJob')
    refresh_timestamp = Time.parse(cache_value) if cache_value.present?
    question = false
    question = true unless refresh_timestamp && refresh_timestamp > Time.zone.now.beginning_of_day
    prefix = question ? '[QUESTION]' : '[PASS]'
    subscription = Subscription.by_subject('RefreshV4CampaignsViewJob Alert')
    if prefix == '[QUESTION]'
      NotificationMailer.notify_subscribers("#{prefix} RefreshV4CampaignsViewJob Alert", 'new_net_spends is not refreshed yet', subscription).deliver_now
      return false
    end

    check_date = Date.yesterday
    v4_stat_records_count = ConversionRecordRedshift.connection.query("SELECT COUNT(*) FROM v4_stat_records WHERE report_date = '#{check_date}'")[0][0].to_i
    question = v4_stat_records_count == 0
    prefix = question ? '[QUESTION]' : '[PASS]'
    if prefix == '[QUESTION]'
      NotificationMailer.notify_subscribers("#{prefix} RefreshV4CampaignsViewJob Alert", "v4_stat_records not exist on #{check_date}", subscription).deliver_now
      return false
    end

    NotificationMailer.notify_subscribers("#{prefix} RefreshV4CampaignsViewJob Alert", "new_net_spends and v4_stat_records exists on #{check_date}", subscription).deliver_now
    true
  end

  def related_jobs_running?
    workers = Sidekiq::Workers.new
    return true if workers && workers.select{|_, _, w| w['queue'] == 'independent_job' && w['payload'] && w['payload']['wrapped'] == 'VendorBillingRecordBatchJob'}.size > 0
  end
end
