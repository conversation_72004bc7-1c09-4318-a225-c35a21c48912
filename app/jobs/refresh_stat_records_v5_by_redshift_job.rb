# frozen_string_literal: true

class RefreshStatRecordsV5ByRedshiftJob < ApplicationJob
  queue_as :heavy_job

  include RefreshDataService
  include ErbBinding
  include BillableConversionDataChecker
  include ConflictCheckConcern

  attr_reader :start_date, :end_date, :click_url_ids

  BASIC_TABLE = 'v5_base_stat_records'.freeze
  TABLE = 'v5_stat_records'.freeze

  def perform(start_date: Date.yesterday.to_s, end_date: Time.zone.today.to_s, click_url_ids: [])
    @start_date = start_date
    @end_date   = end_date
    @click_url_ids = click_url_ids

    if related_jobs_running?
      SlackService.send_notification_to_channel("#{self.class}##{__method__}: RebuildRealtimeStatRecordsByDateJob is running", :mighty)
      RefreshStatRecordsV5ByRedshiftJob.set(wait: 15.minutes).perform_later(start_date: @start_date, end_date: @end_date, click_url_ids: click_url_ids)
      return
    end

    # Generate click and impression view
    RefreshPrepareStatRecordsJob.perform_now(
      start_date: start_date,
      end_date: end_date,
      click_more_than: nil,
      impression_more_than: nil,
      table: 'v5_app_creative_click_and_impression',
      click_url_ids: click_url_ids
    )

    success = v2_check_conflict(notification_clazz: 'SendConflictsToSlackCommand', table_names: {v5_base_stat_records: :update, v5_stat_records: :update}) do
      # Generate v5 base stat_records
      generate_base_stat_record
      # Generate cohort_and_roas data and v5_stat_records
      generate_stat_record
    end
    RefreshStatRecordsV5ByRedshiftJob.set(wait: 15.minutes).perform_later(start_date: start_date, end_date: end_date, click_url_ids: click_url_ids) if !success
  rescue PG::InternalError, ActiveRecord::StatementInvalid => e
    if e.message =~ /Serializable isolation violation/
      SlackService.send_notification("#{self.class}##{__method__}: #{e.message}")
      SlackService.send_notification_to_channel("#{self.class}##{__method__}: #{e.message}", :mighty)
      RefreshStatRecordsV5ByRedshiftJob.set(wait: 15.minutes).perform_later(start_date: start_date, end_date: end_date, click_url_ids: click_url_ids)
    else
      raise e
    end
  end

  def non_purchase_revenue_track_types
    TrackingConfig.find_by(config_type: 'system', name: 'NON_PURCHASE_REVENUE_TRACK_TYPES')&.value.with_indifferent_access || {}
  end

  def generate_stat_record
    if table_exist?('ConversionRecordRedshift', TABLE)
      sql = [
        read_sql_from_file(sql_file_name('s3'), {recent_day: 60, non_purchase_revenue_track_types: non_purchase_revenue_track_types}),
        read_sql_from_file(sql_file_name('s4'), {recent_day: 60, click_url_ids: click_url_ids}),
        delete_table_data_by_date(TABLE, 60, click_url_ids),
        copy_table_data("tmp_#{TABLE}", TABLE),
        drop_table("tmp_#{TABLE}"),
        drop_table('tmp_v5_stat_records_cohort_and_roas')
      ].join('; ')
    else
      sql =[
        read_sql_from_file(sql_file_name('s3'), {recent_day: 60, non_purchase_revenue_track_types: non_purchase_revenue_track_types}),
        read_sql_from_file(sql_file_name('s4'), {recent_day: 60, click_url_ids: click_url_ids}),
        rename_table("tmp_#{TABLE}", TABLE),
        drop_table("tmp_#{TABLE}"),
        drop_table('tmp_v5_stat_records_cohort_and_roas')
      ].join('; ')
    end

    ConversionRecordRedshift.connection.execute(sql)
  end

  def generate_base_stat_record
    if table_exist?('ConversionRecordRedshift', BASIC_TABLE)
      sql = [
        read_sql_from_file(
          sql_file_name('s1'),
          { start_date: start_date, end_date: end_date, click_url_ids: click_url_ids }
        ),
        read_sql_from_file(sql_file_name('s2')),
        delete_table_data_by_date_range(BASIC_TABLE, start_date, end_date, click_url_ids),
        copy_table_data("tmp_group_#{BASIC_TABLE}", BASIC_TABLE),
        drop_table("tmp_group_#{BASIC_TABLE}"),
        drop_table("tmp_#{BASIC_TABLE}")
      ].join('; ')
    else
      sql = [
        read_sql_from_file(
          sql_file_name('s1'),
          { start_date: (Time.zone.today - 60.days).to_s, end_date: Time.zone.today.to_s, click_url_ids: click_url_ids }
        ),
        read_sql_from_file(sql_file_name('s2')),
        rename_table("tmp_group_#{BASIC_TABLE}", BASIC_TABLE),
        drop_table("tmp_group_#{BASIC_TABLE}"),
        drop_table("tmp_#{BASIC_TABLE}")
      ].join('; ')
    end

    ConversionRecordRedshift.connection.execute(sql)
  end

  def sql_file_name(type)
    "app/data_views/#{TABLE}_#{type}.sql"
  end

  # https://github.com/feed-mob/tracking_admin/issues/11028
  # check if RebuildRealtimeStatRecordsByDateJob is running
  def related_jobs_running?
    workers = Sidekiq::Workers.new
    return true if workers && workers.select{|_, _, w| w['queue'] == 'daily_job' && w['payload'] && w['payload']['wrapped'] == 'RebuildRealtimeStatRecordsByDateJob'}.size > 0
  end
end
