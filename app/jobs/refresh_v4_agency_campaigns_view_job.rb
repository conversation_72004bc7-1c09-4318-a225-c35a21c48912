class RefreshV4AgencyCampaignsViewJob < ApplicationJob
  queue_as :heavy_job

  def perform
    sleep 6
    workers = Sidekiq::Workers.new
    return true if workers && workers.select{|_, _, w| w['queue'] == 'heavy_job' && w['payload'] && w['payload']['wrapped'] == 'RefreshV4AgencyCampaignsViewJob'}.size > 1

    if appsflyer_agency_report_not_ready?
      message = "#{report_date} - AppsflyerAgencyReport data is not ready"
      SlackService.send_notification_to_star(message)
    end

    AgencyRecord.connection.execute("REFRESH MATERIALIZED VIEW CONCURRENTLY v4_agency_campaigns_view;")
  end

  private

  def appsflyer_agency_report_not_ready?
    AppsflyerAgencyReport.
      where(date: report_date).
      last.
      blank?
  end

  def report_date
    Time.zone.yesterday - 1.day
  end
end
