# 将 redshift impressions 前一天的数据按小时统计保存到 v3_hourly_impression_counts
# 每天01:10 UTC触发
# https://github.com/feed-mob/tracking_admin/issues/14028

class RefreshV3HourlyImpressionCountsJob < ApplicationJob
  queue_as :daily_job

  attr_reader :date

  def perform(date: Date.yesterday.to_s)
    RedisClient.main.setex('refresh_v3_hourly_impression_counts_job:status', 600, 'working')
    @date = date.to_date
    save_to_redshift
    RedisClient.main.del('refresh_v3_hourly_impression_counts_job:status')
  rescue PG::InternalError, ActiveRecord::StatementInvalid => e
    if e.message =~ /Serializable isolation violation/
      SlackService.send_notification("#{self.class}##{__method__}: #{e.message}")
      SlackService.send_notification_to_channel("#{self.class}##{__method__}: #{e.message}", :mighty)
    else
      raise e
    end
  end

  private 

  def save_to_redshift
    V3ImpressionRedshift.connection.execute(
      <<-SQL
        DELETE FROM v3_hourly_impression_counts WHERE count_date = '#{date}';
        INSERT INTO v3_hourly_impression_counts
          SELECT campaign_id, vendor_id, click_id, client_id, event_time::Date AS count_date, EXTRACT(HOUR FROM event_time) as count_hour, 
                 count(*) AS impression_count, SUM(CASE WHEN blocked THEN 1 ELSE 0 END) AS blocked_count, SUM(CASE WHEN blocked THEN 0 ELSE 1 END) AS normal_count
          FROM impressions
          WHERE click_id IS NOT NULL
            AND event_time >= '#{date}' AND event_time < '#{date + 1.day}'
          GROUP BY 1, 2, 3, 4, 5, 6
      SQL
    )
  end
end
