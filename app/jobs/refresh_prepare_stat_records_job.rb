# frozen_string_literal: true

class RefreshPrepareStatRecordsJob < ApplicationJob
  queue_as :heavy_job

  include RefreshDataService
  include ErbBinding

  TABLE_SQL_MAP = {
    v4_app_click_and_impression: 'app/data_views/v4_stat_records_s0.sql',
    v5_app_creative_click_and_impression: 'app/data_views/v5_stat_records_s0.sql'
  }.freeze

  def perform(start_date: Date.yesterday.to_s, end_date: Time.zone.today.to_s, click_more_than: 5, impression_more_than: 30, table: 'v4_app_click_and_impression', click_url_ids: [])
    sql_file_path = TABLE_SQL_MAP[table.to_sym]

    if table_exist?('ConversionRecordRedshift', table)
      sql = [
        read_sql_from_file(
          sql_file_path,
          {
            start_date: start_date, end_date: end_date,
            click_more_than: click_more_than,
            impression_more_than: impression_more_than,
            click_url_ids: click_url_ids
          }
        ),
        delete_table_data_by_date_range(table, start_date, end_date, click_url_ids, 'click_id'),
        copy_table_data("tmp_#{table}", table),
        drop_table("tmp_#{table}"),
      ].join('; ')
    else
      sql = [
        read_sql_from_file(
          sql_file_path,
          {
            start_date: (Time.zone.today - 60.days).to_s, end_date: end_date,
            click_more_than: click_more_than,
            impression_more_than: impression_more_than,
            click_url_ids: click_url_ids
          }
        ),
        rename_table("tmp_#{table}", table)
      ].join('; ')
    end

    ConversionRecordRedshift.connection.execute(sql)
  end
end
