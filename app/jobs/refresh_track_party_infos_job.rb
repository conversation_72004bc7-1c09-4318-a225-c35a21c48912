class RefreshTrackPartyInfosJob < ApplicationJob
  queue_as :daily_job

  def perform
    sleep 6
    workers = Sidekiq::Workers.new
    return true if workers && workers.select{|_, _, w| w['queue'] == 'heavy_job' && w['payload'] && w['payload']['wrapped'] == 'RefreshTrackPartyInfosJob'}.size > 1

    TrackPartyInfo.connection.execute(sql)
  end

  private

  def sql
    File.read('app/data_views/refresh_track_party_infos.sql').squish
  end
end
