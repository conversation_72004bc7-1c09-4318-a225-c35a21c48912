class ZipRecruiterReportDataCommand
  prepend SimpleCommand

  include ActionView::Helpers::NumberHelper
  include IronSourceApiConcern
  include IronSourceAuraApiConcern
  include QuoraApiConcern
  include ReportConfigConcern

  attr_reader :start_date, :end_date

  RESULT_ATTRIBUTES = [
    :date,
    :campaign_name,
    :vendor_name,
    :af_install,
    :af_purchase,
    :cvr2,
    :net_spend,
    :gross_spend,
    :margin,
    :cpa,
    :samsung_install,
    :samsung_spend,
    :samsung_cpe,
    :profit,
    :delta,
    :delta_percent,
    :month
  ].freeze
  Result = Struct.new(*RESULT_ATTRIBUTES)

  CONTENT_ID = '000008182313'

  def initialize(start_date, end_date)
    @start_date = start_date
    @end_date = end_date
  end

  def call
    report_datas = []
    click_urls = ClickUrl.includes(:campaign,:vendor).where(id: click_url_ids)


    datas = []
    (start_date.to_date..end_date.to_date).each do |date|
      agency_af_datas  = get_data_from_appsflyer_agency_reports(date)
      fm_link_af_datas = get_data_from_appsflyer_reports(date)
      spend_data = fetch_spends(date)

      click_urls.each do |click_url|
        af_data = if click_url.link_type == 'fm_link'
          fm_link_af_datas.find {|c|c['click_url_id'] == click_url.id && c['event_date'] == date.to_s}.to_h
        else
          agency_af_datas.find {|c|c['click_url_id'] == click_url.id && c['event_date'] == date.to_s}.to_h
        end

        spend = spend_data.find {|c|c['click_url_id'] == click_url.id && c['date'] == date.to_s}.to_h

        if click_url.id == 21644
          samsung_install = samsung_reports(date).find{ |r| r['startDate'].to_date == date.to_date}.to_h[CONTENT_ID]['total_unique_installs_filter'].to_h['value'].to_i
        else
          samsung_install = 0
        end

        next if spend.blank? && af_data['install'].to_i == 0 && af_data['purchase'].to_i == 0 && samsung_install == 0

        gross_cpi = click_url.gross_cpi_by_date(date)
        net_cpi = click_url.net_cpi_by_date(date)
        row = [
          date.to_date.strftime("%-m/%-d/%Y"),
          click_url.campaign.name,
          click_url.vendor.vendor_name,
          af_data['install'].to_i,
          af_data['purchase'].to_i,
          "=E{{ROW}}/D{{ROW}}",
          spend['net_spend'].to_f,
          spend['gross_spend'].to_f,
          "=(H{{ROW}}-G{{ROW}})/H{{ROW}}",
          "=H{{ROW}}/E{{ROW}}",
        ]

        if click_url.id == 21644
          row = row + [
            samsung_install,
            "=K{{ROW}}*#{net_cpi}",
            "=L{{ROW}}/E{{ROW}}",
            "=H{{ROW}}-G{{ROW}}",
            "=K{{ROW}}-D{{ROW}}",
            "=O{{ROW}}/K{{ROW}}",
          ]
        else
          row = row + ['','','','','','']
        end

        row << date.to_date.beginning_of_month.strftime("%b-%Y")

        datas << Result.new(*row)
      end
    end

    datas
  end

  def fetch_spends(date)
    stat_records_res = ConversionRecordRedshift.connection.query(
      <<-SQL
        SELECT
          sr.click_url_id,
          sr.report_date as date,
          SUM(sr.spend_cents)/100.0 as gross_spend,
          SUM(sr.net_spend_cents)/100.0 as net_spend
        FROM v4_stat_records sr
        LEFT JOIN click_url_infos info ON info.click_url_id = sr.click_url_id AND info.event_date = sr.report_date
        WHERE report_date = '#{date}' AND sr.status IN ('normal', 'injected') AND sr.click_url_id IN (#{click_url_ids.join(',')})
          AND info.direct_spend_input = false
          AND (sr.spend_cents > 0 OR sr.net_spend_cents > 0)
        GROUP BY 1, 2
      SQL
    ).map{|r| [[r[0].to_i, r[1]], [r[2],r[3]]]}.to_h
    net_spends_res = NetSpend.connection.query(
      <<-SQL
        SELECT
          ns.click_url_id,
          ns.spend_date AS date,
          SUM(ns.gross_spend_cents)/100.0 AS gross_spend,
          SUM(ns.net_spend_cents)/100.0 AS net_spend
        FROM net_spends ns
        LEFT JOIN click_url_histories AS cuh ON cuh.click_url_id = ns.click_url_id AND cuh.event_date = ns.spend_date
        WHERE spend_date = '#{date}' AND ns.click_url_id IN (#{click_url_ids.join(',')})
          AND cuh.direct_spend_input = true
        GROUP BY 1, 2
      SQL
    ).map{|r| [[r[0].to_i, r[1]], [r[2],r[3]]]}.to_h

    spends = (stat_records_res.keys + net_spends_res.keys).uniq.map do |key|
      stat_record_gross, stat_record_net = stat_records_res[key]
      direct_spend_gross, direct_spend_net = net_spends_res[key]

      {'click_url_id' => key[0], 'date' => key[1], 'gross_spend' => (stat_record_gross.to_f + direct_spend_gross.to_f), 'net_spend' => (stat_record_net.to_f + direct_spend_net.to_f) }
    end
    spends
  end

  def samsung_reports(date)
    SamsungApiService.new(date, date).fetch_content_metric(CONTENT_ID)
  end

  def get_data_from_appsflyer_agency_reports(date)
    sql = <<-SQL
        SELECT
          cuh.event_date,
          cuh.click_url_id,
          af_report.af_app_id,
          sum(af_report.install) as install,
          sum(af_event.purchase) as purchase
        FROM click_url_histories as cuh
        left join appsflyer_agency_reports as af_report on af_report.click_url_id = cuh.click_url_id and af_report.date = cuh.event_date
        left join appsflyer_in_app_events as af_event on af_event.click_url_id = cuh.click_url_id and af_event.date = cuh.event_date
        WHERE cuh.event_date = '#{date}'
        AND cuh.click_url_id IN (#{click_url_ids.join(',')})
        GROUP by 1, 2, 3
        HAVING SUM(af_report.install) > 0 OR SUM(af_event.purchase) > 0
        ;
      SQL
    AppsflyerAgencyReport.connection.execute(sql).to_a
  end

  def get_data_from_appsflyer_reports(date)
    sql = <<-SQL
        SELECT
          cuh.event_date,
          cuh.click_url_id,
          af_report.af_app_id,
          sum(af_report.impression) as impression,
          sum(af_report.click) as click,
          sum(af_report.install) as install,
          sum(af_event.purchase) as purchase
        FROM click_url_histories as cuh
        left join appsflyer_reports as af_report on af_report.click_url_id = cuh.click_url_id and af_report.date = cuh.event_date
        left join appsflyer_in_app_events as af_event on af_event.click_url_id = cuh.click_url_id and af_event.date = cuh.event_date
        WHERE cuh.event_date = '#{date}'
        AND cuh.click_url_id IN (#{click_url_ids.join(',')})
        GROUP by 1, 2, 3
        HAVING SUM(af_report.impression) > 0 OR SUM(af_report.click) > 0 OR SUM(af_report.install) > 0 OR SUM(af_event.purchase) > 0
        ;
      SQL
    AppsflyerReport.connection.execute(sql).to_a
  end

  def click_url_ids
    ClickUrl.includes(:campaign)
    .where(campaigns: {client_id: Client::ZIP_RECRUITER})
    .where.not(vendor_id: test_vendor_ids)
    .live
    .pluck(:id)
  end

  def test_vendor_ids
    @test_vendor_ids ||= Vendor.where(for_test: true).pluck(:id)
  end
end
