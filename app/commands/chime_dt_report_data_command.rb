class ChimeDtReportDataCommand
  prepend SimpleCommand

  include ActionView::Helpers::NumberHelper

  attr_reader :date

  CAMPAIGNS_IDS = [
    {
      fe_id: 1505,
      fe_name: "Chime_Android_US_CPI_TRACFONE",
      dt_id: 32972,
      dt_name: "Chime_Android_US_CPP_TRACFONE_AT&T",
      campaign_type: "Non Agency"
    },{
      fe_id: 2735,
      fe_name: "Chime_Android_US_CPI_Open_Market_RDNS_Agency",
      dt_id: 34838,
      dt_name: "Chime-Android-17171-US-DTOM-RDNS-Ignite-CPP",
      campaign_type: "Agency"
    }
  ].freeze

  HEADERS = [
    "Date",
    "CAMPAIGN",
    "INSTALL",
    "Client CPI",
    "Registrations",
    "CVR2 Install to Reg",
    "eCPE",
    "SPEND Gross",
    "CPP Bid",
    "Preloads From Digital Turbine",
    "Spend from Digital Turbine (NET)",
    "DT eCPI (NET CPI)",
    "DT eCPE (NET CPE)",
    "CVR Pre-load to install",
    "CVR2 Pre-load to Reg",
    "Profit/Loss",
    "Margin",
    "MONTH"
  ].freeze

  def initialize(date)
    @date = date
  end

  def call
    fe_datas = get_data_from_fe
    report_data = []
    CAMPAIGNS_IDS.each do |c|
      dt_record = get_data_from_dt(c)
      fe_record = fe_datas.find {|k| k["campaign_name"].to_s == c[:fe_name]}

      install = fe_record.present? ? fe_record["install_count"].to_i : 0
      registration = fe_record.present? ? fe_record["registration_count"].to_i : 0
      gross_spend = fe_record.present? ? fe_record["gross_spend"].to_f : 0.0
      reg = install > 0 ? number_to_percentage(registration * 100.0 / install, precision: 2) : "N/A"
      ecpe = registration > 0 ? number_to_currency((gross_spend / registration).round(2)) : "N/A"
      client_cpi = install > 0 ? number_to_currency((gross_spend / install).round(2)) : "N/A"

      dt_preload = dt_record.present? ? dt_record["Preloads"].to_i : 0
      dt_spend = dt_record.present? ? dt_record["Spend"].to_f : 0.0
      cpp_bid = dt_preload > 0 ? number_to_currency((dt_spend / dt_preload).round(2)) : 'N/A'
      dt_ecpi = install > 0 ? number_to_currency((dt_spend / install).round(2)) : "N/A"
      dt_ecpe = registration > 0 ? number_to_currency((dt_spend / registration).round(2)) : "N/A"
      cvr_preload = dt_preload > 0 ? number_to_percentage(install*100.0 / dt_preload, precision: 2) : "N/A"
      cvr2_preload = dt_preload > 0 ? number_to_percentage(registration*100.0 / dt_preload, precision: 2) : "N/A"
      profit_loss = gross_spend - dt_spend
      margin = gross_spend > 0 ? number_to_percentage(profit_loss*100.0 / gross_spend, precision: 2) : "N/A"

      report_data << HEADERS.zip([
        date, 
        c[:dt_name], 
        install, 
        client_cpi, 
        registration, 
        reg, 
        ecpe, 
        number_to_currency(gross_spend),
        cpp_bid, 
        dt_preload, 
        number_to_currency(dt_spend), 
        dt_ecpi, 
        dt_ecpe, 
        cvr_preload,
        cvr2_preload, 
        number_to_currency(profit_loss.round(2)), 
        margin, 
        date.to_date.strftime('%Y-%m')
      ]).to_h
    end
    report_data
  end

  def get_data_from_fe
    non_agency_campaigns = []
    agency_campaigns = []
    datas = []
    CAMPAIGNS_IDS.each do |k|
      if k[:campaign_type].to_s == 'Agency'
        agency_campaigns << k[:fe_id]
      else
        non_agency_campaigns << k[:fe_id]
      end
    end

    if agency_campaigns.present?
      datas += get_data_form_agency(agency_campaigns)
    end

    if non_agency_campaigns.present?
      datas += get_data_form_non_agency(non_agency_campaigns)
    end

    datas
  end

  def get_data_form_non_agency(campaign_ids)
    sql = <<-SQL
        SELECT
          campaign_id,
          campaign_name,
          sum(install_count) as install_count,
          sum(registration_count) as registration_count,
          sum(spend) as gross_spend
        FROM v4_campaigns_view
        WHERE campaign_id in (#{campaign_ids.join(',')}) and calculate_date = '#{date}' and status = 'normal'
        GROUP by 1,2;
      SQL
    ConversionRecordRedshift.connection.execute(sql).to_a
  end

  def get_data_form_agency(campaign_ids)
    sql = <<-SQL
        SELECT
          campaign_id,
          campaign_name,
          sum(install_count) as install_count,
          sum(registration_count) as registration_count,
          sum(spend) as gross_spend
        FROM v4_agency_campaigns_view
        WHERE campaign_id in (#{campaign_ids.join(',')}) and calculate_date = '#{date}' and status = 1
        GROUP by 1,2;
      SQL
    AggregatedStatsBase.connection.execute(sql).to_a
  end

  def get_data_from_dt(c)
    header = {"Content-Type"=>"application/json", "Authorization"=> "Basic #{Base64.encode64('<EMAIL>:FeedMob1!')}"}
    api_url = "https://via.digitalturbine.com/api/report/advertiser/v1/performance.json?startDate=#{date}&endDate=#{date}&campaignIds=#{c[:dt_id]}"

    res = Retriable.retriable(base_interval: 3) do
      HTTParty.get(api_url, headers: header)
    end
    return nil if res.blank? || res["performance"].blank?

    {
      "Preloads" => res["performance"]["preloadCount"],
      "Spend" => res["performance"]["spend"]
    }
  end
end
