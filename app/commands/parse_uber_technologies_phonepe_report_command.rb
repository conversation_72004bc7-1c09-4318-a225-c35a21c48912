# frezen_string_literal: true
require 'csv'

class ParseUberTechnologiesPhonepeReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'.freeze
  PHONEPE_QUERY_20147 = 'from:<EMAIL> newer_than:1d has:attachment filename:csv subject:PhonePe Campaign Report:OF-27260_Uber_CBC_CPC3 BC2404121538349397834342'.freeze
  PHONEPE_QUERY_20148 = 'from:<EMAIL> newer_than:1d has:attachment filename:csv subject:PhonePe Rewards Report: MOF2405021430346892584566'.freeze

  attr_reader :user_id, :date, :gmail, :click_url_id

  def initialize(date, click_url_id)
    @date =  if click_url_id == 20148
      date.to_date.strftime("%d %b, %Y")
    else
      date
    end

    @click_url_id = click_url_id
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    if click_url_id == 20147
      parse_email(PHONEPE_QUERY_20147)
    elsif click_url_id == 20148
      parse_email(PHONEPE_QUERY_20148)
    else
      errors.add(:invalid, "No subject")
      []
    end
  end

  private

  def parse_email(query)
    user_messages = gmail.list_user_messages(user_id, q: query)
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:invalid, "*No Email In Last One Day Received*")
      []
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    csv_data = attachment_content.data.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding("UTF-8"), '')
    data = CSV.parse(csv_data, headers: true).map(&:to_h)
    select_datas = data.select { |row| row['Date'] == date }
    if select_datas.present?
      select_datas
    else
      errors.add(:invalid, "No Data")
      []
    end
  end
end
