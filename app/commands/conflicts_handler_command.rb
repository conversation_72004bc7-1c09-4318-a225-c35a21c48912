# frozen_string_literal: true

class ConflictsHandlerCommand
  def call
    raise NotImplementedError
  end

  def table_infos(tables)
    tables.map do |table, _opt|
      table_info(table)
    end
  end

  def table_info(table)
    {
      table_name: table,
      project_name: Rails.application.class.module_parent_name,
      class_name: self.class.name
    }
  end

  def v2_table_infos(tables)
    tables.map do |table, action|
      v2_table_info(table, action)
    end
  end

  def v2_table_info(table, action = :read)
    {
      table_name: table,
      action: action,
      project_name: Rails.application.class.module_parent_name,
      class_name: self.class.name
    }
  end
end
