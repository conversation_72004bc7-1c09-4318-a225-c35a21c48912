# frozen_string_literal: true

class FetchHubspotAdopsNotesCommand
  prepend SimpleCommand

  include HubspotClient

  def call
    fetch_companies.each do |company|
      company_properties = company.properties
      tasks = fetch_tasks(company.id).results
      get_adops_notes(tasks).each do |task|
        adops_note = find_or_initialize_adops_note(
          hs_company_id: company.id, 
          company_name: company_properties["name"], 
          subject: task.subject
        )
        adops_note.update!(
          body: task.body,
          hs_object_id: task.hs_object_id,
          hs_owner_id: task.hs_owner_id,
          hs_created_at: task.hs_created_at,
          hs_updated_at: task.hs_updated_at
        )
      end
    end
  end

  private

    def fetch_companies
      hubspot_client.crm.companies.basic_api.get_all
    end

    def fetch_tasks(company_id)
      sleep rand(0.5..2.0) # sleep to avoid rate limit
      hubspot_client.crm.objects.tasks.search_api.do_search(body: {
        "limit": 100,
        "filterGroups":
          [
            {
              "filters":[
                {
                  "value": company_id,
                  "propertyName":"associations.company",
                  "operator":"EQ"
                },
                {
                  "value": "ADOPS NOTES",
                  "propertyName": "hs_task_subject",
                  "operator": "CONTAINS_TOKEN"
                }
              ]
            }
          ],
        "properties": ["hubspot_owner_id", "hs_task_subject", "hs_task_body", "hs_task_status"]
      })
    end

    def get_adops_notes(tasks)
      tasks.map do |task|
        properties = task.properties
        OpenStruct.new(
          hs_object_id: properties["hs_object_id"],
          subject: properties["hs_task_subject"],
          body: replace_image_urls(properties["hs_task_body"]),
          hs_owner_id: properties["hubspot_owner_id"],
          hs_created_at: task.created_at,
          hs_updated_at: task.updated_at
        )
      end
      .group_by(&:subject)
      .transform_values do |tasks_for_subject|
        tasks_for_subject.max_by(&:hs_updated_at)
      end
      .values
    end

    def replace_image_urls(body)
      return if body.blank?

      doc = Nokogiri::HTML(body)
      doc.css("img").each do |img|
        command = SaveHubspotImageToS3Command.call(img["src"])
        if command.failure?
          Rails.logger.error("Failed to save image to S3: #{command.errors}")
          next
        end

        img["src"] = command.result
      end

      doc.to_html
    end

    def find_or_initialize_adops_note(hs_company_id:, company_name:, subject:)
      HubspotAdopsNote.find_or_initialize_by(hs_company_id: hs_company_id, company_name: company_name, subject: subject)
    end
end
