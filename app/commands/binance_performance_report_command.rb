# frezen_string_literal: true

require 'googleauth'
require 'google/apis/sheets_v4'

class BinancePerformanceReportCommand
  include ReportConfigConcern
  include FileUtils
  include ActionView::Helpers::NumberHelper

  AF_APP_IDS = ['com.binance.dev', 'id1436799971']
  TAB_NAME = 'Campaign Performance'

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1QkDTYHDLmBFeCedBccK1k2yEY8-lDUmNWCgWMmMydVw'.freeze

  attr_reader :start_date, :end_date, :date_range

  def initialize(start_date, end_date)
    @start_date = start_date.to_date
    @end_date = end_date.to_date
    @date_range = "#{@start_date.strftime('%m/%d')}-#{@end_date.strftime('%m/%d')}"
  end

  def call
    @service = Google::Apis::SheetsV4::SheetsService.new
    @service.authorization = authorizer
    response = @service.get_spreadsheet_values(SPREADSHEET_ID, "#{TAB_NAME}!A:A")
    first_columns = response.values
    row_index = first_columns.find_index{|row| row.first == date_range}
    if row_index.nil?
      create_new_date_range_data
    else
      update_date_range_data(first_columns)
    end
  end

  def create_new_date_range_data
    sheet_id = @service.get_spreadsheet(SPREADSHEET_ID).sheets.find{|sheet| sheet.properties.title == TAB_NAME}&.properties&.sheet_id
    new_rows_needed = performance_data.size + 5
    insert_new_rows(sheet_id, 0, new_rows_needed)

    @service.update_spreadsheet_value(SPREADSHEET_ID, "#{TAB_NAME}!A1", { values: [[date_range]] }, value_input_option: 'USER_ENTERED')

    first_row_number = 2
    values = []
    values << headers
    last_row_number = first_row_number
    performance_data.each_with_index do |record, index|
      last_row_number += 1
      values << [
        record[:vendor_name],
        record[:campaign_name],
        record[:click],
        record[:install],
        "=IFERROR(D#{last_row_number}/C#{last_row_number}, \"-\")",
        record[:spend],
        "=IFERROR(F#{last_row_number}/D#{last_row_number}, \"-\")",
        record[:registration],
        "=IFERROR(H#{last_row_number}/C#{last_row_number}, \"-\")",
        "=IFERROR(H#{last_row_number}/D#{last_row_number}, \"-\")",
        record[:first_time_deposit],
        "=IFERROR(K#{last_row_number}/D#{last_row_number}, \"-\")",
        "=IFERROR(K#{last_row_number}/H#{last_row_number}, \"-\")",
        "=IFERROR(F#{last_row_number}/K#{last_row_number}, \"-\")",
        record[:first_trade],
        "=IFERROR(O#{last_row_number}/D#{last_row_number}, \"-\")",
        "=IFERROR(O#{last_row_number}/H#{last_row_number}, \"-\")",
        "=IFERROR(F#{last_row_number}/O#{last_row_number}, \"-\")",
        record[:revenue],
        "=IFERROR(S#{last_row_number}/F#{last_row_number}, \"-\")"
      ]
    end
    values << total_row(first_row_number + 1, last_row_number)
    value_range_object = {
      major_dimension: "ROWS",
      values: values
    }
    @service.update_spreadsheet_value(SPREADSHEET_ID, "#{TAB_NAME}!A#{first_row_number}:T#{last_row_number+1}", value_range_object, value_input_option: 'USER_ENTERED')
  end

  def update_date_range_data(first_columns)
    start_row_index = first_columns.find_index{|row| row.first == date_range}
    row_count = get_row_count(start_row_index + 1, first_columns)

    if performance_data.size != row_count
      send_report_config_job_slack_alert("#{self.class.name}: 更新的行数和原来的行数不一致，需要人工更新#{TAB_NAME} #{start_date} ~ #{end_date}", :mighty)
      return
    end
    values = []
    row_number = start_row_index + 2
    performance_data.each_with_index do |record, index|
      updated_row_number = row_number + index + 1
      values << [
        record[:vendor_name],
        record[:campaign_name],
        record[:click],
        record[:install],
        "=IFERROR(D#{updated_row_number}/C#{updated_row_number}, \"-\")",
        record[:spend],
        "=IFERROR(F#{updated_row_number}/D#{updated_row_number}, \"-\")",
        record[:registration],
        "=IFERROR(H#{updated_row_number}/C#{updated_row_number}, \"-\")",
        "=IFERROR(H#{updated_row_number}/D#{updated_row_number}, \"-\")",
        record[:first_time_deposit],
        "=IFERROR(K#{updated_row_number}/D#{updated_row_number}, \"-\")",
        "=IFERROR(K#{updated_row_number}/H#{updated_row_number}, \"-\")",
        "=IFERROR(F#{updated_row_number}/K#{updated_row_number}, \"-\")",
        record[:first_trade],
        "=IFERROR(O#{updated_row_number}/D#{updated_row_number}, \"-\")",
        "=IFERROR(O#{updated_row_number}/H#{updated_row_number}, \"-\")",
        "=IFERROR(F#{updated_row_number}/O#{updated_row_number}, \"-\")",
        record[:revenue],
        "=IFERROR(S#{updated_row_number}/F#{updated_row_number}, \"-\")"
      ]
    end
    value_range_object = {
      major_dimension: "ROWS",
      values: values
    }
    @service.update_spreadsheet_value(SPREADSHEET_ID, "#{TAB_NAME}!A#{row_number+1}:T#{row_number+row_count}", value_range_object, value_input_option: 'USER_ENTERED')
  end

  private

  def performance_data
    click_install_records = get_click_install
    spend_records = get_spend
    event_revenue_records = get_event_revenue
    records = click_install_records.map do |row|
      {
        vendor_name: row[0],
        campaign_name: row[1],
        click: row[2],
        install: row[3],
        spend: spend_records[[row[0], row[1]]],
        registration: event_revenue_records[[row[0], row[1]]] ? event_revenue_records[[row[0], row[1]]]['registration_verified'] : '',
        first_time_deposit: event_revenue_records[[row[0], row[1]]] ? event_revenue_records[[row[0], row[1]]]['af_first_deposit'] : '',
        first_trade: event_revenue_records[[row[0], row[1]]] ? event_revenue_records[[row[0], row[1]]]['af_first_trade'] : '',
        revenue: event_revenue_records[[row[0], row[1]]] ? event_revenue_records[[row[0], row[1]]][:revenue] : '',
      }
    end
    
    records.reject do |record|
      all_zeros = true
      
      numeric_fields = [:click, :install, :spend, :registration, :first_time_deposit, :first_trade, :revenue]
      numeric_fields.each do |field|
        value = record[field]
        value = 0 if value.is_a?(String) && value.strip.empty?
        if value.to_f > 0
          all_zeros = false
          break
        end
      end
      
      all_zeros
    end
  end

  def get_click_install
    sql = <<-SQL
      SELECT * FROM (
        SELECT v.vendor_name, c.name, sum(click) as clicks, sum(install) as installs
        FROM appsflyer_agency_reports aar
        LEFT JOIN click_urls cu ON cu.id = aar.click_url_id
        LEFT JOIN campaigns c ON c.id = cu.campaign_id
        LEFT JOIN vendors v ON v.id = cu.vendor_id
        WHERE aar.af_app_id IN ('com.binance.dev', 'id1436799971')
          AND date >= '#{start_date}' AND date <= '#{end_date}'
        GROUP BY  1,2

        UNION

        SELECT v.vendor_name, c.name, sum(click) as clicks, sum(install) as installs
        FROM appsflyer_non_agency_reports aar
        LEFT JOIN click_urls cu ON cu.id = aar.click_url_id
        LEFT JOIN campaigns c ON c.id = cu.campaign_id
        LEFT JOIN vendors v ON v.id = cu.vendor_id
        WHERE aar.af_app_id IN ('com.binance.dev', 'id1436799971')
          AND date >= '#{start_date}' AND date <= '#{end_date}'
        GROUP BY  1,2
      ) t ORDER BY 1,2
    SQL
    AppsflyerAgencyReport.connection.query(sql)
  end

  def get_event_revenue
    vendor_campaign_name_lookups = get_vendor_campaign_name
    events = {}
    AF_APP_IDS.each do |af_app_id|
      (start_date..end_date).each do |date|
        service = AppsflyerApiServiceV3.new(af_app_id: af_app_id, client_id: Client::BINANCE_ID, start_date: date, end_date: date)
        records = service.in_app_events_report
        records.each do |record|
          record = record.to_h
          click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
          vendor_name, campaign_name = vendor_campaign_name_lookups[click_url_id.to_i]
          event_name = record['Event Name']&.downcase
          events[[vendor_name, campaign_name]] ||= {revenue: 0}
          events[[vendor_name, campaign_name]][event_name] ||= 0
          events[[vendor_name, campaign_name]][event_name] += 1
          events[[vendor_name, campaign_name]][:revenue] += record['Event Revenue'].to_f
        end
      end
    end
    events
  end

  def get_spend
    campaign_ids = Campaign.where(client_id: Client::BINANCE_ID).pluck(:id)
    ConversionRecordRedshift.connection.query(
      <<-SQL
        SELECT vendor_name, campaign_name, sum(spend) as gross
        FROM v4_campaigns_view
        WHERE calculate_date >= '#{start_date}' AND calculate_date <= '#{end_date}'
          AND status IN ('normal', 'injected')
          AND campaign_id IN (#{campaign_ids.join(', ')})
        GROUP BY 1,2
        ORDER BY 1,2
      SQL
    ).map{|r| [[r[0], r[1]], r[2]]}.to_h
  end

  def insert_new_rows(sheet_id, new_row_start_index, new_row_end_index)
    insert_dimension_request = Google::Apis::SheetsV4::InsertDimensionRequest.new(
      range: Google::Apis::SheetsV4::DimensionRange.new(
        sheet_id: sheet_id,
        dimension: 'ROWS',
        start_index: new_row_start_index,
        end_index: new_row_end_index
      ),
      inherit_from_before: false
    )

    batch_update_request = Google::Apis::SheetsV4::BatchUpdateSpreadsheetRequest.new(
      requests: [Google::Apis::SheetsV4::Request.new(insert_dimension: insert_dimension_request)]
    )
    response = @service.batch_update_spreadsheet(SPREADSHEET_ID, batch_update_request)
  end

  def get_row_count(row_index, first_columns)
    row_count = 0
    first_columns[row_index+1..-1].each_with_index do |row, index|
      break if row.first&.strip == 'Total'
      row_count += 1
    end
    row_count
  end

  def authorizer
    @authorizer ||= begin
      authorizer = Google::Auth::ServiceAccountCredentials.make_creds(
        json_key_io: File.open(client_secrets_file_path),
        scope: SCOPE
      )
      authorizer.fetch_access_token!
      authorizer
    end
  end

  def client_secrets_file_path
    File.join(current_path, "config", 'feedmob-4bbd49e741a5.json')
  end

  def current_path
    Dir["#{Rails.root}"]
  end

  def headers
    ['PARTNER', 'CAMPAIGN', 'CLICKS', 'INSTALLS', 'CTI', 'SPEND', 'CPI', 'Registration', 'CLICK TO Registration', 'INSTALL TO Registration', 'first_time_deposit', 'INSTALL TO first_time_deposit', 'Registration TO first_time_deposit', 'first_order_create CPA', 'first_trade', 'INSTALL TO first_trade', 'Reg TO first_trade', 'first_trade CPA', 'Revenue', 'ROAS']
  end

  def total_row(first_row_number, last_row_number)
    total_row = ['Total', '']
    ['C', 'D'].each do |col|
      total_row << "=SUM(#{col}#{first_row_number}:#{col}#{last_row_number})"
    end
    total_row << "=iferror(D#{last_row_number+1}/C#{last_row_number+1}, \"-\")"
    total_row << "=SUM(F#{first_row_number}:F#{last_row_number})"
    total_row << "=iferror(F#{last_row_number+1}/D#{last_row_number+1}, \"-\")"
    total_row << "=SUM(H#{first_row_number}:H#{last_row_number})"
    total_row << "=iferror(H#{last_row_number+1}/C#{last_row_number+1}, \"-\")"
    total_row << "=iferror(H#{last_row_number+1}/D#{last_row_number+1}, \"-\")"
    total_row << "=SUM(K#{first_row_number}:K#{last_row_number})"
    total_row << "=iferror(K#{last_row_number+1}/D#{last_row_number+1}, \"-\")"
    total_row << "=iferror(K#{last_row_number+1}/H#{last_row_number+1}, \"-\")"
    total_row << "=iferror(F#{last_row_number+1}/K#{last_row_number+1}, \"-\")"
    total_row << "=SUM(O#{first_row_number}:O#{last_row_number})"
    total_row << "=iferror(O#{last_row_number+1}/D#{last_row_number+1}, \"-\")"
    total_row << "=iferror(O#{last_row_number+1}/H#{last_row_number+1}, \"-\")"
    total_row << "=iferror(F#{last_row_number+1}/O#{last_row_number+1}, \"-\")"
    total_row << "=SUM(S#{first_row_number}:S#{last_row_number})"
    total_row << "=iferror(S#{last_row_number+1}/F#{last_row_number+1}, \"-\")"
    total_row
  end

  def get_vendor_campaign_name
    Rails.cache.fetch("binance_vendor_campaign_names", expires_in: 5.minutes) do
      ClickUrl.connection.query(
        <<-SQL
          SELECT cu.id, v.vendor_name, c.name
          FROM click_urls cu
          LEFT JOIN campaigns c ON cu.campaign_id = c.id
          LEFT JOIN vendors v ON cu.vendor_id = v.id
          WHERE c.client_id = #{Client::BINANCE_ID}
            AND cu.status IN (4, 99, 100)
        SQL
      ).map{|r| [r[0], [r[1], r[2]]]}.to_h
    end
  end
end
