# frezen_string_literal: true
class FetchKochavaReportBaseCommand
  prepend SimpleCommand

  APP_API_HOST = 'https://apps.api.kochava.com'.freeze
  REPORT_API_HOST = 'https://reporting.api.kochava.com'.freeze
  API_KEY = 'A7CC91DD-851F-7E79-87B5-DBCF4443BABA'.freeze

  def fetch_token
    @summary_token = create_kochava_summary_job
    @event_token = create_kochava_events_job
    [summary_token, event_token]
  end

  def fetch_kochava_app
    res = Retriable.retriable(base_interval: 3) do
      HTTParty.get(
        "#{APP_API_HOST}/apps/list?limit=0",
        headers: {'Authentication-Key'=> API_KEY})
    end

    res['apps']
  end

  def create_kochava_summary_job
    body = {
      api_key: API_KEY,
      app_guid: mapping.app_guid,
      time_start: time_start.to_s,
      time_end: time_end.to_s,
      traffic: [
        "impression","click","install"
      ],
      traffic_filtering: {},
      traffic_grouping: [
        "network", "campaign"
      ],
      time_series: "24",
      time_zone: "UTC",
      delivery_method: [
        "S3link"
      ],
      delivery_format: "csv",
      columns_order: [
        "default"
      ]
    }
    body[:traffic_filtering][:network] = [@network] if @network.present?

    res = HTTParty.post(
      "#{REPORT_API_HOST}/v1.4/summary",
      body: body.to_json,
      headers: { 'Content-Type' => 'application/json' }
    )

    res.dig('report_token')
  end

  def create_kochava_events_job
    body = {
      api_key: API_KEY,
      app_guid: mapping.app_guid,
      time_start: time_start.to_s,
      time_end: time_end.to_s,
      traffic: [
        "event"
      ],
      traffic_filtering: {
        event_name: mapping.events_in_json.values.flatten.uniq
      },
      time_zone: "UTC",
      delivery_method: [
        "S3link"
      ],
      delivery_format: "csv",
      columns_order: [
        "default"
      ]
    }
    body[:traffic_filtering][:network] = [@network] if @network.present?

    res = HTTParty.post(
      "#{REPORT_API_HOST}/v1.4/detail",
      body: body.to_json,
      headers: { 'Content-Type' => 'application/json' }
    )
    res.dig('report_token')
  end

  def wait_job_finished(token)
    return if token.blank?

    res = HTTParty.post(
      "#{REPORT_API_HOST}/v1.4/progress",
      body: { api_key: API_KEY, app_guid: mapping.app_guid, token: token }.to_json,
      headers: { 'Content-Type' => 'application/json' }
    )
    res['report']
  end

  def send_star_slack(text)
    SlackService.send_notification_to_star text
  end

  def download_csv(url)
    return if url.blank?
    res = HTTParty.get(url)
    CSV.parse(res.body, headers: true)
  end

  def mappings
    @mappings ||= KochavaEventMapping.all.to_a
  end
end
