class ParticipantsCommand
  prepend SimpleCommand

  attr_reader :title

  def initialize(ticket_title)
    @title = ticket_title.to_s.gsub(/\s/, '')
  end

  def call
    {
      d: Array(participants[:d]),
      c: <PERSON>rray(participants[:c]),
      p: <PERSON><PERSON><PERSON>(participants[:p]),
      q: <PERSON><PERSON><PERSON>(participants[:q]),
      v: <PERSON><PERSON><PERSON>(participants[:v]),
    }
  end

  private

  def participants
    @participants ||= begin
      title =~ /^.+\[([a-zA-Z|:]+)\]$/

      marks_array = Regexp.last_match(1)&.split('|')

      Array(marks_array).each_with_object({}) do |item, hash|
        mark, name = item.split(':')
        next if mark.blank? || name.blank?

        mark_result(hash, mark.downcase.to_sym, name.capitalize)
      end
    end
  end

  def mark_result(hash, mark, name)
    name_array = hash.fetch(mark, [])
    name_array << name
    hash[mark] = name_array.uniq
  end
end
