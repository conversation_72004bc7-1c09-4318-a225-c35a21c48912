# frezen_string_literal: true
require 'csv'

class ParseUpsideMatchgroupReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'.freeze
  EMAIL_QUERY = 'has:attachment filename:csv subject:Upside_US_AprMay2025_Direct_Daily_Report'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    start_date = (date.to_date + 1.days).strftime("%Y/%m/%d")
    end_date = (date.to_date + 2.days).strftime("%Y/%m/%d")

    user_messages = gmail.list_user_messages(user_id, q: "#{EMAIL_QUERY} after:#{start_date} before:#{end_date}")
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:invalid, "*No Email In Last One Day Received*")
      {ios: [0,0,0], android: [0,0,0]}
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    csv_data = attachment_content.data.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding("UTF-8"), '')
    data = CSV.parse(csv_data)
    select_data = data.select { |row| row[0] == date.to_date.strftime('%-m/%-d/%y') || row[0] == date.to_date.strftime('%-m/%d/%y') }
    if select_data.present?
      android_data = select_data.select {|c|c[3].downcase.include?('android') }
      ios_data = select_data.select {|c|c[3].downcase.include?('ios') }
      {ios: [ios_data.sum{|c| c[7].gsub(',', '').to_i},ios_data.sum{|c| c[8].gsub(',', '').to_i},ios_data.sum{|c| c[10].gsub(',', '').to_f}], android: [android_data.sum{|c| c[7].gsub(',', '').to_i},android_data.sum{|c| c[8].gsub(',', '').to_i},android_data.sum{|c| c[10].gsub(',', '').to_f}]}
    else
      errors.add(:invalid, "No Data")
      {ios: [0,0,0], android: [0,0,0]}
    end
  end
end
