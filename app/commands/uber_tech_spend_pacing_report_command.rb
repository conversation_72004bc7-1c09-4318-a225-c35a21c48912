class UberTechSpendPacingReportCommand
  prepend SimpleCommand

  OPTIONS = [
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'India', country_code: 'IN', click_url_ids: [19710] },
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'Spain', country_code: 'ES', click_url_ids: [20560] },
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'United Kingdom', country_code: 'GB', click_url_ids: [20374] },
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'Brazil', country_code: 'BR', click_url_ids: [20566, 21515] },
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'Argentina', country_code: 'AR', click_url_ids: [20561] },
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'Mexico', country_code: 'MX', click_url_ids: [20557] },
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'Canada', country_code: 'CA', click_url_ids: [20331] },
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'Australia', country_code: 'AU', click_url_ids: [21612] },
    { partner: 'InMobi', device: 'Android', lob: 'Uber Rider', country: 'South Korea', country_code: 'SK', click_url_ids: [21643] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'India', country_code: 'IN', click_url_ids: [19836] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'Spain', country_code: 'ES', click_url_ids: [20102] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'United Kingdom', country_code: 'GB', click_url_ids: [19837] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'Brazil', country_code: 'BR', click_url_ids: [20103] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'Argentina', country_code: 'AR', click_url_ids: [19964] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'Mexico', country_code: 'MX', click_url_ids: [20624] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'Canada', country_code: 'CA', click_url_ids: [20104] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'France', country_code: 'FR', click_url_ids: [20100] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'Australia', country_code: 'AU', click_url_ids: [21611] },
    { partner: 'Jampp', device: 'Android', lob: 'Uber Rider', country: 'South Korea', country_code: 'SK', click_url_ids: [21642] },
    { partner: 'Xapads', device: 'Android', lob: 'Uber Rider', country: 'India', country_code: 'IN', click_url_ids: [19775] },
    { partner: 'Xapads', device: 'Android', lob: 'Uber Rider', country: 'Germany', country_code: 'DE', click_url_ids: [20548] },
    { partner: 'Xapads', device: 'Android', lob: 'Uber Rider', country: 'United Kingdom', country_code: 'GB', click_url_ids: [20547] },
    { partner: 'Xapads', device: 'Android', lob: 'Uber Rider', country: 'France', country_code: 'FR', click_url_ids: [20546] },
  ].freeze

  HEADERS = [
    'Partner',
    'Device',
    'LOB',
    'Country',
    'Country Code',
    'Click Url',
    'Total Spend'
  ].freeze

  attr_reader :start_date, :end_date

  def initialize(end_date)
    @start_date = end_date.to_date.beginning_of_month
    @end_date = end_date.to_date
  end

  def call
    CSV.generate(write_headers: true, headers: HEADERS) do |csv|
      generated_data.each do |row|
        csv << row
      end
    end
  end

  def generate_file_name
    "uber_tech_spend_pacing_report_#{start_date}_#{end_date}.csv"
  end

  def generate_subject
    "Uber Tech Spend Pacing Report #{start_date.strftime("%Y/%m/%d")} - #{end_date.strftime("%Y/%m/%d")}"
  end

  def generate_content
    @email_content ||= <<~HTML
      Please find the attached file

      Thanks!
    HTML
  end

  def generated_data
    OPTIONS.map do |option|
      build_row(option)
    end
  end

  private

  def build_row(option)
    [
      option[:partner],
      option[:device],
      option[:lob],
      option[:country],
      option[:country_code],
      build_click_url_links(option[:click_url_ids]),
      fetch_total_spend(option[:click_url_ids])
    ]
  end

  def build_click_url_links(click_url_ids)
    click_url_ids.map { |id| "https://admin.feedmob.com/click_urls/#{id}" }.join(', ')
  end

  def fetch_total_spend(click_url_ids)
    ClickUrl.where(id: click_url_ids).inject(0.0) { |sum, click_url| sum + get_gross_spend(click_url) }
  end

  def get_gross_spend(click_url)
    vendor_id = click_url.vendor_id
    campaign_id = click_url.campaign_id

    if click_url.fm_link?
      result = ConversionRecordRedshift.connection.execute(fe_spend_query(vendor_id, campaign_id)).first
      result ? result.values.first.to_f.round(2) : 0.0
    else
      result = NetSpend.connection.execute(direct_spend_query(vendor_id, campaign_id)).first
      result ? result.values.first.to_f.round(2) : 0.0
    end
  end

  def direct_spend_query(vendor_id, campaign_id)
    <<-SQL
      select
        sum(gross_spend_cents / 100.0) as gross_spend
      from net_spends
      where campaign_id = #{campaign_id}
      and vendor_id = #{vendor_id}
      and spend_date between '#{start_date}' and '#{end_date}'
    SQL
  end

  def fe_spend_query(vendor_id, campaign_id)
    <<-SQL
      select
        sum(spend) as gross_spend
      from v4_campaigns_view
      where campaign_id = #{campaign_id}
      and vendor_id = #{vendor_id}
      and status in ('normal', 'injected', 'over_cap', 'manual_stopped')
      and calculate_date between '#{start_date}' and '#{end_date}'
    SQL
  end
end
