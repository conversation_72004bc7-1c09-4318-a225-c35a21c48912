# frozen_string_literal: true


class NotifyLargeDirectSpendEntryCommand
  prepend SimpleCommand

  def initialize(date = Date.yesterday)
    @date = date
  end

  def call
    notify!(
      bulk_entries: get_bulk_direct_spend_entries,
      acient_entries: get_ancient_direct_spend_entries
    )
  end

  private

    attr_reader :date

    THRESHOLD = 14

    # Email to Slack channel `direct-spend-input-alert`
    # https://join.slack.com/share/enQtNzUyNjg3MTI0MTA1OS0wYTgzMmJhOGYxNzIwMWQ4NDExYmJjMGI0OTRlNjZiZTVjMjM2NDM0OGY5MmJjZTIwZTRiOTg0YzJmZWJjN2Q0
    EMAIL_RECIPIENT = "<EMAIL>".freeze

    def get_bulk_direct_spend_entries
      sql = ActiveRecord::Base.sanitize_sql([<<~SQL, date: date, threshold: THRESHOLD])
        select
          net_spends.click_url_id, count(0) as updated_count
        from net_spends
        left join campaigns on net_spends.campaign_id = campaigns.id
        left join clients on clients.id = campaigns.client_id
        left join vendors on net_spends.vendor_id = vendors.id
        where DATE(net_spends.updated_at) = :date and net_spends.direct_spend_job_id is null
          and campaigns.name NOT ILIKE '%test%' and vendors.vendor_name NOT ILIKE '%test%' and clients.name NOT ILIKE '%test%'
        group by 1
        having count(0) >= :threshold
      SQL
      NetSpend.connection.execute(sql).to_a
    end

    def get_ancient_direct_spend_entries
      sql = ActiveRecord::Base.sanitize_sql([<<~SQL, date: date, threshold: THRESHOLD])
        select
          net_spends.click_url_id, count(0) as updated_count
        from net_spends
        left join campaigns on net_spends.campaign_id = campaigns.id
        left join clients on clients.id = campaigns.client_id
        left join vendors on net_spends.vendor_id = vendors.id
        where DATE(net_spends.updated_at) = :date and net_spends.direct_spend_job_id is null and DATE(net_spends.updated_at) - DATE(net_spends.spend_date) >= :threshold
          and campaigns.name NOT ILIKE '%test%' and vendors.vendor_name NOT ILIKE '%test%' and clients.name NOT ILIKE '%test%'
        group by 1
      SQL
      NetSpend.connection.execute(sql).to_a
    end

    def notify!(bulk_entries:, acient_entries:)
      if bulk_entries.blank? && acient_entries.blank?
        Rails.logger.info("No large direct spend entry found on #{date}")
        return
      end

      NotificationMailer.notify_large_direct_spend_entry(
        subject: "#{date} - Large Direct Spend Entry Alert",
        recipients: [EMAIL_RECIPIENT],
        bulk_entries: bulk_entries,
        acient_entries: acient_entries
      ).deliver_now
    end
end
