# frezen_string_literal: true
require 'csv'
require 'zip'

class ParseChimeAdMarketplaceReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'.freeze
  ADMARKETPLACE_QUERY = {
    '20224': 'from:<EMAIL> has:attachment filename:csv subject:Chime_Mobile_Web_report'
  }.with_indifferent_access.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    result = {}
    ADMARKETPLACE_QUERY.each do |key, query|
      q = parse_query(query)
      success, error_msg, data = parse_email(q)
      result[key] = {
        success: success,
        error_msg: error_msg,
        data: data
      }
    end
    result
  end

  def parse_query(q)
    "#{q} #{date.to_s}"
  end

  private

  def parse_email(query)
    user_messages = gmail.list_user_messages(user_id, q: query)
    message = Array(user_messages.messages).select do |m|
      email = gmail.get_user_message user_id, m.id
      email.snippet.include?('adMarketplace Report')
    end.first
    if message.blank?
      return [false, "*No Email In Last One Day Received*", []]
    end
    analyze_email(gmail: gmail, user_id: user_id, message: message)
  rescue => e
    [false, e, []]
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    select_datas = []

    data = CSV.parse(attachment_content.data, headers: true).map(&:to_h)
    select_datas = data.select { |row| row['Date'] == date }

    if select_datas.blank?
      return [false, "No Data", []]
    end

    [true, nil, select_datas]
  rescue => e
    [false, e, []]
  end
end
