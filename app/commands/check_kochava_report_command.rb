# frezen_string_literal: true
class CheckKochavaReportCommand < FetchKochavaReportBaseCommand
  prepend SimpleCommand

  attr_reader :time_start, :time_end, :start_date, :end_date, :summary_token, :event_token, :mapping

  def initialize(start_date: (Date.yesterday - 1.day).to_s, end_date: (Date.yesterday - 1.day).to_s, summary_token: nil, event_token: nil, mapping: nil)
    @start_date = start_date
    @end_date = end_date
    @end_date = end_date
    @summary_token = summary_token
    @event_token = event_token
    @time_start = start_date.to_date.beginning_of_day.to_i
    @time_end = end_date.to_date.end_of_day.to_i
    @mapping = mapping
  end

  def call
    summary_s3_url = wait_job_finished(summary_token)
    summary_records = download_csv(summary_s3_url)

    event_s3_url = wait_job_finished(event_token)
    events_records = download_csv(event_s3_url)

    events_records = events_records.blank? ? [] : events_records
    summary_records = summary_records.blank? ? {} : summary_records

    if (summary_records.nil? || events_records.nil?)
      send_star_slack("#{self.class}: date #{date} faild to create report, please check!")
      return
    end

    all_events = {
      impression: summary_records.sum{|c|c['impression_count'].to_i},
      click: summary_records.sum{|c|c['click_count'].to_i},
      install: summary_records.sum{|c|c['install_count'].to_i}
    }
    mapping.events_in_json.each do |feedmob_event, kochava_events|
      all_events[feedmob_event.to_sym] = events_records.select { |c| kochava_events.include?(c['event_name']) }.size
    end

    click_url_rows = check_by_day(all_events)

    network_summary_group = summary_records.group_by {|record| record['network'] }
    network_event_group = events_records.group_by {|record| record['attribution_network_id'] }
    networks = (network_summary_group.keys + network_event_group.keys).uniq

    events = []
    networks.each do |network|
      network_summary = network_summary_group[network].to_a
      network_event = network_event_group[network].to_a

      event = {
        network: network,
        impression: network_summary.sum{|c|c['impression_count'].to_i},
        click: network_summary.sum{|c|c['click_count'].to_i},
        install: network_summary.sum{|c|c['install_count'].to_i}
      }
      mapping.events_in_json.each do |feedmob_event, kochava_events|
        event[feedmob_event.to_sym] = network_event.select { |c| kochava_events.include?(c['event_name']) }.size
      end

      events << event
    end


    rows = []
    events.each do |event|
      records = KochavaAgencyReport.where(date: start_date..end_date, app_guid: mapping.app_guid, network: event[:network])

      db_data_counts = {network: event[:network]}
      event.keys.each do |k|
        next if k == :network
        db_data_counts[:"#{k}"] = records.sum{|c|c.send("#{k}")}
      end

      next if event == db_data_counts      
      diff_hash = {network: event[:network], app_guid: mapping.app_guid, start_date: start_date, end_date: end_date}
      event.keys.each do |k|
        next if k.in?([:network, :app_guid])
        diff_hash[:"#{k}"] = "#{event[:"#{k}"]}/#{db_data_counts[:"#{k}"]}"
      end

      # check diff_hash keys, if only click is different, ignore it
      if diff_hash.except(:network, :app_guid, :start_date, :end_date).keys == [:click]
        if (diff_hash[:click].split('/').first.to_i - diff_hash[:click].split('/').last.to_i).abs == 1
          next
        end
      end

      rows << diff_hash
    end

    [rows, click_url_rows]
  end

  def check_by_day(events)
    record = KochavaAgencyReport.find_by(date: start_date..end_date, app_guid: mapping.app_guid)
    return {} if record.nil? || events.reject { |_, v| v.zero? }.empty?

    click_url = record.first&.click_url
    row = {
      click_url_id: click_url&.id,
      campaign: click_url&.campaign&.name,
      vendor: click_url&.vendor&.vendor_name,
      passed: true
    }
    events.each do |event, v|
      db_value = record.nil? ? 0 : record.map{|r| r[event]}.sum
      row["db_#{event}".to_sym] = db_value
      row[event] = v
      allow_diff = (click_url&.client_paid_action&.include?(event.to_s) || click_url&.vendor_paid_action&.include?(event.to_s))  ? 0 : 1
      row[:passed] = false if (db_value - v).abs > allow_diff
    end
    row[:passed] ? nil : row
  end
end
