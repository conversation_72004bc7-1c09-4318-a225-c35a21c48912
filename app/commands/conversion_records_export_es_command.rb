# frozen_string_literal: true

require 'csv'

class ConversionRecordsExportEsCommand
  include ActionView::Helpers::<PERSON><PERSON>elper
  prepend SimpleCommand

  attr_reader :ops, :start_date, :end_date, :the24metrics_conversions, :loop_limitation

  HEADERS_MAPPING = {
    identifier: 'identifier',
    feedmob_install_at: 'Feedmob_Install_At',
    conversion_id: 'Conversion ID',
    click_url_id: 'Click Url ID',
    vendor_name: 'Vendor Name',
    event_time: 'Feedmob Event Time',
    install_time: 'Feedmob Install Time',
    click_event_time: 'Feedmob Click Time',
    status: 'Status',
    the24metrics_reason: 'The24metrics Reason',
    track_type: 'TrackType',
    action_name: 'Action Name',
    campaign_name: 'Campaign',
    creative: 'Creative',
    track_party: 'Track Party',
    click_device_platform_id: 'Click Device Platform ID',
    device_platform_id: 'Postback Device Platform ID',
    app_id: 'App ID',
    ourl: 'Ourl',
    target_url: 'Target Url',
    purchase_amt: 'Purchase Amt',
    app_name: 'App Name',
    duration: 'Duration(s)',
    remote_ip: 'Remote IP',
    mmp_city: 'MMP City',
    city: 'City',
    click_ourl: 'Click Ourl',
    click_target_url: 'Click Target Url',
    mmp_click_time: 'MMP Click Time',
    mmp_install_time: 'MMP Install Time',
    mmp_event_time: 'MMP Event Time',
    sub_status: 'Sub Status',
    response_code: 'Vendor Response Code',
    postback_time: 'Postback Time',
    repeated: 'Repeated'
  }.freeze

  HEADER_GROUPS = {
    'Campaign Details' => {
      app_id: 'App ID',
      app_name: 'App Name',
      campaign_name: 'Campaign',
      click_url_id: 'Click Url ID',
      conversion_id: 'Conversion ID',
      creative: 'Creative',
      track_party: 'Track Party',
      vendor_name: 'Vendor Name',
    },
    'Conversion Details' => {
      action_name: 'Action Name',
      click_device_platform_id: 'Click Device Platform ID',
      mmp_city: 'MMP City',
      city: 'City',
      device_platform_id: 'Postback Device Platform ID',
      purchase_amt: 'Purchase Amt',
      remote_ip: 'Remote IP',
      status: 'Status',
      the24metrics_reason: 'The24metrics Reason',
      sub_status: 'Sub Status',
      track_type: 'TrackType',
      response_code: 'Vendor Response Code',
      repeated: 'Repeated'
    },
    'Redirect' => {
      click_ourl: 'Click Ourl',
      click_target_url: 'Click Target Url',
      ourl: 'Ourl',
      target_url: 'Target Url',
    },
    'Timestamps' => {
      duration: 'Duration(s)',
      click_event_time: 'Feedmob Click Time',
      event_time: 'Feedmob Event Time',
      install_time: 'Feedmob Install Time',
      mmp_click_time: 'MMP Click Time',
      mmp_event_time: 'MMP Event Time',
      mmp_install_time: 'MMP Install Time',
      postback_time: 'Postback Time'
    }
  }.freeze

  MAX_LOOP_LIMITATION = 100
  LOOP_LIMITATION = 25
  ES_QUERY_SIZE = 2_000

  def initialize(ops, export_columns)
    @export_columns = export_columns
    @ops = ops
    @start_date = ops[:start_date]
    @end_date = ops[:end_date]
    @loop_count = 0
    @after = 0
    @loop_limitation = ops[:export_all] ? MAX_LOOP_LIMITATION : LOOP_LIMITATION
  end

  def resources_from_es
    query = {
      query: { bool: conditions },
      size: ES_QUERY_SIZE,
      search_after: [@after],
      sort: [{ uuid: "asc" }]
    }

    resource = ElasticsearchClient.search_conversion_records(query)["hits"]["hits"]
    return [] if resource.count.zero?

    if display?(:the24metrics_reason)
      the24metrics_conversion_ids = resource.filter{|item| item["_source"]["status"] == 'rejected_by_24metrics' }
                                            .map{|item| item["_source"]["conversion_id"]}
      @the24metrics_conversions = daily_conversion_records(the24metrics_conversion_ids)
    end
    @after = resource.last&.dig("_source")&.dig("uuid")

    resource.map{|r| OpenStruct.new(r["_source"])}
  rescue => e
    Sentry.capture_exception(e, extra: { loop_count: @loop_count, query: query })
    []
  end

  def call
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(headers)
      loop do
        @loop_count += 1
        break if @loop_count > loop_limitation
        rows = resources_from_es
        break if rows.length == 0
        rows.each do |r|
          data_row = build_data_row(r)
          yielder << CSV.generate_line(data_row)
        end
      end
    end
  end

  def build_data_row(r)
    if r.status == 'rejected_by_24metrics' && display?(:the24metrics_reason) && the24metrics_conversions.present?
      the24metrics_reason = the24metrics_conversions.select { |arr| arr.conversion_id == r.conversion_id && arr.track_type == r.track_type }&.first&.info&.memo["the24metrics_reason"]
    end

    data_row = []
    feedmob_install_at = get_feedmob_install_at(r.ourl)
    data_row << create_identifier(r, feedmob_install_at) if display? :identifier
    data_row << feedmob_install_at if display? :feedmob_install_at
    data_row << r.conversion_id if display? :conversion_id
    data_row << r.click_url_id if display? :click_url_id
    data_row << r.vendor_name if display? :vendor_name
    data_row << format_time(r.event_time) if display? :event_time
    data_row << format_time(r.install_time.presence || (r.track_type == 'install' ? r.event_time : nil)) if display? :install_time
    data_row << format_time(r.click_event_time) if display? :click_event_time
    data_row << NewConversionRecord.remap_status(r.status) if display? :status
    data_row << (r.status == 'rejected_by_24metrics' ? the24metrics_reason : "") if display?(:the24metrics_reason)
    data_row << r.track_type if display? :track_type
    data_row << r.action_name if display? :action_name
    data_row << r.campaign_name if display? :campaign_name
    data_row << r.creative if display? :creative
    data_row << r.track_party if display? :track_party
    data_row << r.click_device_platform_id if display? :click_device_platform_id
    data_row << r.device_platform_id if display? :device_platform_id
    data_row << r.app_id if display? :app_id
    data_row << r.ourl if display? :ourl
    data_row << r.target_url if display? :target_url
    data_row << r.purchase_amt if display? :purchase_amt
    data_row << r.app_name if display? :app_name
    data_row << r.duration if display? :duration
    data_row << r.remote_ip if display? :remote_ip
    data_row << r.mmp_city if display? :mmp_city
    data_row << r.city if display? :city
    data_row << r.click_ourl if display? :click_ourl
    data_row << r.click_target_url if display? :click_target_url
    export_mmp_time(data_row, r.mmp_click_time) if display? :mmp_click_time
    export_mmp_time(data_row, r.mmp_install_time) if display? :mmp_install_time
    export_mmp_time(data_row, r.mmp_event_time) if display? :mmp_event_time
    data_row << r.sub_status&.map { |v| NewConversionRecord.remap_status(v) }&.join(',') if display? :sub_status
    data_row << r.response_code if display? :response_code
    data_row << format_time(r.postback_time.presence) if display?(:postback_time)
    data_row << r.repeated if display? :repeated
    data_row
  end

  private

  def must_conditions
    must = [{ range: { event_date: { gte: start_date, lte: end_date } } }]

    must << { match: { status: NewConversionRecord.statuses.key(ops[:status].to_i) } } if ops[:status].present?

    must << { match: { repeated: false } } if ops[:track_type] == ['first_purchase']

    if ops[:track_type].present?
      track_type_keys = ops[:track_type]
                            .map { |type| type == 'first_purchase' ? NewConversionRecord.track_types["purchase"].to_s : type }
                            .uniq
                            .map { |value| NewConversionRecord.track_types.key(value.to_i) }
      must << { terms: { track_type: track_type_keys } }
    end

    must << { terms: { vendor_id: Array(ops[:vendor_id]).map(&:to_i) } } if ops[:vendor_id].present?

    if ops[:campaign_id].present?
      must << { terms: { campaign_id: Array(ops[:campaign_id]).map(&:to_i) } }
    elsif ops[:client_id].present?
      campaign_ids = Campaign.where(client_id: ops[:client_id]).pluck(:id).uniq
      must << { terms: { campaign_id: campaign_ids } } if campaign_ids.present?
    end

    conversion_id = ops[:conversion_id].split(',').map(&:strip) if ops[:conversion_id].present?

    if conversion_id.present?
      should = [{ terms: { conversion_id: conversion_id } },
                { terms: { device_platform_id: conversion_id } }]

      must << { bool: { should: should } }
    end

    if ops[:app_identity].present?
      must << { term: { app_identity: ops[:app_identity].strip } } if !!ops[:app_id_prefix_match] == false
      must << { prefix: { app_identity: ops[:app_identity].strip } } if !!ops[:app_id_prefix_match]
    end

    if ops[:click_id].present?
      click_ids = ops[:click_id].split(',').map(&:strip)
      if click_ids.any?
        must << { bool: { should: [{ terms: { click_url_id: click_ids } }] } }
      end
    end

    if ops[:app_id].present?
      must << { match: { app_id: ops[:app_id] } }
    end

    if ops[:duration_min].present? && ops[:duration_max].present?
      must << { range: { duration: { gte: ops[:duration_min], lte: ops[:duration_max] } } }
    end

    if ops[:privacy_mode].present?
      url = ops[:privacy_mode] == 'From AAP Endpoint' ? 'mode.feedmob.com' : 'tracking.feedmob.com'
      must << { regexp: { ourl: { value: ".*#{url}.*" } } }
    end

    if ops[:response_code].present?
      must << { match: { response_code: ops[:response_code] } }
    end

    if ops[:over_cap].present?
      must.concat(over_cap_conditions)
    end

    must
  end

  def over_cap_conditions
    must = [{ term: { repeated: false } }]

    if ops[:over_cap] == 'released'
      must << {
          term: {
            release_type: 'over_cap'
          }
        }
      must << {
        term: {
          release: true
        }
      }
    end

    must
  end

  def conditions
    cond = {must: must_conditions}
    if ops[:postback_or_not].present?
      if ops[:postback_or_not] == 'postback'
        cond[:filter] = {exists: {field: "target_url"}}
      else
        cond[:must_not] = {exists: {field: "target_url"}}
      end
    end
    cond
  end

  def export_mmp_time(data_row, record)
    mmp_time = millsecond_time?(record) ? format_time(Time.at(record.to_i)) : record
    data_row << mmp_time
  end

  def millsecond_time?(time)
    time =~ /\A[0-9]+$/
  end

  def format_time(time)
    return '' if time.blank?

    time.to_time.in_time_zone.strftime('%Y-%m-%d %H:%M:%S %Z')
  end

  def create_identifier(r, feedmob_install_at)
    if ["AppSamurai", "Kickcash"].include?(r.vendor_name)
      "#{r.app_id}_#{r.remote_ip}_#{feedmob_install_at}"
    else
      "#{r.vendor_name}_#{r.app_id}_#{r.remote_ip}_#{feedmob_install_at}"
    end
  end

  def get_feedmob_install_at(url)
    query_string = URI(url).query
    url_params = Rack::Utils.parse_query(query_string)
    url_params['FEEDMOB_INSTALL_AT']
  end

  def headers
    @headers ||=
      HEADERS_MAPPING
        .select { |key, _| display? key }
        .values
  end

  def display?(column)
    column.to_s.in? @export_columns
  end

  def daily_conversion_records(the24metrics_conversion_ids)
    return [] if the24metrics_conversion_ids.blank?
    conversion_ids = the24metrics_conversion_ids
    daily_conversion_records = []
    (Date.parse(start_date.to_s)..Date.parse(end_date.to_s)).to_a.each do |date|
      daily_record = NewDailyConversionRecord.table_by_date(date).where(status: 12).where("conversion_id IN ('#{conversion_ids.join("','")}')")
      daily_conversion_records << (daily_record.present? ? daily_record : nil)
    end
    daily_conversion_records = daily_conversion_records.flatten.compact
  end
end
