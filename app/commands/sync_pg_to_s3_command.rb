# frozen_string_literal: true

class SyncPgToS3Command
  prepend SimpleCommand
  include FileUtils

  attr_reader :file_name, :zipped_file_name, :bucket, :s3_path, :export_data_sql, :db

  def initialize(bucket, s3_path, file_name, export_data_sql, db = 'primary_db')
    @file_name  = file_name
    @zipped_file_name = "#{@file_name}.gz"
    @export_data_sql = export_data_sql
    @bucket = bucket
    @s3_path = s3_path
    @db = db
  end

  def call
    return if export_data_sql.blank?

    export_data_to_file
    gzip_file
    upload_file_to_s3
  ensure
    clear_local_tmp_files
  end

  private

  def gzip_file
    clear_local_tmp_files
    sh "gzip #{local_file_path}"
  end

  def export_data_to_file
    db_url = if db == 'primary_db'
      ENV['PRIMARY_DATABASE_URL']
    elsif db == 'conversion_db'
      ENV['CONVERSION_DATABASE_URL']
    elsif db == 'aggregate_db'
      ENV['AGGREGATED_STATS_DATABASE_URL']
    end

    sh "mkdir -p #{File.dirname(local_file_path)}"
    sh "psql \'#{db_url}\' -c \"\\COPY (#{export_data_sql}) TO #{local_file_path} WITH CSV HEADER DELIMITER '^' NULL AS '\N'\""
  end

  def upload_file_to_s3
    s3_service = Aws::S3::Resource.new(region: ENV["AWS_S3_REGION"] || "us-east-1")
    obj = s3_service.bucket(s3_bucket_name).object("#{s3_path}/#{zipped_file_name}")
    obj.upload_file(local_zipped_file_path)
  end

  def s3_file_path
    "#{s3_bucket_name}/#{s3_path}/#{zipped_file_name}"
  end

  def clear_local_tmp_files
    sh "rm -rf #{local_zipped_file_path}"
  end

  def local_file_path
    "/tmp/#{file_name}"
  end

  def local_zipped_file_path
    "/tmp/#{zipped_file_name}"
  end

  def s3_bucket_name
    Rails.env.production? ? bucket : 'feedmob-testing'
  end
end
