# frozen_string_literal: true

class CheckScopelyAdjustReportDifferenceCommand
  prepend SimpleCommand

  FILTER_EVENTS = ["impression", "click", "install", "registration", "purchase", "retained", "tutorial"].freeze
  attr_reader :start_date, :end_date

  def initialize(start_date: Date.yesterday.to_s, end_date: Date.yesterday.to_s)
    @start_date = start_date
    @end_date = end_date
  end

  def call
    adjust_api_datas = fetch_api_data
    # 排除不在 EXCLUDE_SCOPELY_ADJUST_REPORT_DATA 配置中的 adjust_campaign_name
    adjust_api_datas = adjust_api_datas.select {|c|
      app_id = c['store_id']
      adjust_campaign_name = c['campaign']
      !adjust_campaign_name.in?(exclude_data[app_id].to_a)
    }

    db_datas = adjust_db_datas
    mappings = mappings(adjust_api_datas, db_datas)
    diff = []

    (start_date.to_date..end_date.to_date).each do |date|
      date_api_datas = adjust_api_datas.select { |c| c["day"].to_date.to_s == date.to_s }
      group_by_campaign_datas = group_data_by_campaign(date_api_datas)

      mappings.each do |mapping|
        process_mapping(mapping, group_by_campaign_datas, db_datas, date, diff)
      end
    end

    diff.sort_by { |d| [d[:date], d[:click_url_id].to_s, d[:adjust_campaign_name].to_s] }
  end

  private

  def process_mapping(mapping, group_by_campaign_datas, db_datas, date, diff)
    campaign_datas = select_campaign_data(mapping, group_by_campaign_datas)
    return if campaign_datas.empty?

    campaign_datas_value = campaign_datas.values.first
    api_data_counts = aggregate_data(campaign_datas_value)

    event_mapping = event_mappings.find { |c| c.app_id == mapping.app_id }
    add_filtered_event_counts(api_data_counts, campaign_datas_value, event_mapping)

    db_data = find_db_data(db_datas, mapping, date)

    return if exclude_click_url_ids.include?(db_data&.click_url_id)
    if db_data&.click_url_id
      return if should_skip_click_url?(db_data)
    end

    db_data_counts = aggregate_db_data_counts(db_data, api_data_counts)
    return if api_data_counts == db_data_counts

    diff << build_diff_hash(mapping, db_data, date, api_data_counts, db_data_counts)
  end

  def fetch_api_data
    @fetch_api_data ||= FetchScopelyAdjustReportBaseCommand.new.fetch_scopely_adjust_data(start_date, end_date, true)
  end

  def mappings(adjust_api_datas, adjust_db_datas)
    api_adjust_campaign_names = adjust_api_datas.pluck("campaign")
    db_adjust_campaign_names = adjust_db_datas.pluck("adjust_campaign_name")

    AdjustCampaignMapping.includes(campaign: :app)
      .where(adjust_campaign_name: (api_adjust_campaign_names + db_adjust_campaign_names).uniq)
      .where(client_id: Client::SCOPELY_ID, skan: false)
      .where.not(vendor_id: test_vendor_ids)
      .to_a
  end

  def group_data_by_campaign(api_datas)
    api_datas.group_by { |c| [c["store_id"], c["campaign"], c["os_name"], c["partner_name"].to_s.downcase] }
  end

  def select_campaign_data(mapping, grouped_datas)
    grouped_datas.select do |key, _|
      match_campaign_name(mapping, key[1]) && mapping.channel.to_s.downcase == key[3]
    end
  end

  def aggregate_data(campaign_datas_value)
    {
      impression_count: campaign_datas_value.sum { |data| data["impressions"].to_i },
      click_count: campaign_datas_value.sum { |data| data["clicks"].to_i },
      install_count: campaign_datas_value.sum { |data| data["installs"].to_i },
    }
  end

  def add_filtered_event_counts(api_data_counts, campaign_datas_value, event_mapping)
    return unless event_mapping.present?

    event_mapping.events_in_json.each do |feedmob_event, adjust_events|
      if FILTER_EVENTS.include?(feedmob_event)
        event_count = adjust_events.map do |event|
          campaign_datas_value.sum { |data| data[event].to_i }
        end.sum
        api_data_counts["#{feedmob_event}_count".to_sym] = event_count
      end
    end
  end

  def find_db_data(db_datas, mapping, date)
    db_datas.find do |k|
      k.date.to_s == date.to_s &&
      match_campaign_name(mapping, k.adjust_campaign_name.to_s)
    end
  end

  def should_skip_click_url?(db_data)
    click_url_id = db_data.click_url_id
    click_url_ids.include?(click_url_id) && job_stat_click_url_ids.exclude?(click_url_id)
  end

  def aggregate_db_data_counts(db_data, api_data_counts)
    db_data_counts = {}
    api_data_counts.keys.each do |k|
      db_data_counts[:"#{k}"] = db_data&.send("#{k}").to_i
    end
    db_data_counts
  end

  def build_diff_hash(mapping, db_data, date, api_data_counts, db_data_counts)
    diff_hash = {
      campaign_id: mapping.campaign_id,
      vendor_id: mapping.vendor_id,
      adjust_campaign_name: mapping.adjust_campaign_name,
      click_url_id: find_by_click_url_id(db_data, mapping),
      date: date,
    }

    passed = true 

    api_data_counts.keys.each do |key|
      diff_hash[:"#{key}"] = "#{api_data_counts[:"#{key}"]}/#{db_data_counts[:"#{key}"]}"
      _threshold = (key.to_s == 'click_count' ? 100 : threshold)
      passed = false if (api_data_counts[:"#{key}"].to_i - db_data_counts[:"#{key}"].to_i).abs > _threshold
    end
    diff_hash[:passed] = passed
    
    diff_hash
  end

  def find_by_click_url_id(db_data, mapping)
    return db_data.click_url_id if db_data&.click_url_id
    ClickUrl.find_by(campaign_id: mapping.campaign_id, vendor_id: mapping.vendor_id)&.id
  end

  def job_stat_click_url_ids
    @job_stat_click_url_ids ||= DirectSpendJobStat.where(status: "live").flat_map(&:click_url_ids)
  end

  def event_mappings
    @event_mappings ||= AdjustEventMapping.where(client_id: Client::SCOPELY_ID).to_a
  end

  def click_url_ids
    @click_url_ids ||= ClickUrl.joins(:campaign).where("campaigns.client_id = ?", Client::SCOPELY_ID).where.not(link_type: "fm_link").pluck(:id)
  end

  def tracking_config
    @tracking_config ||= TrackingConfig.find_by(config_type: "system", name: "CHECK_ADJUST_REPORT_EXCLUDE")&.value&.with_indifferent_access || {}
  end

  def exclude_click_url_ids
    tracking_config[:click_url_ids]
  end

  def threshold
    tracking_config[:threshold] || 0
  end

  def match_campaign_name(mapping, adjust_campaign_name)
    mapping.adjust_campaign_name == adjust_campaign_name || mapping.adjust_campaign_name == adjust_campaign_name.sub(/_+$/, "")
  end

  def adjust_db_datas
    @adjust_db_datas ||= begin
        agency = AdjustAgencyReport.find_by_sql(db_query("adjust_agency_reports"))
        non_agency = AdjustAgencyReport.find_by_sql(db_query("adjust_reports"))
        non_agency + agency
      end
  end

  def db_query(table)
    <<-SQL
      SELECT
        date,
        click_url_id,
        campaign_id,
        vendor_id,
        c.name as campaign_name,
        v.vendor_name AS vendor_name,
        adjust_campaign_name,
        sum(impression) as impression_count,
        sum(click) as click_count,
        sum(install) as install_count,
        sum(registration) as registration_count,
        sum(purchase) as purchase_count,
        sum(retained) as retained_count,
        sum(tutorial) as tutorial_count
      FROM #{table} AS ns
      LEFT JOIN campaigns c ON c.id = ns.campaign_id
      LEFT JOIN vendors v ON v.id = ns.vendor_id
      WHERE date BETWEEN '#{start_date}' AND '#{end_date}'
      AND v.for_test = false
      AND c.client_id = #{Client::SCOPELY_ID}
      GROUP BY 1, 2, 3, 4, 5, 6, 7 
    SQL
  end

  def exclude_data
    @exclude_data ||= TrackingConfig.find_by(config_type: "system", name: "EXCLUDE_SCOPELY_ADJUST_REPORT_DATA")&.value&.with_indifferent_access
  end

  def test_vendor_ids
    @test_vendor_ids ||= Vendor.where(for_test: true).pluck(:id)
  end
end
