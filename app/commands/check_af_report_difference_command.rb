class CheckAfReportDifferenceCommand
  prepend SimpleCommand

  attr_reader :af_app_id, :client_id, :from_date, :to_date

  def initialize(af_app_id:, client_id:, from_date:, to_date: )
    @af_app_id = af_app_id
    @client_id = client_id
    @from_date = from_date
    @to_date = to_date
  end

  def call
    results = []
    mapped_multiple_click_urls = {}

    service = AppsflyerApiServiceV3.new(af_app_id: af_app_id, client_id: client_id, start_date: from_date, end_date: to_date)
    records = service.partners_by_date_report(via_api: true)
    return results if records.nil?

    click_url_mappings = mappings(client_id, mapped_multiple_click_urls)
    allowed_click_url_ids = app_id_click_urls(client_id)

    stored_results = {}

    records.each do |record|
      pid = get_pid(record)

      next if record["Agency/PMD (af_prt)"] != "Feedmob"

      date         = get_date(record)
      platform     = get_platform
      c            = get_c(record)
      spend        = get_spend(record)
      impression   = get_impression(record)
      click        = get_click(record)
      install      = get_install(record)
      click_url_id = get_click_url_id(client_id, af_app_id, pid, c, platform, spend, click_url_mappings)

      click_url = ClickUrl.find_by(id: click_url_id)
      next if click_url_id.blank? || click_url_id.to_i == 17258 || click_url.vendor.for_test || click_url.archived? || not_belong_to_app?(click_url_id, allowed_click_url_ids) || (click_url.link_type != 'fm_link' && job_stat_click_url_ids.exclude?(click_url.id))
      next if click_url.link_type == 'fm_link'
      next if CreateAppsflyerRawInstallSpendJob.new.click_url_ids.include?(click_url_id.to_i)

      mapped_multiple_click_urls[[pid, c, platform]]&.delete(click_url_id) if spend == 0

      stored_result = stored_results[[date, click_url_id, pid, c]] || {}
      stored_results[[date, click_url_id, pid, c]] = {
        spend: stored_result[:spend].to_d + spend.to_d,
        click: stored_result[:click].to_i + click.to_i,
        impression: stored_result[:impression].to_i + impression.to_i,
        install: stored_result[:install].to_i + install.to_i
      }
    end

    stored_results.each do |(date, click_url_id, pid, c), data|
      result = find_difference(
        date,
        click_url_id,
        pid,
        c.force_encoding(Encoding::UTF_8),
        data[:spend],
        data[:click],
        data[:impression],
        data[:install]
      )

      results << result if result.present?
    end

    results
  end

  private

  def mappings(client_id, mapped_multiple_click_urls)
    res = ClickUrl.connection.query(
      <<-EOQ
        SELECT cupm.click_url_id,
                cupm.pid,
                cupm.c,
                cupm.os,
                v.vendor_name,
                (CASE WHEN c.reporting_campaign_name_switch THEN c.reporting_campaign_name ELSE c.name END) AS campaign_name,
                cu.status
        FROM click_url_params_mappings cupm
        LEFT JOIN click_urls cu on cu.id = cupm.click_url_id
        LEFT JOIN campaigns c on c.id = cu.campaign_id
        LEFT JOIN vendors v on v.id = cu.vendor_id
        WHERE c.client_id = #{client_id}
          AND c.name ilike '%Agency%'
          AND cupm.deleted_at IS NULL
      EOQ
    )

    archived_audits = Audit.where(auditable_type: "ClickUrl", auditable_id: res.map{|r|r[0]}, audited_changes: [{ "status"=>[100, 1] }, { "status"=>[4, 1] }]).order(id: :desc)

    grouped_mappings = {}
    res.each do |r|
      key = [r[1], r[2].gsub('{FEEDMOB_VENDOR_NAME}', r[4].gsub(' ', '_')).gsub('{CLICK_URL_ID}', r[0].to_s).gsub('{REPORTING_CAMPAIGN_NAME}', r[5]), r[3]]
      audit = archived_audits.find{|a|a.auditable_id == r[0]}
      unless r[6] == 1 && audit.present? && audit.created_at.to_date <= to_date.to_date
        grouped_mappings[key] ||= {}
        grouped_mappings[key][r[6].to_i] ||= []
        grouped_mappings[key][r[6].to_i] << r[0]
      end
    end
    mapping = {}
    grouped_mappings.each do |pid_c_os, urls|
      mapped_multiple_click_urls[pid_c_os] = urls.values.flatten.uniq
      if urls[4].present? || urls[99].present?
        mapping[pid_c_os] = ((urls[4] || []) + (urls[99] || [])).flatten.max
      else
        mapping[pid_c_os] = urls.values.flatten.max
      end
    end
    mapping
  end

  def get_pid(record)
    record["Media Source (pid)"].strip
  end

  def get_date(record)
    record["Date"].strip
  end

  def get_c(record)
    record["Campaign (c)"].strip
  end

  def get_spend(record)
    record["Total Cost"] == 'N/A' ? 0 : record["Total Cost"]
  end

  def get_impression(record)
    record["Impressions"] == 'N/A' ? 0 : record["Impressions"]
  end

  def get_click(record)
    record["Clicks"] == 'N/A' ? 0 : record["Clicks"]
  end

  def get_install(record)
    record["Installs"] == 'N/A' ? 0 : record["Installs"]
  end

  def get_platform
    if af_app_id.start_with?("id")
      'iOS'
    elsif af_app_id.start_with?("com")
      'Android'
    end
  end

  def get_click_url_id(client_id, af_app_id, pid, c, platform, spend, click_url_mappings)
    click_url_id = AppsflyerAgencyReport.map_click_url_based_on_rule(
      self.class.name,
      client_id,
      af_app_id,
      pid,
      platform,
      spend
    )

    return click_url_id if click_url_id.present?

    click_url_mappings[[pid, c, platform]]
  end

  def find_difference(date, click_url_id, pid, c, spend, click, impression, install)
    conditions = { date: date, pid: pid, c: c }
    aar = AppsflyerAgencyReport.where(conditions.merge(click_url_id: click_url_id, af_app_id: af_app_id)).first
    aar = AppsflyerAgencyReport.where(conditions.merge(spend: spend)).first if aar.nil?
    # https://github.com/feed-mob/tracking_admin/issues/14807#issuecomment-1386692382
    return if click_url_id == 174_22 && aar&.click.to_i == click.to_i && aar&.impression.to_i == impression.to_i && aar&.install.to_i == install.to_i

    if aar&.install.to_i != install.to_i ||
      get_diff_percentage(aar&.click.to_i, click.to_i) > threshold_in_config[:click_count_diff_percentage].to_i ||
      get_diff_percentage(aar&.impression.to_i, impression.to_i) > threshold_in_config[:impression_count_diff_percentage].to_i

      {
        af_app_id: af_app_id,
        pid: pid,
        c: c,
        click_url_id: click_url_id,
        date: date,
        data: {
          api_click: click.to_i,
          db_click:  aar&.click.to_i,
          api_impression: impression.to_i,
          db_impression: aar&.impression.to_i,
          api_install: install.to_i,
          db_install:  aar&.install.to_i
        }
      }
    end
  end

  def get_diff_percentage(db, api)
    return 0 if db == 0 && api == 0
    (db - api).abs * 100.0 / db.to_d
  end

  def app_id_click_urls(client_id)
    ClickUrl.connection.query(
      <<-SQL
        SELECT DISTINCT cu.id
        FROM click_urls cu
        LEFT JOIN campaigns c on c.id = cu.campaign_id
        WHERE c.client_id = #{client_id}
      SQL
    ).flatten
  end

  def not_belong_to_app?(click_url_id, allowed_click_url_ids)
    allowed_click_url_ids.present? && !allowed_click_url_ids.include?(click_url_id)
  end

  def threshold_in_config
    TrackingConfig.find_by(config_type: 'system', name: 'CHECK_AF_SPEND_REPORT_THRESHOLD').value.with_indifferent_access
  end

  def job_stat_click_url_ids
    @job_stat_click_url_ids ||= DirectSpendJobStat.where(status: 'live').map{ |c| c.click_url_ids }.flatten
  end
end
