class CampaignIdExtractionCommand
  prepend SimpleCommand

  attr_reader :campaign_ids, :owner_id, :client_id, :app_category_id, :parent_campaign_id, :cr_value, :options, :develop_mode

  def initialize(options)
    @campaign_ids    = [options[:campaign_ids]].flatten
    @owner_id        = options[:owner_id]
    @client_id       = options[:client_id]
    @parent_campaign_id = options[:parent_campaign_id]
    @cr_value        = options[:cr_value]
    @app_category_id = options[:app_category_id]
    @develop_mode    = options[:develop] == 'true'
    @options         = options
  end

  def call
    ids = user_selected_campaign_ids & owner_campaign_ids & client_campaign_ids & sub_campaign_ids & category_campaign_ids & exclude_twitter_old_campaigns
    ids -= test_campaign_ids unless develop_mode
    ids
  end

  private

  def category_campaign_ids
    return all unless app_category_id.present?
    app_info_ids = AppInfo.where('app_category_ids @> ARRAY[?]', [app_category_id.to_i]).pluck(:id)
    Campaign.unscoped.where(app_info_id: app_info_ids).pluck(:id)
  end

  def user_selected_campaign_ids
    return all unless campaign_ids.present?
    return all unless campaign_ids.select(&:present?).present?
    campaign_ids.map(&:to_i)
  end

  def owner_campaign_ids
    return all unless owner
    return all if super_user?(owner)
    return all unless campaign_owner?(owner)
    owner.campaign_ids
  end

  def client_campaign_ids
    return campaign_ids.map(&:to_i) if client.nil? && cr_value && parent_campaign_id.blank?
    return all unless client
    Campaign.unscoped.where(client_id: client_id).map(&:id)
  end

  def sub_campaign_ids
    return all unless parent_campaign_id.present?
    Campaign.unscoped.joins(:parent_campaigns).where("parent_campaigns.id" => parent_campaign_id).pluck(:id)
  end

  def test_campaign_ids
    Rails.cache.fetch('test_campaign_ids', expires_in: 1.hours) do
      Campaign.includes(:client).where('clients.for_test IS TRUE').ids
    end
  end

  def owner
    return unless owner_id.present?
    @owner ||= User.where(id: owner_id).first
  end

  def client
    return unless client_id.present?
    @client ||= Client.where(id: client_id).first
  end

  def super_user?(user)
    @super_user_emails ||=  MailGroup.mapping_mails("#{self.class}##{__callee__}")
    @super_user_emails.include? user.email.strip
  end

  def campaign_owner?(user)
    Campaign.owners.include?(user)
  end

  def all
    Rails.cache.fetch("all_campaign_ids", expires_in: 1.hours) do
      Campaign.ids
    end
  end

  def exclude_twitter_old_campaigns
    if options[:exclude_twitter_old_campaigns] == 'true'
      all - Campaign.excluded_campaign_ids
    else
      all
    end
  end
end
