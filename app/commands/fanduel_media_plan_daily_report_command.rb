# frezen_string_literal: true

require 'googleauth'
require 'google/apis/sheets_v4'

class FanduelMediaPlanDailyReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1rmZXEdnZ6PINF0oq_tLNJITCukidtLOrzRSVLNLEI5g'.freeze

  def initialize(date)
    @date = date.to_date
  end

  def call
    service = Google::Apis::SheetsV4::SheetsService.new
    service.authorization = authorizer

    # header_range = 'Caps/Pacing!A2:AK2'
    # response = service.get_spreadsheet_values(SPREADSHEET_ID, header_range)
    # headers = response.values.first if response.values

    total = data.values.map(&:to_f).sum
    unless total > 0
      SlackService.send_notification_to_channel("#{self.class.name}: Fanduel Media Plan spend 数据为 0, 无法录入当天的 spend 数据到 Google sheet 中", :mighty)
    end

    range_name = "Caps/Pacing!H1:H200"
    response = service.get_spreadsheet_values(SPREADSHEET_ID, range_name)
    if !response.values
      SlackService.send_notification_to_channel("#{self.class.name}: Fanduel Media Plan Google Sheet 为空，无法录入 spend 数据到 Google sheet 中，请PM检查", :mighty)
      return
    end

    # 找到上个月的序号
    # 找个当月的序号
    row_index = nil
    current_month_row_index = nil
    response.values.each_with_index do |row, index|
      if row.first == @date.beginning_of_month.last_month.strftime("%-m/%-d/%Y")
        row_index = index + 1
        break
      end
    end
    response.values.each_with_index do |row, index|
      if row.first == @date.beginning_of_month.strftime("%-m/%-d/%Y")
        current_month_row_index = index
        break
      end
    end

    if row_index.nil?
      SlackService.send_notification_to_channel("#{self.class.name}: Fanduel Media Plan Google Sheet 无法定位到当前月份需要录入数据的地方，无法录入 spend 数据到 Google sheet 中，请PM检查", :mighty)
      return
    end

    if current_month_row_index.nil?
      current_month_row_index = 0
    end

    # 更新本月的序号
    range_name = "Caps/Pacing!B#{current_month_row_index + 1}:B#{row_index-2}"
    response = service.get_spreadsheet_values(SPREADSHEET_ID, range_name)
    r = {}
    response.values.each_with_index do |row, index|
      click_url_id = row.first&.match(/\d+/)&.to_s
      if click_url_id.present?
        r[click_url_id] = current_month_row_index + index + 1
      end
    end

    missing_click_url_ids = r.keys.map(&:to_i) - data.keys.map(&:to_i)

    if missing_click_url_ids.present?
      content = generate_click_url_content(missing_click_url_ids)
      SlackService.send_notification_to_channel("#{self.class.name}: #{content}", :mighty)
    end

    data.each do |click_url_id, gross_spend|
      rw = r[click_url_id.to_s]
      if rw.present?
        range_name = "Caps/Pacing!#{get_date_row_name}#{rw}"
        p "#{click_url_id} #{range_name} #{gross_spend}"
        service.update_spreadsheet_value(SPREADSHEET_ID, range_name, {
          values: [[
            gross_spend
          ]],
        }, value_input_option: 'USER_ENTERED')
      end
    end

  rescue => e
    error_msg = JSON.parse(e.body)["error"]["message"]
    send_slack_alert(error_msg)
  end

  def get_date_row_name
    # start H = 72
    diff = (@date - @date.beginning_of_month).to_i
    if diff < 19
      return (72 + diff).chr
    else
      return 'A' + (65 + diff%19).chr
    end
  end

  def data
    @data ||= begin
      sql = <<-SQL
        SELECT net_spends.click_url_id, gross_spend_cents / 100.0 AS gross_spend
        FROM net_spends
        LEFT OUTER JOIN click_urls on click_urls.id = net_spends.click_url_id
        LEFT OUTER JOIN campaigns on campaigns.id = click_urls.campaign_id
        WHERE net_spends.spend_date = '#{@date}' and campaigns.client_id = 89;
      SQL
      r1 = NetSpend.connection.query(sql).to_h

      sql2 = <<-SQL
        SELECT sr.click_url_id::int, SUM(CASE WHEN info.direct_spend_input THEN 0 ELSE spend_cents END) / 100.0 AS gross_spend
        FROM v4_stat_records sr
        LEFT JOIN click_url_infos info ON info.click_url_id = sr.click_url_id AND info.event_date = sr.report_date
        WHERE sr.report_date = '#{@date}' AND sr.client_id = 89 AND sr.status = 'normal'
        GROUP by 1
        HAVING SUM(sr.spend_cents) > 0
      SQL
      r2 = ConversionRecordRedshift.connection.query(sql2).to_h.map { |k, v| [k.to_i, v] }.to_h

      r1.merge(r2) do |key, oldval, newval|
        oldval.to_f + newval.to_f
      end
    end
  end

  def send_slack_alert(errors)
    return if errors.blank?
    content = "#{errors}\nGoogle Sheet 中没有 #{@date.to_s} 相应的数据单元, 会影响 Fanduel Media Plan 的 spend 数据录入, 请 PM 检查."
    SlackService.send_notification_to_channel("#{Thread.current[:current_job]} #{self.class.name}:\n#{content}", :mighty)
  end

  def generate_click_url_content(ids)
    click_urls = ClickUrl.where(id: ids).order('id desc')
    grouped_click_urls = click_urls.map{|cu|
      {
        id: cu.id,
        status: cu.status,
        stage_level: cu.stage_level,
      }
    }.group_by{|item| item[:stage_level] }

    result = []
    result << "Fanduel Media Plan Google Sheet 以下click url无法录入spend数据， 请PM检查："
    grouped_click_urls.each do |stage_level, list|
      ids = list.map{|cu| cu[:id]}.join(', ')
      if stage_level == "level 0"
        result << "#{ids} -- 是 level 0 的 click_url, stat_record 数据已停止生成"
      elsif stage_level == "level 1 (automatic)"
        ids_active = list.select{|cu| cu[:status] == 'cu_normal' }.map{|cu| cu[:id]}.join(', ')
        ids_paused = list.select{|cu| cu[:status] == 'paused' }.map{|cu| cu[:id]}.join(', ')
        result << "#{ids} -- 是自动录入 direct spend 的 click url, direct spend 已停止录入, #{ids_active} 是 active 状态, #{ids_paused} 已经 paused  "
      elsif stage_level == "level 1 (manual)"
        result << "#{ids} -- 是手动录入 direct spend 的 click url, #{@date.to_s} 的 direct spend 还未录入"
      end
    end
    result.join("\n\n")
  end

  private

  def authorizer
    @authorizer ||= begin
      authorizer = Google::Auth::ServiceAccountCredentials.make_creds(
        json_key_io: File.open(client_secrets_file_path),
        scope: SCOPE
      )
      authorizer.fetch_access_token!
      authorizer
    end
  end

  def client_secrets_file_path
    File.join(current_path, "config", 'feedmob-4bbd49e741a5.json')
  end

  def current_path
    Dir["#{Rails.root}"]
  end
end
