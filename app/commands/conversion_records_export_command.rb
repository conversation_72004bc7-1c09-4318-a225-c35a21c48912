# frozen_string_literal: true

require 'csv'

class ConversionRecordsExportCommand
  include ActionView::Helpers::<PERSON><PERSON>elper
  prepend SimpleCommand

  attr_reader :ops, :start_date, :end_date

  ExportResult = Struct.new(
    :conversion_id, :click_url_id, :vendor_name, :event_time, :install_time,
    :click_event_time, :status, :track_type, :action_name, :campaign_name,
    :creative, :track_party, :click_device_platform_id, :device_platform_id,
    :app_id, :ourl, :target_url, :purchase_amt, :app_name, :duration,
    :remote_ip, :mmp_city, :city, :click_ourl, :click_target_url,
    :mmp_click_time, :mmp_install_time, :mmp_event_time, :sub_status,
    :response_code, :postback_time, :repeated
  )

  # WARNING!!!
  # Be careful if you are modifying this mapping, as it is also used to generate csv report to clients

  HEADERS_MAPPING = {
    conversion_id: 'Conversion ID',
    click_url_id: 'Click Url ID',
    vendor_name: 'Vendor Name',
    event_time: 'Feedmob Event Time',
    install_time: 'Feedmob Install Time',
    click_event_time: 'Feedmob Click Time',
    status: 'Status',
    track_type: 'TrackType',
    action_name: 'Action Name',
    campaign_name: 'Campaign',
    creative: 'Creative',
    track_party: 'Track Party',
    click_device_platform_id: 'Click Device Platform ID',
    device_platform_id: 'Postback Device Platform ID',
    app_id: 'App ID',
    ourl: 'Ourl',
    target_url: 'Target Url',
    purchase_amt: 'Purchase Amt',
    app_name: 'App Name',
    duration: 'Duration(s)',
    remote_ip: 'Remote IP',
    mmp_city: 'MMP City',
    city: 'City',
    click_ourl: 'Click Ourl',
    click_target_url: 'Click Target Url',
    mmp_click_time: 'MMP Click Time',
    mmp_install_time: 'MMP Install Time',
    mmp_event_time: 'MMP Event Time',
    sub_status: 'Sub Status',
    response_code: 'Vendor Response Code',
    postback_time: 'Postback Time',
    repeated: 'Repeated',
    the_24_metrics_reason:'The24metrics Reason'
  }.freeze

  LOOP_LIMITATION = 100
  ES_QUERY_SIZE = 2_000

  def initialize(ops)
    @ops = ops
    @start_date = ops[:start_date]
    @end_date = ops[:end_date]
    @loop_count = 0
    @after = 0
  end

  def resources_from_es
    query = {
      query: { bool: {must: must_conditions} },
      size: ES_QUERY_SIZE,
      search_after: [@after],
      sort: [{ uuid: "asc" }]
    }
    resource = ElasticsearchClient.search_conversion_records(query)["hits"]["hits"]
    return [] if resource.count.zero?
    @after = resource.last&.dig("_source")&.dig("uuid")
    resource.map{|r| ExportResult.new(*r["_source"].values_at(*ExportResult.members.map(&:to_s)))}
  rescue => e
    Sentry.capture_exception(e, extra: { loop_count: @loop_count, query: query })
    []
  end

  def call
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(HEADERS_MAPPING.values)
      loop do
        @loop_count += 1
        break if @loop_count > LOOP_LIMITATION
        rows = resources_from_es
        break if rows.length == 0
        the_24_metrics_conversion_ids = get_the_24_metrics_conversion_ids(rows)
        the_24_metrics_conversions = the_24_metrics_conversions(the_24_metrics_conversion_ids)
        rows.each do |r|
          data_row = build_data_row(r, the_24_metrics_conversions)
          yielder << CSV.generate_line(data_row)
        end
      end
    end
  end

  private

  def must_conditions
    must = [{ range: { event_date: { gte: start_date, lte: end_date } } }]

    must << { terms: { track_type: [ops[:track_type]] } } if ops[:track_type].present?

    if ops[:client_id].present?
      campaign_ids = Campaign.where(client_id: ops[:client_id]).pluck(:id).uniq
      must << { terms: { campaign_id: campaign_ids } } if campaign_ids.present?
    end

    if ops[:vendor_ids].present?
      must << { terms: { vendor_id: ops[:vendor_ids] } }
    end

    must
  end

  def build_data_row(r, the_24_metrics_conversions)
    [
      r.conversion_id,
      r.click_url_id,
      r.vendor_name,
      format_time(r.event_time),
      format_time(r.install_time.presence || (r.track_type == 'install' ? r.event_time : nil)),
      format_time(r.click_event_time),
      r.status,
      r.track_type,
      r.action_name,
      r.campaign_name,
      r.creative,
      r.track_party,
      r.click_device_platform_id,
      r.device_platform_id,
      r.app_id,
      r.ourl,
      r.target_url,
      r.purchase_amt,
      r.app_name,
      r.duration,
      r.remote_ip,
      r.mmp_city,
      r.city,
      r.click_ourl,
      r.click_target_url,
      format_mmp_time(r.mmp_click_time),
      format_mmp_time(r.mmp_install_time),
      format_mmp_time(r.mmp_event_time),
      r.sub_status&.join(','),
      r.response_code,
      format_time(r.postback_time.presence),
      r.repeated,
      get_the_24_metrics_reason(the_24_metrics_conversions, r)
    ]
  end  

  def format_mmp_time(record)
    millsecond_time?(record) ? format_time(Time.at(record.to_i)) : record
  end  

  def millsecond_time?(time)
    time =~ /\A[0-9]+$/
  end

  def format_time(time)
    return '' if time.blank?

    time.to_time.in_time_zone.strftime('%Y-%m-%d %H:%M:%S %Z')
  end

  def get_the_24_metrics_reason(the_24_metrics_conversions, resource)
    return if resource.status != 'rejected_by_24metrics'

    key = [resource.conversion_id, resource.track_type]
    the_24_metrics_conversions[key]&.info_memo&.dig('the24metrics_reason')
  end

  def get_the_24_metrics_conversion_ids(resources)
    resources.filter_map { |r| r.conversion_id if r.status == 'rejected_by_24metrics' }
  end

  def the_24_metrics_conversions(conversion_ids)
    return [] if conversion_ids.blank?

    daily_conversion_records = []
    (Date.parse(start_date.to_s)..Date.parse(end_date.to_s)).to_a.each do |date|
      daily_record = NewDailyConversionRecord.table_by_date(date).where(status: 12).where("conversion_id IN ('#{conversion_ids.join("','")}')").select(:uuid, :conversion_id, :track_type, :event_date)
      daily_conversion_records << daily_record.presence
    end
    daily_conversion_records.flatten.compact.index_by { |record| [record.conversion_id, record.track_type] }
  end
end
