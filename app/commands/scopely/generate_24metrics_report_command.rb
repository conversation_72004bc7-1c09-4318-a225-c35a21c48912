# frozen_string_literal: true

module Scopely
  class Generate24metricsReportCommand
    prepend SimpleCommand

    attr_reader :start_date, :end_date, :filename

    def initialize(start_date:, end_date:)
      @start_date = start_date.to_date
      @end_date = end_date.to_date
      @filename = "mg_24metrics_#{start_date}_#{end_date}"
      @down_file = Tempfile.new("#{filename}.csv")
      @zip_file = Tempfile.new("#{filename}.csv.zip")
    end

    def call
      # Account Sync - Adjust - YWB
      ywb_csv_stream = Scopely::Crawler24metricsReportCommand.new(
        start_date: start_date,
        end_date: end_date,
        url: "https://fraudshield.24metrics.com/app#/reporting/conversion?tracker_id=773&user_id=695&filters=%5B%5D&date_start=#{start_date}&date_end=#{end_date}&date_interval=custom&persist=false",
        tracker_id: 773
      ).call.result
      # Account Sync - Adjust - Monopoly
      monopoly_csv_stream = Scopely::Crawler24metricsReportCommand.new(
        start_date: start_date,
        end_date: end_date,
        url: "https://fraudshield.24metrics.com/app#/reporting/conversion?tracker_id=2417&user_id=695&filters=%5B%5D&date_start=#{start_date}&date_end=#{end_date}&date_interval=custom&persist=false",
        tracker_id: 2417
      ).call.result

      s3_url = upload_to_s3(ywb_csv_stream, monopoly_csv_stream)
      s3_historical_url = tracking_backup
      ClientDailyReportLog.create(
        user: '<EMAIL>',
        client_name: report_config.frontend_report_name,
        status: :finished,
        date_range: "#{start_date} to #{end_date}",
        upload_at: Time.now,
        memo: "Upload #{report_config.frontend_report_name} Report successfully",
        upload_path: s3_url,
        historical_backup_url: s3_historical_url,
        report_config_id: report_config.id,
      )
    end

    private

    def report_config
      @report_config ||= ReportConfig.find(95)
    end

    def upload_to_s3(ywb_csv_stream, monopoly_csv_stream)
      File.open(@down_file, 'w') do |f|
        ywb_csv_stream.each do |line|
          f.write(line)
        end
        monopoly_csv_stream.each_with_index do |line, index|
          next if index == 0
          f.write(line)
        end
      end
      ::Zip::File.open(@zip_file.path, ::Zip::File::CREATE) do |zipfile|
        zipfile.add("#{file_name}.csv", @down_file.path)
      end

      obj = s3_service.bucket(bucket_name).object("#{file_name}.csv.zip")
      obj.upload_file(@zip_file.path)
      "https://s3.amazonaws.com/#{bucket_name}/#{file_name}.zip"
    ensure
      @down_file&.close!
      @zip_file&.close!
    end

    def tracking_backup
      unique_filename = [filename, Time.zone.now.to_i, SecureRandom.uuid].join('-')
      target_filename = "report-historical-backup/#{unique_filename}.zip"
      AwsService.copy(
        source_bucket: bucket_name,
        target_bucket: bucket_name,
        source_filename: "#{filename}.zip",
        target_filename: target_filename
      )

      "https://s3.amazonaws.com/#{bucket_name}/#{target_filename}"
    end

    def bucket_name
      Rails.env.production? ? "feedmob-singular-reports-backup" : "tracking-env-stage"
    end

    def s3_service
      @s3_service ||= Aws::S3::Resource.new(region: ENV["AWS_S3_REGION"] || "us-east-1")
    end
  end
end
