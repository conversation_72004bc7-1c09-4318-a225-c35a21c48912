require 'capybara'
require 'capybara/cuprite'

module Scopely
  class Crawler24metricsReportCommand
    prepend SimpleCommand
    require 'csv'
    require 'tempfile'

    attr_reader :start_date, :end_date, :tracker_id

    def initialize(start_date:, end_date:, url:, tracker_id:)
      @start_date = start_date.to_date
      @end_date = end_date.to_date
      @tracker_id = tracker_id
      @url = url
    end

    def call
      setup_capybara
      temp_dir = Dir.mktmpdir('24metrics_report')
      begin
        zip_file = login_and_queue_report(temp_dir)
        process_zip_file(zip_file)
      ensure
        FileUtils.remove_entry(temp_dir) if temp_dir && Dir.exist?(temp_dir)
      end
    end

    def process_zip_file(zip_file)
      require 'zip'
      csv_data = []

      Zip::File.open(zip_file) do |zip|
        zip.each do |entry|
          next unless entry.name.end_with?('.csv')
          csv_content = entry.get_input_stream.read
          CSV.parse(csv_content, headers: true) do |row|
            row_hash = row.to_h
            row_hash['updated_status'] = row_hash['status']
            row_hash['updated_status'] = 'approved' if satisfy_special_condition?(row_hash)
            row_hash['identifier'] = generate_identifier(row_hash)
            csv_data << row_hash
          end
        end
      end

      generate_csv_stream(csv_data)
    end

    def generate_identifier(row_hash)
      "#{row_hash['sub_id']}_#{row_hash['remote_address']}_#{row_hash['fs_adjust_installed_at']}"
    end

    def satisfy_special_condition?(row_hash)
      sub_id = row_hash['sub_id'].to_s.strip
      (['1002', '1003', '1081'].include?(sub_id) || sub_id.start_with?('AppSamurai')) && row_hash['status'].to_s.strip.casecmp('rejected').zero? && row_hash['reason'].to_s.strip == 'Abnormally Low Session Times'
    end

    def generate_csv_stream(data)
      headers = data.first&.keys || []
      
      Enumerator.new do |yielder|
        yielder << CSV::Row.new(headers, headers, true).to_s
        data.each do |row|
          yielder << CSV::Row.new(headers, row.values).to_s
        end
      end
    end

    private

    def setup_capybara
      Capybara.register_driver :cuprite do |app|
        Capybara::Cuprite::Driver.new(app, {
          window_size: [1200, 800],
          browser_options: {
            'no-sandbox': nil,
            'disable-dev-shm-usage': nil,
            'disable-gpu': nil,
            'disable-setuid-sandbox': nil
          },
          timeout: 20,
          process_timeout: 30,
          inspector: false,
          headless: true,
          js_errors: false
        })
      end
      
      Capybara.default_driver = :cuprite
      Capybara.javascript_driver = :cuprite
      Capybara.default_max_wait_time = 10
    end

    def wait_for_download(temp_dir)
      download_timeout = 60
      downloaded_file = nil
      last_size = 0
      size_stable_count = 0
      
      download_timeout.times do |i|
        sleep 1
        files = Dir.glob(File.join(temp_dir, '*'))
        
        complete_files = files.reject { |f| f.end_with?('.crdownload', '.part', '.tmp') }
        
        if complete_files.any?
          current_file = complete_files.max_by { |f| File.mtime(f) }
          
          if current_file && File.exist?(current_file)
            current_size = File.size(current_file)
            
            if current_size > 0
              Rails.logger.info "Download in progress: #{File.basename(current_file)} - #{current_size} bytes"
              
              if current_size == last_size
                size_stable_count += 1
                if size_stable_count >= 3
                  downloaded_file = current_file
                  break
                end
              else
                size_stable_count = 0
                last_size = current_size
              end
            end
          end
        end
        
        partial_files = files.select { |f| f.end_with?('.crdownload', '.part', '.tmp') }
      end
      
      unless downloaded_file && File.exist?(downloaded_file)
        files_in_dir = Dir.glob(File.join(temp_dir, '*'))
        partial_files = files_in_dir.select { |f| f.end_with?('.crdownload', '.part', '.tmp') }
        complete_files = files_in_dir - partial_files
        
        error_message = ["Download failed or timed out after #{download_timeout} seconds."]
        if partial_files.any?
          error_message << "Partial downloads found:"
          partial_files.each do |f|
            error_message << "  - #{File.basename(f)} (#{File.size(f)} bytes)"
          end
        end
        if complete_files.any?
          error_message << "Other files in directory:"
          complete_files.each do |f|
            error_message << "  - #{File.basename(f)} (#{File.size(f)} bytes)"
          end
        end
        if files_in_dir.empty?
          error_message << "No files found in download directory"
        end
        
        raise error_message.join("\n")
      end
      
      unless downloaded_file.end_with?('.zip')
        raise "Invalid file type: Expected .zip file but got #{File.basename(downloaded_file)} (#{File.size(downloaded_file)} bytes)"
      end
      
      downloaded_file
    end

    def login_and_queue_report(temp_dir)
      session = Capybara::Session.new(:cuprite)
      begin
        session.driver.browser.page.command(
          'Page.setDownloadBehavior',
          behavior: 'allow',
          downloadPath: temp_dir
        )
        
        session.visit('https://fraudshield.24metrics.com/login')
              
        session.fill_in 'email', with: ENV['METRICS24_USERNAME']
        session.fill_in 'password', with: ENV['METRICS24_PASSWORD']
        session.click_button 'Login'
        
        sleep 2

        session.visit(@url)

        sleep 5
        
        session.find('a.btn-danger', text: 'Show Advanced Settings').click
        session.find('a', text: 'Map Parameter').click
        session.find("input[placeholder='type a parameter name here e.g. fs_transaction_id']").set("fs_adjust_installed_at")
        session.find("input[value='Generate Report']").click
        
        sleep 5

        session.click_button('Queue CSV Export in the Download Center', exact: true, match: :first)
        
        sleep 300
        session.visit('https://fraudshield.24metrics.com/app#/reporting/download_center')
        sleep 5
        
        session.within('table', wait: 10) do
          rows = session.all('tbody tr')
          timeout = Time.now + 60
          target_row = nil
          
          while Time.now < timeout
            target_row = rows.find do |row|
              cols = row.all('td')
              cols[0] && cols[2].text.include?("24metrics_conversion_report_#{start_date}_#{end_date}_tracker_id_#{tracker_id}")
            end
            
            if target_row
              status = target_row.all('td')[3].text
              if status == 'Ready'
                p "Found ready target row: #{target_row.text}"
                session.within(target_row) { session.find('td:nth-child(6) a').click }
                break
              else
                p "Found target row but status is #{status}, waiting..."
                sleep 10
                session.refresh
                rows = session.within('table tbody', wait: 10) { session.all('tr') }
              end
            else
              raise "Target report not found in download center"
            end
          end
          
          if target_row && status == 'Ready'
            downloaded_file = wait_for_download(temp_dir)
          else
            raise "No ready report found in download center"
          end
        end
      ensure
        session.driver.quit
      end
    end
  end
end
