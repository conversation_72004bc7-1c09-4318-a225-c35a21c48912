# frozen_string_literal: true

module Scopely
  class GenerateAllSpendsReportCommand
    prepend SimpleCommand

    attr_reader :start_date, :end_date, :filename

    def initialize(start_date:, end_date:)
      @start_date = start_date.to_date
      @end_date = end_date.to_date
      @filename = "mg_all_spends_#{start_date}_#{end_date}"
    end

    def call
      export_service = ExportService.new(filename)
      export_service.write do |down_file|
        File.open(down_file, "w") do |f|
          csv_stream.each do |line|
            f.write(line)
          end
        end
      end

      export_service.upload_to_s3
    end

    def csv_stream
      direct_spends = DirectSpend.includes(:campaign, :vendor, :click_url, :client)
                                 .where(spend_date: start_date..end_date, client_id: Client::SCOPELY_ID)
                                 .where(vendors: { for_test: false })
                                 .where("gross_spend_cents > 0 OR net_spend_cents > 0")
                                 .order(spend_date: :desc)

      Enumerator.new do |yielder|
        yielder << CSV.generate_line(['Spend Date', 'Client', 'Vendor', 'Campaign', 'Click URL', 'Gross Spend', 'Net Spend', 'Margin', 'Data Source', 'Event Source'])
        direct_spends.each do |direct_spend|
          yielder << CSV.generate_line([
            direct_spend.spend_date,
            direct_spend.client_name,
            direct_spend.vendor_name,
            direct_spend.campaign_name,
            direct_spend.click_url_id,
            direct_spend.gross_spend,
            direct_spend.net_spend,
            direct_spend.calculated_margin,
            spend_source(direct_spend.source),
            direct_spend.event_source
          ])
        end
      end
    end

    def spend_source(source)
      case source
      when 'direct_spend_input'
        'manually entry'
      when 'v4_stat_records'
        'stat_records'
      else
        source
      end
    end
  end
end
