# frozen_string_literal: true

module Scopely
  class GenerateAllClientRateCommand
    prepend SimpleCommand

    attr_reader :start_date, :end_date, :filename

    def initialize(start_date:, end_date:)
      @start_date = start_date.to_date
      @end_date = end_date.to_date
      @filename = "mg_all_client_rate_#{start_date}_#{end_date}"
    end

    def call
      export_service = ExportService.new(filename)
      export_service.write do |down_file|
        csv_stream = ::Export::GenerateClientRateReportCommand.call(begin_date: start_date, end_date: end_date).result
        File.open(down_file, "w") do |f|
          csv_stream.each do |line|
            f.write(line)
          end
        end
      end

      export_service.upload_to_s3
    end
  end
end
