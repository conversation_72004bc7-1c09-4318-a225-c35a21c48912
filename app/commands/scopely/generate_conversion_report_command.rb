# frozen_string_literal: true

module Scopely
  class GenerateConversionReportCommand
    prepend SimpleCommand

    attr_reader :start_date, :end_date, :filename

    def initialize(start_date:, end_date:)
      @start_date = start_date.to_date
      @end_date = end_date.to_date
      @filename = "mg_scopely_conversion_records_#{start_date}_#{end_date}"
    end

    def call
      options = {
        start_date: start_date,
        end_date: end_date,
        client_id: Client::SCOPELY_ID,
        track_type: ['2'],
        campaign_ids: campaign_ids,
        export_all: true
      }
      export_columns = ['identifier', 'feedmob_install_at', 'app_id', 'campaign_name', 'click_url_id', 'conversion_id', 'vendor_name', 'remote_ip', 'status', 'track_type', 'repeated', 'ourl', 'event_time']
      cr_csv_stream = ::ConversionRecordsExportEsCommand.call(options, export_columns).result
      agency_cr_csv_stream = ::AgencyConversionRecordsExportEsCommand.call(options, export_columns).result

      export_service = ExportService.new(filename)
      export_service.write do |down_file|
        File.open(down_file, "w") do |f|
          cr_csv_stream.each do |line|
            f.write(line)
          end
          agency_cr_csv_stream.each_with_index do |line, index|
            next if index == 0
            f.write(line)
          end
        end
      end

      export_service.upload_to_s3
    end

    private

    def campaign_ids
      Campaign.unscoped.where(client_id: Client::SCOPELY_ID).map(&:id)
    end
  end
end
