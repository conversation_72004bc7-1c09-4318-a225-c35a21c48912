class ScheduleReportGenerateCommand
  prepend SimpleCommand

  attr_reader :page, :start_date, :end_date, :options, :export_daily

  def initialize(page, start_date, end_date, options = {})
    @page         = page
    @start_date   = start_date
    @end_date     = end_date
    @export_daily = options[:export_daily]
    @options      = options
  end

  def call
    raise "We can not export the page:#{page}" unless export_pages.include?(page)
    if page.include?('+')
      send "export_campaigns_vendor"
    else
      send "export_#{page}"
    end
  end

  private

  def export_campaigns_vendor
    if export_daily?
      ScheduleReport::CampaignsVendorDailyExportCommand.call(start_date, end_date, conversion, v2_options).result
    else
      ScheduleReport::CampaignsVendorIndexExportCommand.call(start_date, end_date, conversion, v2_options).result
    end
  end

  def export_campaigns
    if export_daily?
      ScheduleReport::CampaignsDailyExportCommand.call(start_date, end_date, conversion, v2_options).result
    else
      ScheduleReport::CampaignsIndexExportCommand.call(start_date, end_date, conversion, v2_options).result
    end
  end

  def export_vendors
    if export_daily?
      ScheduleReport::VendorsDailyExportCommand.call(start_date, end_date, conversion, v2_options).result
    else
      ScheduleReport::VendorsIndexExportCommand.call(start_date, end_date, conversion, v2_options).result
    end
  end

  def export_clients
    if export_daily?
      ScheduleReport::ClientsDailyExportCommand.call(start_date, end_date, conversion, v2_options).result
    else
      ScheduleReport::ClientsIndexExportCommand.call(start_date, end_date, conversion, v2_options).result
    end
  end

  def export_sources
    ScheduleReport::SourcesIndexExportCommand.call(start_date, end_date, conversion, v2_options)
  end

  def export_caps
    ScheduleReport::CapsExportCommand.call(start_date, end_date, conversion, v2_options).result
  end

  def export_daily?
    !!@export_daily
  end

  def conversion
    @conversion = options[:conversion] || 'purchase'
  end

  def v2_options
    options.merge(conversion: conversion)
  end

  def export_pages
    ScheduleExportTemplate.export_pages.keys.map(&:downcase)
  end
end
