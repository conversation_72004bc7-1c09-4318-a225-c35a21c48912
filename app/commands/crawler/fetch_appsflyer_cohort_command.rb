require 'json'
require 'capybara'
require 'capybara/cuprite'
require 'capybara/dsl'

module Crawler
  class FetchAppsflyerCohortCommand
    prepend SimpleCommand
    include Capybara::DSL

    attr_reader :start_date, :end_date, :af_app_id, :params
  
    LOGIN_URL = 'https://hq1.appsflyer.com/auth/login'.freeze
    COHORT_DATA_URL = 'https://hq1.appsflyer.com/cohort/data'.freeze

    def initialize(start_date:, end_date:, params:, af_app_id:)
      @start_date = start_date.to_date
      @end_date = end_date.to_date
      @af_app_id = af_app_id
      @params = params
    end

    def call
      browser_response = fetch_cohort_data_via_browser
      
      if browser_response.present? && !errors.any?
        return browser_response
      end
    end

    private

    def fetch_cohort_data_via_browser      
      begin
        init_driver
        login_to_appsflyer
        response = make_cohort_data_request
        
        begin
          JSON.parse(response)
        rescue JSON::ParserError => e
          errors.add(:base, "Failed to parse browser response: #{e.message}")
          nil
        end
      rescue => e
        errors.add(:base, "Error fetching cohort data via browser: #{e.message}")
        nil
      ensure
        Capybara.reset_sessions!
      end
    end

    def init_driver
      Capybara.javascript_driver = :cuprite
      Capybara.register_driver(:cuprite) do |app|
        browser_options = {
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36'
        }
        Capybara::Cuprite::Driver.new(app, browser_options: browser_options)
      end

      Capybara.configure do |config|
        config.default_driver = :cuprite
        config.default_max_wait_time = 10
      end
    end

    def login_to_appsflyer
      account = ENV['APPSFLYER_ACCOUNT']
      password = ENV['APPSFLYER_PASSWORD']
      
      visit(LOGIN_URL)
      Capybara.asset_host = LOGIN_URL
      
      fill_in 'username', with: account
      fill_in 'password', with: password
      find('button[data-qa-id="submit-button"]').click
      
      sleep 5
    end

    def make_cohort_data_request
      page.execute_script(<<~JS)
        return new Promise((resolve, reject) => {
          fetch('#{COHORT_DATA_URL}', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(#{params.to_json}),
            credentials: 'include'
          })
          .then(response => response.json())
          .then(data => resolve(JSON.stringify(data)))
          .catch(error => reject(error.toString()));
        });
      JS
    end
  end
end