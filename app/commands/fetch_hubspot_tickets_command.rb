require 'httparty'
class FetchHubspotTicketsCommand
  prepend SimpleCommand

  PROJECT_MANAGEMENT_TICKETS_PIPELINE_ID = 83020039
  HUBSPOT_TICKET_URL_PREFIX = 'https://app.hubspot.com/contacts/2992171/record/0-5/'
  GITHUB_REPO = 'feed-mob/feedmob'
  STATUS = {
    '155635343' => 'Queue',
    '155635344' => 'In Progress',
    '155635345' => 'Completed',
    '155635346' => 'Parked',
  }
  GITHUB_STATUS = [
    'backlog',
    'Inquiry & Discussion',
    'in progress',
    'Code Review',
    'PA Stage Verify Needed',
    'enhancement needed',
    'PR',
    'Self Check',
    'Dev Verify',
    'PA Production Verify',
    'QA',
    'finished',
    'Watch',
    'hold',
    'invalid'
  ]

  attr_reader :date

  def initialize(date: Date.yesterday)
    @date = date.to_date
  end

  def call
    fetch_tickets.each do |ticket|
      save_ticket(ticket)
    end
  end

  private
  
  def fetch_tickets
    options = {
      limit: 100,
      after: nil,
      properties: ['id', 'createdate', 'subject', 'hs_pipeline_stage', 'priority__advanced_'],
      filterGroups: [{
        filters: [
          { propertyName: 'hs_pipeline', operator: 'EQ', value: PROJECT_MANAGEMENT_TICKETS_PIPELINE_ID },
          { propertyName: 'createdate', operator: 'GTE', value: date.beginning_of_day.to_i * 1000 }
        ]
      }]
    }
    response = HubSpot::Client.post('/crm/v3/objects/tickets/search', body: options.to_json)

    if response.success?
      response.parsed_response['results'] || []
    else
      Rails.logger.error "#{self.class.name} Error fetching tickets: #{response.body}"
      []
    end
  end

  def save_ticket(ticket_data)
    properties = ticket_data['properties']
    subject = properties['subject']
    github_issue_number = extract_github_issue_number(subject)
    github_ticket_link, github_ticket_status, pa_users = fetch_github_issue_details(github_issue_number) if github_issue_number

    ticket = HubspotTicket.find_or_initialize_by(hubspot_id: ticket_data['id'])
    ticket.title = subject
    ticket.link = "#{HUBSPOT_TICKET_URL_PREFIX}#{ticket_data['id']}"
    ticket.priority = properties['priority__advanced_']
    ticket.createdate = properties['createdate']
    ticket.status = STATUS[properties['hs_pipeline_stage']]
    ticket.github_ticket_link = github_ticket_link
    ticket.github_ticket_status = github_ticket_status
    ticket.pa_users = pa_users || []
    ticket.save
  end

  def extract_github_issue_number(subject)
    match = subject.match(/#(\d+)/)
    match[1] if match
  end

  def fetch_github_issue_details(issue_number)
    return [nil, nil] unless issue_number

    url = "https://api.github.com/repos/#{GITHUB_REPO}/issues/#{issue_number}"
    headers = {
      'User-Agent' => 'Ruby',
      'Authorization' => "token #{ENV['GITHUB_ACCESS_TOKEN']}"
    }

    response = HTTParty.get(url, headers: headers)
    if response.success?
      json = response.parsed_response
      github_ticket_status = json['labels'].map{|r| r['name'] if GITHUB_STATUS.include?(r['name'])}.compact.first
      participants = ParticipantsCommand.call(transform_symbol(json['title'])).result
      [json['html_url'], github_ticket_status, participants[:v] || []]
    else
      Rails.logger.error "Failed to fetch GitHub issue ##{issue_number}: #{response.body}"
      [nil, nil]
    end
  end

  def transform_symbol(title)
    title.gsub("【", "[").gsub("】", "]")
  end
end
