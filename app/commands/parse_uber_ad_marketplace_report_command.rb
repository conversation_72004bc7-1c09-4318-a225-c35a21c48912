# frozen_string_literal: true

require 'csv'
require 'zip'

# 从QA的收到的Gmail中获取UberAdMarketplace的 Net Spend 数据
class ParseUberAdMarketplaceReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'
  ADMARKETPLACE_QUERY = 'from:<EMAIL> newer_than:10d has:attachment filename:zip subject:UberEats---adMarketplace-daily-L30-data.csv'
  ADMARKETPLACE_ID = 498.freeze  

  attr_reader :user_id, :start_date, :end_date, :gmail, :warnings

  def initialize(start_date, end_date)
    @start_date = start_date
    @end_date = end_date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
    @warnings = []
  end

  def call
    parse_email
  end

  private

  def parse_email
    user_messages = gmail.list_user_messages(user_id, q: ADMARKETPLACE_QUERY)
    if user_messages.messages.present?
      @message = user_messages.messages.select do |m|
        email = gmail.get_user_message user_id, m.id
        email.snippet.include?('adMarketplace Report')
      end.first
    end

    if @message.blank?
      errors.add(:invalid, '*No Email In Last One Day Received*')
      []
    else
      analyze_email(gmail: gmail, user_id: user_id, message: @message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    select_datas = []
    Zip::File.open_buffer(attachment_content.data) do |zip|
      entry = zip.first
      content = entry.get_input_stream.read
      data = CSV.parse(content, headers: true).map(&:to_h)
      select_datas = data.select { |row| row['RPT_Date'].to_date <= end_date && row['RPT_Date'].to_date >= start_date }

      errors.add(:invalid, 'No Data') if select_datas.blank?
    end

    unless select_datas.blank?
      processed_data = process_data(select_datas)
      return processed_data
    end
    []
  end

  def process_data(data)
    reporting_campaign_name_mapping = ClickUrl.reporting_campaign_name_mapping_by(ADMARKETPLACE_ID)
    result = {}
    data.each do |row|
      rpt_date = row['RPT_Date']
      adv_campaign = row['Adv_Campaign']
      spend = row['Spend'].to_f

      click_url_id = extract_click_url_id(adv_campaign)
      if click_url_id.nil?
        campaign_name = adv_campaign.split('&').first.strip
        click_url_id = reporting_campaign_name_mapping[campaign_name]  
      end

      if click_url_id.nil?
        warnings << "#{reporting_campaign_name}"
        next
      end
      result[rpt_date] ||= {}
      result[rpt_date][click_url_id.to_s] ||= 0
      result[rpt_date][click_url_id.to_s] += spend
    end

    if warnings.present?
      message = "No Click URL found for admarketplace uploaded csv:\n#{warnings.uniq.join("\n")}"
      SlackService.send_notification_to_channel(message, :mighty)
    end

    result
  end

  def extract_click_url_id(adv_campaign)
    parts = adv_campaign.split('_')
    last_part = parts.last
    return last_part.to_i if last_part.match?(/^\d{5}$/)
    nil
  end
end
