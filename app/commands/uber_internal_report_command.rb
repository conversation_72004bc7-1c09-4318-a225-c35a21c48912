# frozen_string_literal: true

class UberInternalReportCommand < UberSpendReportCommand
  prepend SimpleCommand
  include ErbBinding

  CLIENT_NAME = 'Uber Internal'

  RESULT_ATTRIBUTES = [
    :click_url_id,
    :report_date,
    :client_id,
    :campaign_id,
    :publisher_name,
    :language_country,
    :currency_code,
    :spends,
    :impressions,
    :clicks,
    :installs,
    :purchases,
    :agency_purchases,
    :signups,
    :net_spends,
    :billing_type,
    :os_targeted,
    :objective_name,
    :campaign_name,
    :product_name,
    :country_id,
    :city_id,
    :geo
  ].freeze

  Result = Struct.new(*RESULT_ATTRIBUTES)

  attr_reader :start_date, :end_date, :client_id, :client, :filename, :opts
  attr_accessor :mismatch_mapping

  def initialize(start_date:, end_date:, opts: {}, **_)
    @start_date = start_date.to_date
    @end_date   = end_date.to_date
    @filename   = "SeaPod_install_and_event_#{start_date}-#{end_date}"
    @client_id  = Client.where(name: 'Uber Technologies').pluck(:id)
    @client     = Client.find_by(name: 'Uber Technologies')
    @opts       = opts
    @mismatch_mapping = []
  end

  def call
    export_service = ExportService.new(filename, zipping: true)
    csv_stream = report_csv_stream.strip
    export_service.write do |down_file|
      down_file.write(csv_stream)
    end
    download_url = export_service.upload_to_s3

    {pod: 'Seapod', daily_url: download_url}
  end

  def client_name
    CLIENT_NAME
  end

  def data
    @data ||= begin
      results = ConversionRecordRedshift.connection.query(sql).map{|r| [[r[0], r[1].to_i, r[2].to_i, r[3], r[4], r[5]], r]}.to_h
      mmp_results = AggregatedStatsBase.connection.query(mmp_sql).map{|r| [[r[0], r[1].to_i, r[2].to_i, r[3], r[4], r[5]], r]}.to_h
      agency_api_results = ConversionRecordRedshift.connection.query(agency_api_sql).map{|r| [[r[0], r[1].to_i, r[2].to_i, r[3], r[4], r[5]], r]}.to_h
      net_spends_res = ClickUrl.connection.query(net_spends_sql).map{|r| [[r[0], r[1].to_i, r[2].to_i, r[3], r[4], r[5]], r]}.to_h
      d = (results.keys + net_spends_res.keys + mmp_results.keys + agency_api_results.keys).uniq.map do |key|
        spend = (results[key] ? results[key][6].to_f : 0) + (net_spends_res[key] ? net_spends_res[key][-2].to_f : 0)
        impressions = (results[key] ? results[key][7].to_i : 0) + (mmp_results[key] ? mmp_results[key][7].to_i : 0)
        clicks = (results[key] ? results[key][8].to_i : 0) + (mmp_results[key] ? mmp_results[key][8].to_i : 0)
        installs = (results[key] ? results[key][9].to_i : 0) + (mmp_results[key] ? mmp_results[key][9].to_i : 0)
        purchases = results[key] ? results[key][10].to_i : 0
        agency_purchases = agency_api_results[key] ? agency_api_results[key][6].to_i : 0
        signups = (results[key] ? results[key][11].to_i : 0) + (mmp_results[key] ? mmp_results[key][11].to_i : 0)
        net_spend = (results[key] ? results[key][12].to_f : 0) + (net_spends_res[key] ? net_spends_res[key][-1].to_f : 0)

        click_url_id = get_click_url_id(key[2], key[3])
        row = [click_url_id] + key + [spend, impressions, clicks, installs, purchases, agency_purchases, signups, net_spend]

        record = Result.new(*row)
        next if record.spends.to_f.zero? && record.net_spends.to_f.zero? && record.impressions.to_i.zero? && record.clicks.to_i.zero? && record.installs.to_i.zero? && record.purchases.to_i.zero? && record.agency_purchases.to_i.zero? && record.signups.to_i.zero?
        value_hash = uber_eats_mappings[[record.campaign_id.to_i, Vendor.cached_names.find{|vendor_id,vendor_name| vendor_name == record.publisher_name}&.first]]
        if value_hash.blank?
          mismatch_mapping << {key: record.campaign_id, value: Campaign.cached_names[record.campaign_id.to_i]}
          value_hash = MAPPING[record.client_id.to_i]
        end

        next if value_hash.blank?

        record.campaign_id    = value_hash[:campaign_id]
        record.campaign_name  = value_hash[:campaign_name]
        record.product_name   = value_hash[:product_name]
        record.objective_name = value_hash[:objective_name]
        record.billing_type   = value_hash[:billing_type]
        record.os_targeted    = value_hash[:os_targeted]
        record.country_id     = value_hash[:country_id]
        record.city_id        = value_hash[:city_id]
        record.geo            = value_hash[:campaign_name][/_[A-Z]{2}-/, 0]&.gsub('_', '')&.gsub('-', '')
        record
      end.compact
      d
    end
  end

  def get_click_url_id(campaign_id, vendor_name)
    click_url = all_uber_click_url_ids.find {|c| c[:campaign_id] == campaign_id && c[:vendor_name] == vendor_name && c[:status].in?(['active', 'cu_normal']) }
    click_url = all_uber_click_url_ids.find {|c| c[:campaign_id] == campaign_id && c[:vendor_name] == vendor_name && c[:status] == 'paused' } if click_url.blank?
    click_url = all_uber_click_url_ids.find {|c| c[:campaign_id] == campaign_id && c[:vendor_name] == vendor_name && c[:status] == 'archived' } if click_url.blank?
    click_url.to_h[:click_url_id]
  end

  def all_uber_click_url_ids
    @all_uber_click_url_ids ||= Rails.cache.fetch('all_uber_click_url_ids', expires_in: 5.minutes) do
      uber_click_url_ids = ClickUrl.includes(:campaign, :vendor).where(campaign_id: campaign_ids, vendor_id: vendor_ids).where.not(campaign_id: exclude_campaign_ids)
      uber_click_url_ids.map {|click_url| {click_url_id: click_url.id, status: click_url.status, campaign_id: click_url.campaign_id, vendor_name: click_url.vendor.vendor_name} }
    end
  end

  def sql
    view_sql = File.read('app/data_views/uber_internal_report_view.sql')
    ERB.new(view_sql).result(binding_from_hash(conditions: conditions, arbitrage_click_urls: client.arbitrage_click_url_ids))
  end

  def mmp_sql
    @mmp_sql ||= <<-SQL
      SELECT
        calculate_date                            AS report_date,
        c.client_id                               AS client_id,
        ar.campaign_id                            AS campaign_id,
        ar.vendor_name                            AS publisher_name,
        'en-us'                                   AS language_country,
        'USD'                                     AS currency_code,
        SUM(spend)                                AS spends,
        SUM(impression_count)                     AS impressions,
        SUM(click_count)                          AS clicks,
        SUM(install_count)                        AS install_count,
        SUM(purchase_count)                       AS purchase_count,
        SUM(registration_count)                   AS signups,
        SUM(net_spend)                            AS net_spends
      FROM agency_records as ar
      LEFT JOIN core_main.campaigns c on c.id = ar.campaign_id
      #{agency_conditions}
      GROUP BY 1,2,3,4
      ORDER BY 1
    SQL
  end

  def agency_api_sql
    @agency_api_sql ||= <<-SQL
      SELECT
        calculate_date                                AS report_date,
        '169'                                         AS client_id,
        ar.campaign_id                                AS campaign_id,
        ar.vendor_name                                AS vendor_name,
        'en-us'                                       AS language_country,
        'USD'                                         AS currency_code,
        SUM(first_purchase_count)                     AS first_purchase_count
      FROM agency_stat_records as ar
      #{agency_conditions}
      GROUP BY 1,2,3,4
      ORDER BY 1
    SQL
  end

  def net_spends_sql
    @net_spends_sql ||= <<-SQL
      SELECT
        spend_date                                AS report_date,
        c.client_id                               AS client_id,
        ns.campaign_id                            AS campaign_id,
        v.vendor_name                             AS publisher_name,
        'en-us'                                   AS language_country,
        'USD'                                     AS currency_code,
        SUM(installs_count)                       AS installs_count,
        SUM(gross_spend_cents)/100.0              AS gross_spends,
        SUM(net_spend_cents)/100.0                AS net_spends
      FROM net_spends ns
      LEFT JOIN campaigns c on c.id = ns.campaign_id
      LEFT JOIN vendors v on v.id = ns.vendor_id
      #{net_spend_conditions}
      GROUP BY 1,2,3,4
      ORDER BY 1
    SQL
  end

  def generate_csv_stream(results)
    CSV.generate(headers: true) do |csv|
      csv << csv_header
      results.each do |result|
        csv_row = []
        csv_row << result.report_date
        csv_row << result.click_url_id
        csv_row << result.campaign_id
        csv_row << result.campaign_name
        csv_row << result.publisher_name.gsub(' ', '_')
        csv_row << result.product_name
        csv_row << result.objective_name
        csv_row << result.billing_type
        csv_row << result.os_targeted
        csv_row << result.language_country
        csv_row << result.country_id
        csv_row << result.city_id
        csv_row << result.spends
        csv_row << result.currency_code
        csv_row << result.impressions
        csv_row << result.clicks
        csv_row << result.installs
        csv_row << result.purchases
        csv_row << result.agency_purchases
        csv_row << result.signups
        csv_row << result.net_spends
        csv_row << result.geo
        csv << csv_row
      end
      csv
    end
  end

  def csv_header
    ['report_date', 'click_url_id', 'campaign_id', 'campaign_name', 'publisher_name', 'product_name', 'objective_name', 'billing_type', 'os_targeted', 'language_country', 'country_id', 'city_id', 'spend_local', 'currency_code', 'impressions', 'clicks', 'installs', 'fe_first_purchases', 'agency_first_purchases', 'signups', 'net_spends', 'geo']
  end
end
