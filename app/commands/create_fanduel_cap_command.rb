# require "csv"

class CreateFanduelCapCommand
  prepend SimpleCommand

  attr_accessor :date

  CLIENT_ID = 89 # FanDuel

  def initialize(options = {})
    @date = options[:date]
  end

  def call
    data = generate_data
    generate_csv_stream(data)
  end

  def generate_file_name
    "FeedMob - fanduel_target_cap_#{date}.csv"
  end

  def generate_subject
    "Fanduel target cap daily report - #{date}"
  end

  def generate_content
    @email_content ||= <<-HTML
      Please find the attached file

      Thanks!
    HTML
  end

  private

  def generate_csv_stream(results)
    CSV.generate(headers: true) do |csv|
      csv << headers
      results.each do |result|
        csv << result
      end
      csv
    end
  end

  def generate_data
    click_urls = []
    campaign_ids = Campaign.where(client_id: CLIENT_ID).pluck(:id)

    ## active click url
    click_urls = click_urls.concat ClickUrl.where(campaign_id: campaign_ids).where(status: ['cu_normal', 'active']).includes(:vendor, :campaign).order('id desc')

    ## pause and archive
    click_urls = click_urls.concat ClickUrl.where('updated_at >= ?', date).where(campaign_id: campaign_ids).where(status: ['paused', 'archived']).includes(:vendor, :campaign).order('id desc')

    result = []
    click_urls.each do |click_url|
      row = []
      row << click_url.id
      row << click_url.campaign&.name
      row << click_url.vendor&.vendor_name
      tip = is_after_date(click_url) ? get_tip(click_url) : get_cap(click_url.revision_at(date.end_of_day))
      row << tip
      result << row
    end
    result
  end

  def headers
    result = []
    result << "Click Url ID"
    result << "Campaign"
    result << "Vendor"
    result << date
    result
  end

  def is_after_date(click_url)
    return click_url.present? && click_url.created_at > date.end_of_day
  end

  def get_tip(click_url)
    if click_url.present?
      return "Created on #{click_url.created_at.strftime("%m/%d/%Y")}"
    end
    return ""
  end

  def get_cap(click_url)
    if click_url.present?
      return ['cu_normal', 'active'].include?(click_url.status) ? click_url.advertiser_cap : click_url.status
    end
    return ""
  end
end
