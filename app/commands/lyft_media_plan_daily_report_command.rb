class LyftMediaPlanDailyReportCommand
  LYFT_MEDIA_PLAN_SECRET_ID = ENV['SINGULAR_LYFT_MEDIA_PLAN_SECRET_ID']
  
  PASSENGER_ACTIVATION_KEY_HASH = "25a8e625c84a4c799af469778f4cb976"
  PASSENGER_SIGNUP_KEY_HASH = "9faacf81022f4521a795b59f809bf7f3"

  SPREADSHEET_ID = "1D_018jY7YuAL1NbSpfnLteJMR9vT-Mzr9D1SwHK4RZo"
  # Local testing
  # SPREADSHEET_ID = "1QwTddi3erGIHyZofYgx4eahQkcGIp4aCtsJYjXw6vsY"
  CLICK_URL_ID = 20597

  def initialize(date)
    @date = date.to_date
    @service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    clicks = report.sum { |row| row["custom_clicks"] }
    installs = report.sum { |row| row["custom_installs"] }
    activations = report.sum { |row| row.dig(PASSENGER_ACTIVATION_KEY_HASH, "actual") }
    signups = report.sum { |row| row.dig(PASSENGER_SIGNUP_KEY_HASH, "actual") }

    date = @date.strftime("%m/%d/%Y")
    @service.update_or_append_row("Campaign Performance", 0, date, [
      date, # Date
      clicks.to_i, # Singular Clicks
      installs.to_i, # Singular Installs
      "=C{{ROW}}/B{{ROW}}", # Singular cvr (click - install)
      "", # (Ignore) Samsung Installs
      "", # (Ignore) Installs Delta (E{{ROW}} - C{{ROW}}) / E{{ROW}}
      activations.to_i, # Passenger Activation - Singular
      signups.to_i, # Passenger Signup - Singular
      "$#{spend.round(2)}", # Spend
    ], exclude_columns: [4, 5])
    @service.center_align_sheet("Campaign Performance")
  end

  private

  def report
    @report ||= saved_report_service.load(
      start_date: @date,
      end_date: @date
    )
  end

  def spend
    NetSpend.where(
      spend_date: @date,
      click_url_id: CLICK_URL_ID
    ).sum(:gross_spend_cents) / 100.0
  end

  def saved_report_service
    # Report name Lyft Samsung
    @saved_report_service ||= SingularSavedReportService.new(
      <<~CURL
      curl 'https://app.singular.net/api/get_anonymous_data?secret_id=#{LYFT_MEDIA_PLAN_SECRET_ID}&report_password=&query=%7B%22compare%22:false,%22is_goals%22:false,%22dimensions%22:\[%22app%22,%22os%22,%22source%22,%22sub_campaign_id%22,%22sub_campaign_name%22,%22unified_campaign_id%22,%22unified_campaign_name%22\],%22metrics%22:\[%22adn_cost%22,%22custom_clicks%22,%22custom_installs%22\],%22discrepancy_metrics%22:\[\],%22file_combiner_dimensions%22:\[\],%22granularity_levels%22:\[\],%22enrichment_dimensions%22:\[\],%22source_dimensions%22:\[\],%22metadata_dimensions%22:\[\],%22skan_conversion_dimensions%22:\[\],%22cohort_metrics%22:\[%22revenue%22,%22roi%22,%2225a8e625c84a4c799af469778f4cb976%22,%229faacf81022f4521a795b59f809bf7f3%22\],%22skan_modeled_cohort_metrics%22:\[\],%22modeled_skan_custom_events%22:\[\],%22data_type_fields%22:\[\],%22metric_group_fields%22:\[\],%22cohort_metric_group_fields%22:\[\],%22cohort_periods%22:\[\],%22skan_modeled_cohort_periods%22:\[\],%22unified_cohort_periods%22:\[\],%22goals_metrics%22:\[\],%22goals_forecast_metrics%22:\[\],%22filters%22:\[%7B%22dimension%22:%22app%22,%22operator%22:%22in%22,%22values%22:\[%22041297a9-d9cf-4e38-9776-280ee94a89f3%22\],%22options%22:%7B%22no_lowercase%22:true%7D%7D,%7B%22dimension%22:%22source%22,%22operator%22:%22in%22,%22values%22:\[%22feebmob_agency%22\],%22options%22:%7B%22no_lowercase%22:true%7D%7D,%7B%22dimension%22:%22os%22,%22operator%22:%22in%22,%22values%22:\[4\],%22options%22:%7B%22no_lowercase%22:true%7D%7D\],%22time_breakdown%22:\[%22day%22\],%22source_attribution_type%22:\[\],%22with_append_tables%22:\[\],%22cross_device_cohort_type%22:\[\],%22cross_device_demo_cohort_type%22:\[\],%22start_date%22:%222024-10-16%22,%22start_date_2%22:%222024-10-15%22,%22end_date%22:%222024-10-16%22,%22end_date_2%22:%222024-10-15%22,%22goal%22:null,%22display_unenriched%22:true,%22display_admon_alignment%22:false,%22skan_redownloads_dimensions%22:\[\],%22skan_validated_dimensions%22:\[\],%22confidence_interval_flag%22:\[\],%22bookmark_id%22:%22%22,%22bookmark_creator%22:%22%22,%22updatedInstanceId%22:%22%22,%22is_fraud%22:false,%22is_skan_summary%22:false,%22is_unified_report%22:false,%22is_skan%22:false,%22is_admon_report%22:false,%22pivot_table%22:%7B%7D,%22query_type%22:%22charts%22,%22is_slim_mode%22:false,%22permutation_keys%22:\[%7B%22name%22:%22app%22,%22display_name%22:%22App%22,%22is_default%22:true,%22is_admon_default%22:true,%22visible%22:true,%22skan_visible%22:true,%22slim_mode_visible%22:true,%22creative_visible%22:true,%22fraud_visible%22:true,%22ad_monetization_visible%22:true,%22cross_device_visible%22:true,%22skan_summary_visible%22:true,%22unified_report_visible%22:true,%22tooltip%22:%22The+app+name+as+defined+in+the+Apps+page%22,%22id%22:14,%22display_format%22:null,%22sorting_id%22:null,%22type%22:%22dimension%22%7D,%7B%22name%22:%22source%22,%22display_name%22:%22Source%22,%22is_default%22:true,%22visible%22:true,%22skan_visible%22:true,%22slim_mode_visible%22:true,%22fraud_visible%22:true,%22creative_visible%22:true,%22ad_monetization_visible%22:false,%22cross_device_visible%22:true,%22skan_summary_visible%22:true,%22unified_report_visible%22:true,%22standard_analytics_visible%22:true,%22tooltip%22:%22The+partner+or+network+name%22,%22id%22:18,%22display_format%22:null,%22sorting_id%22:1,%22type%22:%22dimension%22%7D,%7B%22name%22:%22os%22,%22display_name%22:%22OS%22,%22is_default%22:false,%22is_admon_default%22:true,%22visible%22:true,%22slim_mode_visible%22:true,%22fraud_visible%22:true,%22creative_visible%22:true,%22ad_monetization_visible%22:true,%22cross_device_visible%22:true,%22standard_analytics_visible%22:true,%22id%22:21,%22display_format%22:null,%22sorting_id%22:null,%22type%22:%22dimension%22%7D,%7B%22name%22:%22unified_campaign_name%22,%22display_name%22:%22Campaign+Name%22,%22is_default%22:false,%22visible%22:true,%22slim_mode_visible%22:true,%22creative_visible%22:true,%22fraud_visible%22:true,%22skan_visible%22:true,%22skan_summary_visible%22:true,%22cross_device_visible%22:true,%22unified_report_visible%22:true,%22standard_analytics_visible%22:true,%22id%22:30,%22display_format%22:null,%22sorting_id%22:null,%22type%22:%22dimension%22%7D,%7B%22name%22:%22unified_campaign_id%22,%22display_name%22:%22Campaign+ID%22,%22is_default%22:false,%22visible%22:true,%22slim_mode_visible%22:true,%22cross_device_visible%22:true,%22creative_visible%22:true,%22fraud_visible%22:true,%22skan_visible%22:true,%22skan_summary_visible%22:true,%22unified_report_visible%22:true,%22standard_analytics_visible%22:true,%22id%22:214,%22display_format%22:null,%22sorting_id%22:null,%22type%22:%22dimension%22%7D,%7B%22name%22:%22sub_campaign_id%22,%22display_name%22:%22Sub+Campaign+ID%22,%22is_default%22:false,%22visible%22:true,%22slim_mode_visible%22:true,%22cross_device_visible%22:true,%22creative_visible%22:true,%22fraud_visible%22:true,%22skan_summary_visible%22:true,%22standard_analytics_visible%22:true,%22tooltip%22:%22An+additional+breakdown+that+some+partners+have.+Corresponds+to+the+ad+set+in+Facebook+and+the+ad+group+in+Google%22,%22id%22:215,%22display_format%22:null,%22sorting_id%22:null,%22type%22:%22dimension%22%7D,%7B%22name%22:%22sub_campaign_name%22,%22display_name%22:%22Sub+Campaign+Name%22,%22is_default%22:false,%22visible%22:true,%22slim_mode_visible%22:true,%22cross_device_visible%22:true,%22creative_visible%22:true,%22fraud_visible%22:true,%22skan_summary_visible%22:true,%22standard_analytics_visible%22:true,%22tooltip%22:%22An+additional+breakdown+that+some+partners+have.+Corresponds+to+the+ad+set+in+Facebook+and+the+ad+group+in+Google%22,%22id%22:216,%22display_format%22:null,%22sorting_id%22:null,%22type%22:%22dimension%22%7D\],%22chart%22:true,%22valid_key_list_for_charts%22:\[\[%22Lyft%2520Passenger%22,%22Feedmob%2520Agency%22,%22Android%22,%22PAX_ACQ_SEARCH_US_ANDROID_NEW_FEEDMOB%22,%2220597%22,%22N%252FA%22,%22N%252FA%22\]\],%22valid_key_name_list_for_charts%22:\[%22app%22,%22source%22,%22os%22,%22unified_campaign_name%22,%22unified_campaign_id%22,%22sub_campaign_id%22,%22sub_campaign_name%22\]%7D' \
        -H 'accept: application/json, text/plain, */*' \
        -H 'accept-language: en-US,en;q=0.9' \
        -H 'content-type: application/json;charset=UTF-8' \
        -H 'cookie: _ga=GA1.2.573901021.1726122621; singular_logged_in=True; intercom-device-id-aqks926j=06edbdc9-814c-4069-8817-e67c8e7c9408; fs_uid=#5R3DR#eed16985-d5c4-4e22-a2dc-09b94ab4af70:f85d1fad-200f-44a0-97d7-6f3a53342332:1729588916648::2#/1757658692; _ga_MTHFB9V9LE=GS1.2.1729752632.2.0.1729752632.60.0.0; csrftoken=saQwEAfDpuhNuUhIdh4RBxhHmE4Ahkg2PIqzmFgnII9K9vrKABKKy3osYzkVCfKm; sessionid=e0zrs48ohf57bos3dzgf1wdk4jo5yrin; singular_user=<EMAIL>; _gid=GA1.2.431273912.1729846447; _ga_81VEH2V9N5=GS1.2.1729846447.10.1.1729846679.60.0.0; intercom-session-aqks926j=ZDhNeGtrK1ZFL2V2OG9Nc2crQXZoS2ljNHBJM2Y0bkRjbjYxNHp5a0tFaXRaMkxxUFNhb2NYTzRPSzQ3dTE4Ui0tcFdwdm5Hb01Jb1RoQWJpcFovMXN5UT09--3b40e20e9e8f24a67ea85831bacd075aa074934f; mp_5b0e4a3604343d3334a6f6dcfd748ce4_mixpanel=%7B%22distinct_id%22%3A%20%22vanessa%2Bagency%40feedmob.com%22%2C%22%24device_id%22%3A%20%22191e4ecf7c0ef7-031f1bf40b328a-17525637-1fa400-191e4ecf7c0ef7%22%2C%22%24search_engine%22%3A%20%22google%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fwww.google.com%2F%22%2C%22%24initial_referring_domain%22%3A%20%22www.google.com%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%2C%22%24user_id%22%3A%20%22vanessa%2Bagency%40feedmob.com%22%2C%22email%22%3A%20%22vanessa%2Bagency%40feedmob.com%22%2C%22url%22%3A%20%22https%3A%2F%2Fapp.singular.net%2Freact%2Fanonymous2%2F%3Fsecret_id%3Dae9c076a19489a2092149a7c3e126f43c0d241b49ddf39acd5e71c228d866775%23%2Freact%2Fanonymous%2Fae9c076a19489a2092149a7c3e126f43c0d241b49ddf39acd5e71c228d866775%2F%22%2C%22clean_url%22%3A%20%22https%3A%2F%2Fapp.singular.net%2Freact%2Fanonymous2%2F%22%2C%22host%22%3A%20%22app.singular.net%22%2C%22org%22%3A%********%2C%22org_creation_date%22%3A%20%22June%2021%2C%202023%2C%202%3A08%20a.m.%22%2C%22active_organization%22%3A%20%22feedmob%22%2C%22user_product%22%3A%20%22unified%22%2C%22tier%22%3A%20%22ENTERPRISE%22%2C%22is_trial%22%3A%20%22False%22%2C%22is_midmarket%22%3A%20false%2C%22is_web%22%3A%20%22False%22%2C%22is_self_serve_signup%22%3A%20false%2C%22permissions_role%22%3A%20%22Admin%22%2C%22is_partner%22%3A%20%22False%22%2C%22event_source%22%3A%20%22frontend%22%2C%22is_internal_test_account%22%3A%20%22False%22%2C%22is_agency%22%3A%20%22True%22%2C%22sfdc_account_id%22%3A%20null%2C%22FullStory%20Session%22%3A%20%22https%3A%2F%2Fapp.fullstory.com%2Fui%2F5R3DR%2Fclient-session%2Feed16985-d5c4-4e22-a2dc-09b94ab4af70%253Af85d1fad-200f-44a0-97d7-6f3a53342332%3Fintegration_src%3Dmixpanel%22%2C%22user%22%3A%20%22vanessa%2Bagency%40feedmob.com%22%7D' \
        -H 'origin: https://app.singular.net' \
        -H 'priority: u=1, i' \
        -H 'referer: https://app.singular.net/react/anonymous2/?secret_id=#{LYFT_MEDIA_PLAN_SECRET_ID}' \
        -H 'sec-ch-ua: "Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"' \
        -H 'sec-ch-ua-mobile: ?0' \
        -H 'sec-ch-ua-platform: "macOS"' \
        -H 'sec-fetch-dest: empty' \
        -H 'sec-fetch-mode: cors' \
        -H 'sec-fetch-site: same-origin' \
        -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36' \
        -H 'x-browser-location: https://app.singular.net/react/anonymous2/?secret_id=#{LYFT_MEDIA_PLAN_SECRET_ID}#/react/anonymous/#{LYFT_MEDIA_PLAN_SECRET_ID}/' \
        -H 'x-csrftoken: saQwEAfDpuhNuUhIdh4RBxhHmE4Ahkg2PIqzmFgnII9K9vrKABKKy3osYzkVCfKm' \
        -H 'x-org: feedmob' \
        --data-raw '{}'
      CURL
    )
  end
end
