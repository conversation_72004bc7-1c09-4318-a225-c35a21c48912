class VendorBillingIndexCommand
  prepend SimpleCommand

  RESULT_ATTRIBUTES = [
    :vendor_id_s,
    :vendor_name,
    :campaign_id_s,
    :click_count_s,
    :install_count_s,
    :gross_spend_s,
    :net_spend_s,
    :total_clicks,
  ].freeze

  Result = Struct.new(*RESULT_ATTRIBUTES) do
    attr_accessor :title

    [
      :click_count,
      :install_count,
    ].each do |method_name|
      define_method method_name do
        num = (send "#{method_name}_s")
        num ? num.to_i : 0
      end
    end

    def vendor_id
      return unless vendor_id_s.present?
      vendor_id_s.to_i
    end

    def campaign_id
      return unless campaign_id_s.present?
      campaign_id_s.to_i
    end

    def gross_spend
      return 0 unless gross_spend_s.present?
      gross_spend_s.to_d
    end

    def net_spend
      return 0 unless net_spend_s.present?
      net_spend_s.to_d
    end

    def margin
      gross_spend.zero? ? 0 : net_revenue / gross_spend
    end

    def net_revenue
      gross_spend - net_spend
    end
  end

  attr_reader :start_date, :end_date, :options

  def initialize(start_date, end_date, options = {})
    @start_date = start_date
    @end_date   = end_date
    @options    = options
  end

  def call
    if options[:use_cache] && options[:campaign_id].present? && options[:vendor_id].present?
      record = campaign_vendor_data[[options[:vendor_id].to_i, options[:campaign_id].to_i]]
      [Result.new(*record)]
    else
      data
    end
  end

  def campaign_vendor_data
    @campaign_vendor_data ||= Rails.cache.fetch("campaign_vendor_data_#{start_date}_#{end_date}", expires_in: 10.minutes) do
      redshift_res.map{|r| [[r[0].to_i, r[2].to_i], r]}.to_h
    end
  end

  private

  def data
    redshift_res.map { |result| Result.new(*result) }
  end

  def redshift_res
    @redshift_res ||= Retriable.retriable(base_interval: 30) do
      ConversionRecordRedshift.connection.query(redshift_sql).to_a
    end
  end

  def redshift_sql
    @redshift_sql ||= <<-SQL
      SELECT vendor_id                                  AS vendor_id,
             vendor_name                                AS vendor_name,
             campaign_id                                AS campaign_id,
             SUM(click_count)                           AS click_count,
             SUM(#{normalize_install :install_count, :redshift})   AS install_count,
             SUM(#{normalize_gross_spend :redshift})               AS gross_spend,
             SUM(#{normalize_spend :net_spend, :redshift})         AS net_spend,
             SUM(click_count)                           AS total_clicks
      FROM arbitrage_v4_campaigns_view
      #{conditions(type: :redshift)}
      GROUP BY 1, 2, 3
    SQL
  end

  def conditions(type: :pg)
    sql_condition = []

    sql_condition << "calculate_date >= '#{start_date}' AND calculate_date <= '#{end_date}'"
    sql_condition << where_status(type)

    if !options[:use_cache]
      sql_condition << "campaign_id = '#{options[:campaign_id]}'"  if options[:campaign_id].present?
      sql_condition << "vendor_id = '#{options[:vendor_id]}'"     if options[:vendor_id].present?
    end

    return unless sql_condition.present?
    "WHERE #{sql_condition.join(' AND ')}"
  end

  def install_statuses(type = :pg)
    status_list = NewConversionRecord::STATUS_GROUPS.dig(:default, :value)
    type == :pg ? status_list.map { |name| NewConversionRecord.statuses[name] } : status_list
  end

  def spend_statuses(type = :pg)
    status_list = NewConversionRecord::STATUS_GROUPS.dig(:default, :spend_calculation)
    type == :pg ? status_list.map { |name| NewConversionRecord.statuses[name] } : status_list
  end

  def normalize_gross_spend(type = :pg)
    if type == :pg
      <<-SQL
      CASE
      WHEN click_url_id IN (#{arbitrage_click_url_ids.join(',')}) AND status NOT IN (11, 23, 25) AND calculate_date >= '2023-06-01' AND calculate_date < '2024-01-01' THEN spend
      WHEN status IN (#{spend_statuses(type).join(',')}) THEN spend
      ELSE 0
      END
      SQL
    else
      <<-SQL
      CASE
      WHEN click_url_id IN (#{arbitrage_click_url_ids.join(',')}) AND status NOT IN ('reengagement', 'invalid_mip', 'faked_conversion') AND calculate_date >= '2023-06-01' AND calculate_date < '2024-01-01' THEN spend
      WHEN status IN ('#{spend_statuses(type).join("','")}') THEN spend
      ELSE 0
      END
      SQL
    end
  end

  def normalize_spend(field, type = :pg)
    if type == :pg
      <<-SQL
      CASE
      WHEN status IN (#{spend_statuses(type).join(',')})
      THEN #{field}
      ELSE 0
      END
      SQL
    else
      <<-SQL
      CASE
      WHEN status IN ('#{spend_statuses(type).join("','")}')
      THEN #{field}
      ELSE 0
      END
      SQL
    end
  end

  def normalize_install(field, type = :pg)
    if type == :pg
      <<-SQL
        CASE
        WHEN status IN (#{install_statuses.join(',')})
        THEN install_count
        ELSE 0
        END
      SQL
    else
      <<-SQL
        CASE
        WHEN status IN ('#{install_statuses(:redshift).join("','")}')
        THEN install_count
        ELSE 0
        END
      SQL
    end
  end

  def where_status(type = :pg)
    if type == :pg
      "(
        (status IN (1, 3) AND click_url_id NOT IN (#{arbitrage_click_url_ids.join(',')}))
        OR
        (status NOT IN (11, 23, 25) AND click_url_id IN (#{arbitrage_click_url_ids.join(',')}))
      )"
    else
      "(
        (status IN ('normal', 'injected') AND click_url_id NOT IN (#{arbitrage_click_url_ids.join(',')}))
        OR
        (status NOT IN ('reengagement', 'invalid_mip', 'faked_conversion') AND click_url_id IN (#{arbitrage_click_url_ids.join(',')}))
      )"
    end
  end

  def arbitrage_click_url_ids
    arbitrage_click_url_ids = ClickUrl.arbitrage_click_url_ids
    arbitrage_click_url_ids = [-999] if arbitrage_click_url_ids.blank?
    arbitrage_click_url_ids
  end
end
