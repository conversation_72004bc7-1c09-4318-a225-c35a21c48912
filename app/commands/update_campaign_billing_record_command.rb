class UpdateCampaignBillingRecordCommand
  prepend SimpleCommand

  MULTI_SQL_RUN_ERROR = 'Multi sql is running!'.freeze

  attr_reader :vendor_id,
  :campaign_id,
  :start_date,
  :end_date,
  :billing_month,
  :is_historical,
  :campaign_billing_records

  def initialize(options = {})
    @start_date   = options[:start_date]&.to_date
    @end_date     = options[:end_date]&.to_date
    @billing_month = options[:billing_month]
    @vendor_id    = options[:vendor_id].to_i
    @campaign_id  = options[:campaign_id].to_i
    @is_historical = options[:is_historical]
    @campaign_billing_records = options[:campaign_billing_records] || []
  end

  def call
    update_campaign_billing_record!
  end

  private

  def update_campaign_billing_record!
    campaign_billing_record.update!(
      campaign_name: campaign.name,
      client_name: client.name,
      client_id: client.id,
      clicks: clicks,
      installs:  installs,
      events: paid_events,
      gross_spends: gross_spends,
      net_spends: net_spends,
      makegoods: makegoods,
      non_billable_vendor_spend: non_billable_vendor_spend,
      total_clicks: total_clicks,
    )
  end

  def campaign
    Campaign.find_by(id: campaign_id)
  end

  def client
    campaign.client
  end

  def clicks
    Integer(results&.click_count || 0)
  end

  def total_clicks
    Integer(results&.total_clicks || 0)
  end

  def installs
    Integer(results&.install_count || 0)
  end

  def gross_spends
    (results&.gross_spend || 0).to_d
  end

  def net_spends
    (results&.net_spend || 0).to_d
  end

  def results
    @results ||= VendorBillingIndexCommand.call(
                  start_date,
                  end_date,
                  vendor_id: vendor_id,
                  campaign_id: campaign_id,
                  is_historical: is_historical,
                  use_cache: true,
                ).result.first
  end

  def makegoods
    ClientMakegood.
      without_deleted.
      where(
        month: billing_month,
        vendor_id: vendor_id,
        campaign_id: campaign_id
      ).sum(:net_amount)
  end

  def non_billable_vendor_spend
    AdvertiserSpend.
    where(
      vendor_id: vendor_id,
      campaign_id: campaign_id,
      spend_month: billing_month,
      status: 3,
      send_to_vendor: false,
      send_csv_log: false
      ).inject(0) do |sum, advertiser_spend|
      if advertiser_spend.action_count.zero?
        sum
      else
        sum + (advertiser_spend.net_spend || 0) * advertiser_spend.release_action_count / advertiser_spend.action_count
      end
    end
  end

  def paid_events
    paid_actions.inject(0) do |sum, paid_action|
      sum + campaign_billing_records.find do |i|
        i.vendor_id.to_i == vendor_id && i.campaign_id.to_i == campaign_id
      end&.send(paid_action.to_sym).to_i
    end
  end

  def paid_actions
    @paid_actions ||= ClickUrl.all_paid_actions(campaign.click_urls.where(vendor_id: vendor_id).distinct.pluck(:bind_action).uniq)
  end

  def campaign_billing_record
    CampaignBillingRecord.find_or_create_by!(
      vendor_id: vendor_id,
      campaign_id: campaign_id,
      month: billing_month,
    )
  end
end
