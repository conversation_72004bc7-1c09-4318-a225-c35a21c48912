# frozen_string_literal: true

class SyncPgToRedshiftCommand
  prepend SimpleCommand
  include FileUtils

  attr_reader :to_table, :file_name, :zipped_file_name, :export_data_sql, :delete_condition, :db, :redshift_modal

  def initialize(to_table, file_name, export_data_sql, delete_condition, db = 'primary_db', redshift_modal = 'ConversionRecordRedshift')
    @to_table   = to_table
    @file_name  = file_name
    @zipped_file_name = "#{@file_name}.gz"
    @export_data_sql = export_data_sql
    @delete_condition = delete_condition
    @db = db
    @redshift_modal = redshift_modal
  end

  def call
    return if export_data_sql.blank?

    export_data_to_file
    gzip_file
    upload_file_to_s3
    delete_old_redshift_data
    copy_from_s3_to_redshift
  ensure
    clear_local_tmp_files
  end

  private

  def gzip_file
    clear_local_tmp_files
    sh "gzip #{local_file_path}"
  end

  def export_data_to_file
    db_url = if db == 'primary_db'
      ENV['PRIMARY_DATABASE_URL']
    elsif db == 'conversion_db'
      ENV['AGGREGATED_STATS_DATABASE_URL']
    elsif db == 'new_conversion_db'
      ENV['CONVERSION_DATABASE_URL']
    end
    sh "mkdir -p #{File.dirname(local_file_path)}"
    sh "psql \'#{db_url}\' -c \"\\COPY (#{export_data_sql}) TO #{local_file_path} WITH CSV DELIMITER '^' \""
  end

  def upload_file_to_s3
    s3_service = Aws::S3::Resource.new(region: ENV["AWS_S3_REGION"] || "us-east-1")
    obj = s3_service.bucket(s3_bucket_name).object("sync_to_redshift_job/#{zipped_file_name}")
    obj.upload_file(local_zipped_file_path)
  end

  def delete_old_redshift_data
    redshift_modal.constantize.connection.execute("DELETE FROM #{to_table} #{delete_condition}")
  end

  def copy_from_s3_to_redshift
    sql = <<-SQL
      COPY #{to_table}
      FROM 's3://#{s3_file_path}'
      IAM_ROLE '#{ENV['REDSHIFT_IAM_ROLE']}'
      DELIMITER AS '^'
      CSV QUOTE '"'
      GZIP
    SQL

    redshift_modal.constantize.connection.execute(sql)
  end

  def s3_file_path
    "#{s3_bucket_name}/sync_to_redshift_job/#{zipped_file_name}"
  end

  def clear_local_tmp_files
    sh "rm -rf #{local_zipped_file_path}"
  end

  def local_file_path
    "/tmp/#{file_name}"
  end

  def local_zipped_file_path
    "/tmp/#{zipped_file_name}"
  end

  def s3_bucket_name
    Rails.env.production? ? 'feedmob-redshift-records' : 'feedmob-testing'
  end
end
