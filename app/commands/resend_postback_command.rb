class ResendPostbackCommand
  prepend SimpleCommand

  attr_reader :cr, :mcr, :postback, :csv_log, :status, :click_url, :target_url, :options

  def initialize(conversion_record, postback, csv_log, options = {})
    @cr = conversion_record
    @postback = postback
    @options = options
    @csv_log = csv_log
    @click_url = ClickUrl.find_by(id: cr.click_url)

    set_mcr
    set_target_url
    replace_params_by_click_record
    replace_conversion_params
  end

  def call
    if postback
      return unless params
      resend
    else
      resend
    end
  end

  private

  def set_mcr
    @mcr = NewConversionRecord.by_date(cr.event_time.to_date).find_by(conversion_id: cr.conversion_id, status: cr.status, track_type: cr.track_type, repeated: cr.repeated)
  end

  def params
    ourl = cr.ourl
    ourl = mcr.ourl unless ourl
    @params ||= Rack::Utils.parse_query URI(ourl).query
    @params.with_indifferent_access
  end

  def resend
    url, res_code, res_msg =
      if target_url
        if postback == true
          ApiConcern.http_get(target_url)
        end
      end

    if csv_log
      Sidekiq::Client.via(SIDEKIQ_CONVERSION_WORKER_POOL) do
        HandleConversionReleasedLogJob.perform_async(
          event_time: cr.event_time,
          uuid: cr.uuid,
          conversion_id: cr.conversion_id,
          vendor_id: cr.vendor_id,
          campaign_id: cr.campaign_id,
          click_id: cr.click_id,
          postback_type: 'send_csv_log',
          postback_reason: options[:postback_reason]
        )
      end
    end

    cr.memo ||= {}
    info = cr&.info
    if cr.status == "normal"
      cr.class.transaction do
        cr.update!(target_url: url, memo: cr.memo.merge(release: true, tags: ["normal"], postback: postback, postback_time: Time.now.to_s).merge(options[:memo].to_h))
        info.update!(response_code: res_code, response_msg: res_msg, memo: info.memo.merge(release: true, release_type: "#{cr.status}", exec_time: Time.now.to_s, postback: postback, postback_time: Time.now.to_s, tags: ["normal"]).merge(options[:memo].to_h)) if info

        update_conversion_record(cr, url, res_code, res_msg, cr.status, cr.status, postback)
      end
    else
      origin_status = cr.status
      update_status = top_priority_status(cr)
      postback_time = get_postback_time
      cr.class.transaction do
        cr.update!(status: update_status, target_url: url, memo: cr.memo.merge(release: true, tags: tags.delete_if{|t| t == origin_status}, postback: postback, postback_time: postback_time).merge(options[:memo].to_h))
        info.update!(response_code: res_code, response_msg: res_msg, memo: info.memo.merge(release: true, release_type: "#{origin_status}", postback: postback, exec_time: Time.now.to_s, postback_time: postback_time, tags: tags.delete_if{|t| t == origin_status}).merge(options[:memo].to_h)) if info

        update_conversion_record(cr, url, res_code, res_msg, origin_status, update_status, postback, postback_time)
      end
    end
  end

  def get_postback_time
    if target_url.blank?
      return nil
    end

    Time.now.to_s
  end

  def top_priority_status(cr)
    ordered_statuses = %w(reengagement invalid_cr rejected_by_third_party manual_stopped click_injection dismatch_device dismatch_country blocked_city mtti_sub_thres non_monetized_sku install_decay_thres over_cap rejected_by_24metrics out_of_seven purchase_hijacking)
    selected = ordered_statuses.select{|s| tags.include?(s)}
    if status = selected.at(selected.index(cr.status) + 1)
      status
    else
      "normal"
    end
  end

  def update_conversion_record(cr, url, res_code, res_msg, origin_status, update_status, postback, postback_time = Time.now.to_s)
    c = NewConversionRecord.by_date(cr.event_time.to_date).find_by(conversion_id: cr.conversion_id, status: origin_status, track_type: cr.track_type, repeated: cr.repeated)
    if origin_status == "normal"
      c.update!(target_url: url, response_code: res_code, response_msg: res_msg, memo: c.memo.merge(release: true, release_type: "#{cr.status}", exec_time: Time.now.to_s, postback_time: postback_time, postback: postback).merge(options[:memo].to_h), imported: true) if c
    else
      c.update!(target_url: url, response_code: res_code, response_msg: res_msg, status: update_status, memo: c.memo.merge(release: true, release_type: "#{origin_status}", postback_time: postback_time, postback: postback, exec_time: Time.now.to_s, tags: tags.delete_if{|t| t == origin_status}).merge(options[:memo].to_h), imported: true) if c
    end

    body =
      [{ index: { _id: cr.uuid, data: cr.as_indexed_json}, create: {_id: cr.uuid, data: cr.as_indexed_json}}]
    ElasticsearchClient.conversion_record.bulk(index: 'conversion_records', type: 'conversion_record', body: body, pipeline: 'monthlyindex') if cr.present?
  end

  def set_target_url
    @target_url = begin
      return nil unless postback
      return nil unless billable_status?
      return nil unless click_url
      return nil unless feedmob_install?
      if cr.repeated
        return nil unless is_purchase?
      end

      click_url.get_postback_by_action(params["ACTION"], cr.repeated)
    end
  end

  def is_purchase?
    cr.track_type == "purchase"
  end

  def add_spend?
    click_url&.bind_action&.sub("first_", "") == cr.track_type
  end

  def billable_status?
    (NewConversionRecord::NonBillable - tags.delete_if{|c| c == "manual_stopped" || c == cr.status}).size == NewConversionRecord::NonBillable.size
  end

  def tags
    memo = cr.memo.present? ? cr.memo : mcr.memo
    JSON.parse(memo.dig("tags"))
  end

  def feedmob_install?
    return true if cr.track_type == "install"
    params["FEEDMOB_INSTALL"].to_s != "0"
  end

  def click_record
    @click_record ||= cr.click_record
  end

  def install_record
    @install_record ||= cr.install_record
  end

  def replace_params_by_click_record
    return if click_record.blank?
    return unless @target_url
    @target_url = click_record["device_platform_id"] ?
      @target_url.gsub(
        /\{DEVICE_PLATFORM_ID\}/, click_record["device_platform_id"]
    ) : @target_url

    @target_url = click_record["campaign_id"] ?
      @target_url.gsub(
        /\{CAMPAIGN_ID\}/, click_record["campaign_id"].to_s
    ) : @target_url

    @target_url = click_record["app_id"] ?
      @target_url.gsub(
        /\{APP_ID\}/, click_record["app_id"].to_s
    ) : @target_url
  end

  def replace_conversion_params
    return unless @target_url
    @target_url = params[:CONVERSION_ID] ? @target_url.gsub(/\{CONVERSION_ID\}/, params[:CONVERSION_ID]) : @target_url
    @target_url = (params[:DEVICE_PLATFORM_ID] || cr.device_platform_id) ? @target_url.gsub(/\{DEVICE_PLATFORM_ID\}/, params[:DEVICE_PLATFORM_ID] || cr.device_platform_id) : @target_url
    @target_url = params[:CAMPAIGN_ID] ? @target_url.gsub(/\{CAMPAIGN_ID\}/, params[:CAMPAIGN_ID]) : @target_url
    @target_url = params[:APP_ID] ? @target_url.gsub(/\{APP_ID\}/, params[:APP_ID]) : @target_url
    @target_url = params[:OFFER_ID] ? @target_url.gsub(/\{OFFER_ID\}/, params[:OFFER_ID]) : @target_url


    @target_url = @target_url.gsub(/\{CONVER_TIME\}/, cr.event_time.to_s)

    @target_url = @target_url.gsub(/\{FEEDMOB_INSTALL_AT\}/, install_at.to_s)
    @target_url = @target_url.gsub(/\{FEEDMOB_INSTALL_AT_NUMERIC\}/, install_at.try(:to_i).to_s)
    @target_url = @target_url.gsub(/\{FEEDMOB_CONVERSION_AT\}/, conversion_at.to_s)
    @target_url = @target_url.gsub(/\{FEEDMOB_CONVERSION_AT_NUMERIC\}/, conversion_at.try(:to_i).to_s)
    @target_url = @target_url.gsub(/\{FEEDMOB_DEVICE_IP\}/, cr&.remote_ip.to_s)
  end

  def install_at
    third_party_install_at || feedmob_install_time
  end

  def conversion_at
    third_party_conversion_at || cr.event_time.to_datetime
  end

  def feedmob_install_time
    install_record ? install_record.event_time : cr.event_time.to_datetime
  end

  def third_party_install_at
    if params[:FEEDMOB_INSTALL_AT].to_i.to_s == params[:FEEDMOB_INSTALL_AT].to_s
      time = Time.zone.at(params[:FEEDMOB_INSTALL_AT].to_i)
    else
      time = params[:FEEDMOB_INSTALL_AT].try(:to_datetime)
    end
    time ? time : nil
  end

  def third_party_conversion_at
    if params[:FEEDMOB_CONVERSION_AT].to_i.to_s == params[:FEEDMOB_CONVERSION_AT].to_s
      time = Time.zone.at(params[:FEEDMOB_CONVERSION_AT].to_i)
    else
      time = params[:FEEDMOB_CONVERSION_AT].try(:to_datetime)
    end
    time ? time : nil
  end
end
