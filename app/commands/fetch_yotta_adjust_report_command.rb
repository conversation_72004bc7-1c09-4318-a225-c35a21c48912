# frezen_string_literal: true

class FetchYottaAdjustReportCommand < FetchYottaAdjustReportBaseCommand
  prepend SimpleCommand
  include AdjustApiConcern
  include MmpAggregateDataConcern

  attr_reader :start_date, :end_date, :report_not_mappings
  SYSTEM_EMAIL = '<EMAIL>'

  def initialize(start_date: Date.yesterday.to_s, end_date: Date.yesterday.to_s)
    @start_date = start_date
    @end_date = end_date
    @report_not_mappings = []
    @mmp_aggrate_datas = []
  end

  def call
    campaign_datas = fetch_adjust_data(start_date, end_date)

    if campaign_datas.nil?
      SlackService.send_notification_to_star("#{self.class.name}: start_date #{start_date} end_date #{end_date} faild to create report, please check!")
      return
    end
    (start_date.to_date..end_date.to_date).each do |date|
      save_report(date, campaign_datas)
    end

    save_mmp_aggregate_datas

    send_slack_alert(report_not_mappings)
  end

  def save_report(date, campaign_datas)
    date_campaign_datas = campaign_datas.select { |c| c['day'].to_date.to_s == date.to_s }
    group_campaign_datas = date_campaign_datas.group_by {|c| [c['store_id'], c['campaign'], c['os_name'], c['partner_name'].to_s.downcase] }
    group_campaign_datas.each do |key, items|
      app_id, adjust_campaign_name, adjust_os_name, adjust_channel = key

      if adjust_campaign_name == 'unknown'
        report_not_mappings << key
        next
      end

      event_mapping = event_mappings.find {|c| c.app_id == app_id }

      click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel,  Client::YOTTA_ID, false)

      next if click_url.blank?

      metrics = %w[impressions clicks installs] + event_mapping.yotta_events_in_json(end_date).values.flatten.uniq
      raw_log = metrics.each_with_object({}) do |metric, hash|
        hash[metric] = items.sum{|c|c[metric].to_i}
      end
      update_mmp_original_event_report(raw_log, click_url, date, event_mapping)

      model = {
        impression: items.sum{|c|c['impressions'].to_i},
        click: items.sum{|c|c['clicks'].to_i},
        install: items.sum{|c|c['installs'].to_i},
        raw_log: raw_log
      }
      mmp_aggrate_data = model.dup

      event_mapping.yotta_events_in_json(end_date).each do |feedmob_event, adjust_events|
        mmp_aggrate_data["#{feedmob_event}_original".to_sym] = {}

        event_count = adjust_events.map do |event|
          item_event_count = items.sum{|c|c[event].to_i}
          mmp_aggrate_data["#{feedmob_event}_original".to_sym][event] = item_event_count
          item_event_count
        end.sum
        model[feedmob_event.to_sym] = event_count
        mmp_aggrate_data[feedmob_event.to_sym] = event_count
      end

      @mmp_aggrate_datas << mmp_aggrate_data.merge({source_report_type: table_by(click_url).to_s, click_url_id: click_url.id, date: date})

      report = table_by(click_url).find_by(date: date, adjust_campaign_name: adjust_campaign_name, adjust_channel: adjust_channel, adjust_os_name: adjust_os_name, skan: false)
      if report.blank?
        report = table_by(click_url).new(click_url_id: click_url.id, campaign_id: click_url.campaign_id, vendor_id: click_url.vendor_id, date: date, adjust_campaign_name: adjust_campaign_name, adjust_channel: adjust_channel, adjust_os_name: adjust_os_name, skan: false)
      end

      Audited.audit_class.as_user(User.find_by(email: SYSTEM_EMAIL)) do
        report.update model
      end
    end
  end

  def update_mmp_original_event_report(raw_log, click_url, date, event_mapping)
    return if event_mapping.blank?

    raw_log =  raw_log.select { |event, total| total.to_i > 0 }

    raw_log.each do |adjust_event, total|
      feedmob_event = event_mapping.get_feedmob_event(adjust_event)

      next if feedmob_event.blank?

      report = MmpOriginalEventReport.find_or_create_by(
        click_url_id: click_url.id,
        date: date,
        mmp_original_event_name: adjust_event,
        track_type: feedmob_event
      )

      report.update(
        total: total,
        click_url_id: click_url.id,
        campaign_id: click_url.campaign_id,
        vendor_id: click_url.vendor_id,
        vendor: click_url.vendor&.vendor_name,
        campaign: click_url.campaign&.name,
        mmp: 'adjust'
      )
    end
  end

  def send_slack_alert(report_not_mappings)
    if report_not_mappings.present?
      SlackService.send_notification_to_star("#{self.class.name}: 在 #{start_date} ~ #{end_date}, 以下 adjust api 数据没有对应的 mapping，需要补充mapping 关系。\n#{report_not_mappings.uniq.map { |r| r.join(', ') }.join("\n")}")
    end
  end
end
