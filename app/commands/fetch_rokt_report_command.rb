require "base64"

class FetchRoktReportCommand
  attr_accessor :date, :basic_token, :client

  BASE_URL = 'https://api.rokt.com'.freeze
  ROKT_VENDOR_ID = 243

  def initialize(date, client)
    @date = date.to_date
    @client = client
    @campaign_name_mapping = get_mapping
    @basic_token = Base64.strict_encode64("#{ENV['ROKT_APP_ID']}:#{ENV['ROKT_APP_SECRET']}")
  end

  def execute
    access_token = fetch_token()
    records = fetch_report(access_token)
    generate_net_spend(records)
  end

  def get_mapping
    ENV['ROKT_CAMPAIGN_MAPPING']&.split(';')&.map{|item| item.split(',')}&.to_h || {}
  end

  def fetch_token
    res = HTTParty.post(
      "#{BASE_URL}/auth/oauth2/token",
      headers: {"Content-Type"=>"application/x-www-form-urlencoded", "Authorization"=> "Basic #{@basic_token}"},
      body: { grant_type: 'client_credentials' }
    )
    JSON.parse(res.body).dig('access_token')
  end

  def fetch_report(access_token)
    return [] if access_token.blank?
    res = HTTParty.get(
      "#{BASE_URL}/reporting/accounts/3013880200746511599/campaigns/breakdown",
      headers: { "Authorization"=> "Bearer #{access_token}"},
      query: { dateStart: date.beginning_of_day.iso8601(3)[0..-2], dateEnd: date.end_of_day.iso8601(3)[0..-2], timeZoneVariation: "UTC", Currency: "USD" }
    )
    JSON.parse(res.body).dig('data', 'items')
  end

  def generate_net_spend(records)
    return if records.blank?
    res = records.each_with_object({}) do |record, hash|
      campaign_id = @campaign_name_mapping[record['campaignName'].split("|").first.strip]
      click_urls = ClickUrl.where(campaign_id: campaign_id, vendor_id: ROKT_VENDOR_ID)
      if click_urls.blank?
        SlackService.send_notification_to_channel("Rokt api report: #{record['campaignName']} 未映射到click url， 会导致无法自动生成相应的cpm spend.", :mighty)
        next
      end

      if click_urls.count > 1
        SlackService.send_notification_to_channel("Rokt api report: #{record['campaignName']} 有多个click url映射到同一个campaign， 会导致无法自动生成相应的cpm spend.", :mighty)
        next
      end
      click_url = click_urls[0]

      hash[click_url.id] ||= { 
        click: 0, spend: 0, impression: 0, install: 0, 
        margin: click_url.margin_by_date(date), 
        vendor_id: click_url.vendor_id, 
        campaign_id: click_url.campaign_id, 
        paid_action: click_url.bind_action
      }
      hash[click_url.id][:spend] += record['netCost'].to_f
    end

    Audited.audit_class.as_user(User.find_by(email: '<EMAIL>')) do
      res.each do |click_url_id, value|
        gross_spend = calculated_gross_spend(value[:margin], value[:spend])
        net_spend = NetSpend.find_or_create_by(spend_date: date, click_url_id: click_url_id)
        net_spend.update(
          vendor_id: value[:vendor_id],
          campaign_id: value[:campaign_id],
          paid_action: value[:paid_action],
          net_spend: value[:spend],
          gross_spend: gross_spend,
          impression: value[:impression],
          clicks_count: value[:click],
          installs_count: value[:install],
          audit_comment: self.class.name
        )
      end
    end
  end

  def calculated_gross_spend(margin, net_spend)
    margin.to_f < 100 ? net_spend / (1 - margin/100.0) : 0
  end
end
