class BinanceReportDataCommand
  prepend SimpleCommand

  include ActionView::Helpers::NumberHelper
  include IronSourceApiConcern
  include IronSourceAuraApiConcern
  include QuoraApiConcern
  include ReportConfigConcern
  attr_reader :date,:google_sheet_headers

  HEADERS = [
    "Date",
    "Click URL",
    "Campagin Name",
    "Geo",
    "OS",
    "Partner",
    "AF Impressions",
    "AF Clicks",
    "AF Installs",
    "CVR",
    "AF Verified Registrations",
    "AF First Time Trade",
    "Partner Dashboard Spend",
    "SPEND Gross",
    "Profit/Loss",
    "Margin",
    "D7 Rev $",
    "D7 ROAS",
    "MONTH"
  ].freeze

  def initialize(date,google_sheet_headers = nil)
    @date = date
    @google_sheet_headers = google_sheet_headers
  end

  def call
    report_datas = []
    click_urls = ClickUrl.includes(:campaign,:vendor).where(id: click_url_ids)

    agency_af_datas  = get_data_from_appsflyer_agency_reports
    fm_link_af_datas = get_data_from_appsflyer_reports

    no_deal_click_urls = []
    click_urls.each do |click_url|
      af_data = if click_url.link_type == 'fm_link'
        fm_link_af_datas.find {|c|c['click_url_id'] == click_url.id && c['event_date'] == date.to_s}.to_h
      else
        agency_af_datas.find {|c|c['click_url_id'] == click_url.id && c['event_date'] == date.to_s}.to_h
      end

      if click_url.vendor_id.in?([497, 391])  # InMobi DSP, Smadex 的net和gross取值于direct spend
        gross_spend, net_spend = get_direct_spend(click_url, date)
      elsif click_url.id == 21124 # hot fix for click_url_id 12214, 等自动化 job 完成后，再来调整
        gross_spend, net_spend = get_direct_spend(click_url, date)
      elsif click_url.id.in?([21546,21547])
        # 这2个 Applovin 的 ClickURL 比较特殊， 2025-04-03号及之前，是测试的数据，Gross 和 Net 按最多500计算，取值与 Direct Spend, 之后按正常计费
        # 2025-04-04开始，正常使用公式计算
        if date.to_s >= '2025-04-04'
          net_spend = get_net_spend(click_url, date)
          gross_spend = get_gross_spend(click_url, date, net_spend)
        else
          # 更新 2025-04-03 及 之前的数据，不能取 Direct Spend 直接更新，人工介入
          # gross_spend, net_spend = get_direct_spend(click_url, date)
          no_deal_click_urls << {
            click_url_id: click_url.id,
            date: date
          }
          next
        end
      elsif click_url.direct_spend_input_by_date(date) # 打开了 direct spend input 的 click_url
        net_spend = get_net_spend(click_url, date)
        gross_spend = get_gross_spend(click_url, date, net_spend)
      else # fm_link 的 spend 计算方式
        gross_spend, net_spend = get_fm_link_spend(click_url, date)

        send_report_config_job_slack_alert("#{self.class.name}: click_url_id: #{click_url.id} link_type: #{click_url.link_type} 当前按照 fm link 的方式自动录入数据到 Binance Media Plan，请检查是否合理!", :star)
      end

      next if af_data.blank? && net_spend.to_f == 0

      report_datas << HEADERS.zip([
        date.to_date.strftime("%m/%d/%Y"),
        click_url.id.to_s,
        click_url.campaign.name,
        "=VLOOKUP(#{get_google_sheet_header_chr('Campagin Name')}{{ROW}},'Ced Reference'!A:B,2,0)",
        "=VLOOKUP(#{get_google_sheet_header_chr('Campagin Name')}{{ROW}},'Ced Reference'!A:C,3,0)",
        click_url.vendor.vendor_name,
        af_data['impression'].to_i,
        af_data['click'].to_i,
        af_data['install'].to_i,
        "=IFERROR(#{get_google_sheet_header_chr('AF Installs')}{{ROW}}/#{get_google_sheet_header_chr('AF Clicks')}{{ROW}},\"-\")",
        af_data['registration_verified'].to_i,
        af_data['af_first_trade'].to_i,
        net_spend,
        gross_spend,
        "=#{get_google_sheet_header_chr('SPEND Gross')}{{ROW}}-#{get_google_sheet_header_chr('Partner Dashboard Spend')}{{ROW}}",
        "=(#{get_google_sheet_header_chr('SPEND Gross')}{{ROW}}-#{get_google_sheet_header_chr('Partner Dashboard Spend')}{{ROW}})/#{get_google_sheet_header_chr('SPEND Gross')}{{ROW}}",
        af_data['revenue_7'].to_f,
        "=#{get_google_sheet_header_chr('D7 Rev $')}{{ROW}}/#{get_google_sheet_header_chr('SPEND Gross')}{{ROW}}",
        date.to_date.strftime("%B")
      ]).to_h
    end
    send_notification(no_deal_click_urls)
    format_report_data(google_sheet_headers, report_datas)
  end

  def format_report_data(google_sheet_headers, report_datas)
    header_mapping = {}
    if google_sheet_headers.present?
      not_in_google_sheet_headers = HEADERS.reject { |item| google_sheet_headers.include?(item) }
      if not_in_google_sheet_headers.present?
        msg = "#{self.class.name}: 数据返回的 #{not_in_google_sheet_headers.join(', ')} 字段不在Binance Media Plan Google Sheet Header 中，不会写入字段对应数据，请检查!"
        send_report_config_job_slack_alert(msg, :star)
      end

      google_sheet_headers.uniq.each_with_index do |header, index|
        if header.strip.empty?
          header_mapping[(65 + index).chr] = (65 + index).chr
        else
          header_mapping[header.strip] = (65 + index).chr
        end
      end

      formatted_data = report_datas.map do |data|
        hash_data = header_mapping.transform_values { "-" }
        data.each do |key, value|
          hash_data[key] = value if header_mapping.key?(key)
        end
        hash_data
      end

      formatted_data
    else
      send_report_config_job_slack_alert("#{self.class.name}: 还没有配置 Google Sheet Header，会影响 Binance Media Plan 的生成，请检查!", :star)
      []
    end
  end

  def get_google_sheet_header_chr(title)
    index = google_sheet_headers.index(title)
    if index.present?
      (index + 65).chr
    else
      'AZ'
    end
  end

  def get_gross_spend(click_url, date, net_spend)
    gross = if click_url.vendor_id.in?([454, 447, 411, 408])
      gross_cpi = click_url.gross_cpi_by_date(date)
      "=#{get_google_sheet_header_chr('AF First Time Trade')}{{ROW}}*#{gross_cpi}"
    elsif click_url.vendor_id == 358
      gross_cpi = click_url.gross_cpi_by_date(date)
      "=#{get_google_sheet_header_chr('AF Installs')}{{ROW}}*#{gross_cpi}"
    else
      margin = click_url.margin_by_date(date)
      "=#{get_google_sheet_header_chr('Partner Dashboard Spend')}{{ROW}}/#{(1 - margin.to_f / 100)}"
    end
    gross
  end

  def get_net_spend(click_url, date)
    net_spend = if click_url.vendor_id == 192
      get_ironsrce_net_spend(click_url, date)
    elsif click_url.vendor_id == 288
      get_ironsrce_aura_net_spend(click_url, date)
    elsif click_url.vendor_id == 407
      get_quora_net_spend(click_url, date)
    elsif click_url.vendor_id == 358
      net_cpi = click_url.net_cpi_by_date(date)
      "=#{get_google_sheet_header_chr('AF Installs')}{{ROW}}*#{net_cpi}"
    elsif [454, 447, 411, 408].include?(click_url.vendor_id)
      net_cpi = click_url.net_cpi_by_date(date)
      "=#{get_google_sheet_header_chr('AF First Time Trade')}{{ROW}}*#{net_cpi}"
    elsif click_url.vendor_id == Vendor::APP_LOVIN_ID
      get_applovin_spend(click_url, date)
    else # 没有确定 net spend 计算方式的 vendor，直接从 direct spend 获取
      message = "#{self.class.name}: click url: #{click_url.id} 还没有增加 #{click_url.vendor.vendor_name} 的 net spend 获取， 暂时直接从 direct spend 获取，请检查是否合理!"
      key = "#{self.class.name}:click_url:#{Digest::MD5.hexdigest(message)}"

      unless Rails.cache.exist?(key)
        Rails.cache.write(key, true, expires_in: 10.minutes)
        send_report_config_job_slack_alert(message, :star)
      end

      direct_spend = NetSpend.find_by(click_url_id: click_url.id, spend_date: date)
      direct_spend&.net_spend.to_f
    end

    net_spend
  end

  def get_applovin_spend(click_url, date)
    applovin_campaign_ids = applovin_mappings.select {|k, v| v == click_url.id  }.map {|k,v| k}
    applovin_reports.select { |c|
      c[:applovin_campaign_id].in?(applovin_campaign_ids)
    }.sum{ |c| c[:spend].to_f }
  end

  def get_ironsrce_aura_net_spend(click_url, date)
    ironsrce_aura_reports.select {|c|
      c['day'] == date.to_s && c['campaign_name'].include?(click_url.campaign.name)
    }.sum{|c|c['spend']}
  end

  def get_ironsrce_net_spend(click_url, date)
    iron_source_mappings = get_iron_source_mappings
    ironsrce_reports.select {|c|
      c['date'].to_date.to_s == date.to_s && (c['campaignName'].include?(click_url.campaign.name) || iron_source_mappings[click_url.id].include?(c['campaignId'].to_s))
    }.sum{|c|c['spend']}
  end

  def get_quora_net_spend(click_url, date)
    quora_reports.select {|c|
      c['campaignName'].include?(click_url.campaign.name)
    }.sum{|c|c['spend']}.to_f/10000.0
  end

  def get_direct_spend(click_url, date)
    direct_spend = NetSpend.find_by(click_url_id: click_url.id, spend_date: date)
    return [0, 0] unless direct_spend
    [direct_spend.gross_spend.to_f, direct_spend.net_spend.to_f]
  end

  def get_fm_link_spend(click_url, date)
    spend_query = fe_spend_query(click_url, date)
    fe_spend = ConversionRecordRedshift.connection.execute(spend_query).first.map { |k, v| v.to_f.round(2) }
  end

  def ironsrce_aura_reports
    @ironsrce_aura_reports ||= fetch_iron_source_aura_reports(date, date)
  end

  def ironsrce_reports
    @ironsrce_reports ||= fetch_reports_by_bundleids(date, date, ['com.binance.dev', 'com.czzhao.binance'])
  end

  def quora_reports
    @quora_reports ||= fetch_binance_quora_reports(date)
  end

  def get_iron_source_mappings
    @iron_source_mappings ||= IronsourceCampaignMapping.where(click_url_id: click_url_ids).pluck(:click_url_id, :ironsource_campaign_id).group_by(&:first).transform_values { |arr| arr.map(&:last) }
  end

  def applovin_reports
    @applovin_reports ||= ApplovinApiService.get_spend(date)
  end

  def applovin_mappings
    @applovin_mappings ||= ApplovinCampaignMapping.where(click_url_id: click_url_ids).pluck(:applovin_campaign_id, :click_url_id).to_h
  end

  def get_data_from_appsflyer_agency_reports
    sql = <<-SQL
        SELECT
          cuh.event_date,
          cuh.click_url_id,
          af_report.af_app_id,
          sum(af_report.impression) as impression,
          sum(af_report.click) as click,
          sum(af_report.install) as install,
          sum(af_event.registration) as registration_verified,
          sum(af_event.purchase) as af_first_trade,
          sum(af_report.revenue_7) as revenue_7
        FROM click_url_histories as cuh
        left join appsflyer_agency_reports as af_report on af_report.click_url_id = cuh.click_url_id and af_report.date = cuh.event_date
        left join appsflyer_in_app_events as af_event on af_event.click_url_id = cuh.click_url_id and af_event.date = cuh.event_date
        WHERE cuh.event_date = '#{date}'
        AND cuh.click_url_id IN (#{click_url_ids.join(',')})
        GROUP by 1, 2, 3
        HAVING SUM(af_report.impression) > 0 OR SUM(af_report.click) > 0 OR SUM(af_report.install) > 0 OR SUM(af_event.registration) > 0 OR SUM(af_event.purchase) > 0
        ;
      SQL
    AppsflyerAgencyReport.connection.execute(sql).to_a
  end

  def get_data_from_appsflyer_reports
    sql = <<-SQL
        SELECT
          cuh.event_date,
          cuh.click_url_id,
          af_report.af_app_id,
          sum(af_report.impression) as impression,
          sum(af_report.click) as click,
          sum(af_report.install) as install,
          sum(af_event.registration) as registration_verified,
          sum(af_event.purchase) as af_first_trade,
          sum(af_report.revenue_7) as revenue_7
        FROM click_url_histories as cuh
        left join appsflyer_reports as af_report on af_report.click_url_id = cuh.click_url_id and af_report.date = cuh.event_date
        left join appsflyer_in_app_events as af_event on af_event.click_url_id = cuh.click_url_id and af_event.date = cuh.event_date
        WHERE cuh.event_date = '#{date}'
        AND cuh.click_url_id IN (#{click_url_ids.join(',')})
        GROUP by 1, 2, 3
        HAVING SUM(af_report.impression) > 0 OR SUM(af_report.click) > 0 OR SUM(af_report.install) > 0 OR SUM(af_event.registration) > 0 OR SUM(af_event.purchase) > 0
        ;
      SQL
    AppsflyerReport.connection.execute(sql).to_a
  end

  def click_url_ids
    ClickUrl.includes(:campaign)
    .where(campaigns: {client_id: Client::BINANCE_ID})
    .where.not(vendor_id: test_vendor_ids)
    .live
    .pluck(:id)
  end

  def test_vendor_ids
    @test_vendor_ids ||= Vendor.where(for_test: true).pluck(:id)
  end

  def fe_spend_status(vendor_id)
    if [Vendor::BLASTBUCKS_ID, Vendor::ADCOMMUNAL_ID].include?(vendor_id)
      ['normal', 'injected', 'over_cap']
    else
      ['normal', 'injected', 'over_cap', 'manual_stopped']
    end
  end

  def fe_spend_query(click_url, date)
    vendor_id = click_url.vendor_id
    campaign_id = click_url.campaign_id

    <<-SQL
      select
        sum(spend) as gross_spend,
        sum(net_spend) as net_spend
      from v4_campaigns_view
      where campaign_id = #{campaign_id}
      and vendor_id = #{vendor_id}
      and status in ('#{fe_spend_status(vendor_id).join("','")}')
      and calculate_date between '#{date}' and '#{date}'
    SQL
  end

  def send_notification(list)
    return if list.blank?
    tips = list.map{|item|
      "ClickUrl #{item[:click_url_id]} 在 #{item[:date]} 这一行的数据"
    }.join("\n")
    content = "#{Thread.current[:current_job]} #{self.class.name}: 在处理以下 Click URL 的 Binance Media Plan 时，需要人工介入:\n#{tips}"
    SlackService.send_notification_to_channel(content, :mighty)
  end
end
