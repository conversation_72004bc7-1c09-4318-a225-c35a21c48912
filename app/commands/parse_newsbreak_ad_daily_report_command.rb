# frezen_string_literal: true

class ParseNewsbreakAdDailyReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  NEWSBREAK_QUERY = 'from:<EMAIL> newer_than:2d has:attachment filename:csv subject:Client Feedmob Report | NewsBreak ad daily report'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    result = []

    user_messages = gmail.list_user_messages(user_id, q: NEWSBREAK_QUERY)
    email_tasks = user_messages.messages
    tasks = Array(email_tasks).map do |message|
      Concurrent::Promises.future {
        analyze_email(gmail: gmail, user_id: user_id, message: message)
      }
    end

    result.concat Concurrent::Promises.zip(*tasks).value!

    result.flatten!

    if result.empty?
      errors.add(:invalid, "*No Email In Last One Day Received*")
      [false, "Error", []]
    else
      [true, nil, result]
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    select_datas = []

    data = CSV.parse(attachment_content.data, headers: true).map(&:to_h)
    data.select { |row| row['Date'] == date.to_s }
  end
end
