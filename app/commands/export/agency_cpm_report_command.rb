require "csv"

class Export::AgencyCpmReportCommand
  prepend SimpleCommand

  attr_accessor :report_date, :options

  def initialize(options = {})
    @options = options
    @report_date = options[:report_date]
  end

  def call
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(headers)
      datas.each do |row|
        yielder << CSV.generate_line(row)
      end
    end
  end

  private

  def headers
    [
      'Click Url',
      'Vendor',
      'Campaigns',
      "#{format_date}(Net_Spend)",
      "#{format_date}(Gross_Spend)",
      "Creative"
    ]
  end

  def datas
    rows = []
    agency_tracking_cofing = TrackingConfig.find_by(name: "agency_cpm_report_app_ids")
    return [] if agency_tracking_cofing.blank?
    agency_app_ids = agency_tracking_cofing.value.values.reduce(&:+)

    agency_app_ids.each do |k|
      s3_file_name = s3_file_keys.select{|f| f.include?("#{k}")}
      next if s3_file_name.blank?
      result = rows_builder(s3_file_name[0], k)
      rows += result if result.present?
    end
    rows
  end

  def s3
    @s3 ||= Aws::S3::Client.new(
      region: ENV['REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
  end

  def bucket
    return 'agency-link-conversion-reports' if Rails.env.production?

    'feedmob-testing'
  end

  def prefix
    "agency-link-reverse-postback-logs/#{report_date.to_date.strftime('%Y/%m/%d')}/"
  end

  def s3_file_keys
    s3.list_objects(
      bucket: bucket,
      prefix: prefix
    ).contents.reject { |content| content.size == 0 }.map(&:key)
  end

  def rows_builder(file_name,app_id)
    click_id_arrays = []
    cpm_spend_arrays = []
    result = s3.get_object(bucket: bucket, key: file_name).body.read
    CSV.parse(result, headers: true).each_with_index do |row, index|
      next if row['Sub Param 5'].blank?
      click_id_arrays.push({
        "#{row['Sub Param 5']}": row['Cost Value'].to_f
      })
    end
    click_array_costs = click_id_arrays.reduce {|acc, h| acc.merge(h) {|_,v1,v2| v1 + v2 }}
    return [] if click_array_costs.blank?
    click_array_costs.each do |k, v|
      click_url = ClickUrl.find_by(id: k.to_s.to_i)
      campaign_name = click_url&.campaign&.name
      vendor_name = click_url&.vendor&.vendor_name
      net_spend = (v * 0.7).round(2)
      cpm_spend_arrays << [k.to_s, vendor_name, campaign_name, net_spend, v]
    end
    cpm_spend_arrays
  end

  def format_date
    report_date.to_date.strftime("%m/%d/%Y")
  end
end
