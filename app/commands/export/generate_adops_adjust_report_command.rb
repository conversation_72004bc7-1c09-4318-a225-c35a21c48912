require "csv"

class Export::GenerateAdopsAdjustReportCommand
  prepend SimpleCommand

  attr_accessor :begin_date, :end_date, :client

  def initialize(begin_date:, end_date:, client:)
    @begin_date = begin_date
    @end_date = end_date
    @client = client
  end

  def call
    get_adjust_data
    filename = "#{client[:app_id]}_install_and_event_#{begin_date}-#{end_date}"
    client[:event_url] = generate_csv_url(filename, events_headers(client[:app_id]), client[:event_datas])

    daily_filename = "#{client[:app_id]}_daily_install_and_event_#{begin_date}-#{end_date}"
    client[:daily_event_url] = generate_csv_url(daily_filename, daily_events_headers(client[:app_id]), client[:daily_event_datas])

    {
      month: begin_date.to_date.strftime('%Y-%m'),
      pod_name: pod_name, client_id: client[:client_id], app_name: client[:app_name], app_id: client[:app_id],
      install_report_url: client[:install_url], event_report_url: client[:event_url],
      daily_install_report_url: client[:daily_install_url], daily_event_report_url: client[:daily_event_url],
      raw_install_report_url: client[:raw_install_url], raw_event_report_url: client[:raw_event_url]
    }
  end

  def pod_name
    config = TrackingConfig.find_by(name: 'ADOPS_REPORT_PODS').value.to_h
    config.find{|k,v|client[:client_name].in?(v)}&.first
  end

  def generate_csv_url(filename, headers, datas)
    export_service = ExportService.new(filename, zipping: true)
    export_service.write do |down_file|
      csv_stream = Enumerator.new do |yielder|
        yielder << CSV.generate_line(headers)
        datas.each do |item|
          yielder << CSV.generate_line(item.values)
        end
      end
      File.open(down_file, "w") do |f|
        csv_stream.each do |line|
          f.write(line)
        end
      end
    end

    download_url = export_service.upload_to_s3
  end

  def events_headers(app_id)
    @events_headers = {
      'id1621328561' => ['click_url_id', 'adjust_campaign', 'adjust_channel', 'installs', 'board 5'],
      'com.scopely.monopolygo' => ['click_url_id', 'adjust_campaign', 'adjust_channel', 'installs', 'board 5'],
      'id1206967173' => ['click_url_id', 'adjust_campaign', 'adjust_channel', 'installs'],
      'id1618284064' => ['click_url_id', 'adjust_campaign', 'adjust_channel', 'installs'],
      'com.enflick.android.TextNow' => ['click_url_id', 'adjust_campaign', 'adjust_channel', 'installs', 'Unique Verified Signup', 'Early Mover Event'],
      '314716233' => ['click_url_id', 'adjust_campaign', 'adjust_channel', 'installs', 'Unique Verified Signup', 'Early Mover Event']
    }

    @events_headers[app_id]
  end

  def daily_events_headers(app_id)
    @daily_events_headers = {
      'id1621328561' => ['event_date', 'click_url_id', 'adjust_campaign', 'adjust_channel', 'installs', 'board 5'],
      'com.scopely.monopolygo' => ['event_date', 'click_url_id', 'adjust_campaign', 'adjust_channel', 'installs', 'board 5'],
      'id1206967173' => ['event_date', 'click_url_id', 'adjust_campaign', 'adjust_channel', 'installs'],
      'id1618284064' => ['event_date', 'click_url_id', 'adjust_campaign', 'adjust_channel', 'installs'],
      'com.enflick.android.TextNow' => ['event_date', 'click_url_id', 'adjust_campaign', 'adjust_channel', 'installs', 'Unique Verified Signup', 'Early Mover Event'],
      '314716233' => ['event_date', 'click_url_id', 'adjust_campaign', 'adjust_channel', 'installs', 'Unique Verified Signup', 'Early Mover Event']
    }

    @daily_events_headers[app_id]
  end

  def get_adjust_data
    if client[:client_id] == 3
      scopely_datas = FetchScopelyAdjustEventsCommand.call(start_date: begin_date, end_date: end_date, app_id: client[:store_id]).result
      client[:event_datas], client[:daily_event_datas] = get_scopely_data_from_csv(scopely_datas, client[:events])
    elsif client[:client_id] == 86
      textnow_datas = FetchTextnowAdjustReportCommand.new.fetch_adjust_data(begin_date, end_date)
      app_datas = textnow_datas.select {|c|c['store_id'] == client[:store_id]}
      client[:event_datas], client[:daily_event_datas] = get_textnow_data_from_csv(app_datas, client[:events])
    end

    client
  end

  def get_textnow_data_from_csv(app_datas, events)
    arr = []
    command = FetchTextnowAdjustReportCommand.new
    group_campaign_datas = app_datas.group_by {|c| [c['campaign'], c['partner_name']] }
    group_campaign_datas.each do |key, items|
      installs = items.sum{|c|c['installs'].to_i}
      _hash_events = {}
      events.each do |event|
        _hash_events[event] = items.sum{|c|c[event].to_i}
      end
      next if installs == 0 && _hash_events.values.sum == 0

      adjust_campaign_name, adjust_channel = key
      mapped_click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel, Client::TEXTNOW_ID, false)
      click_url_id = ''
      click_url_id =  mapped_click_url.id.to_s if mapped_click_url.present?

      arr << { click_url_id: click_url_id, adjust_campaign: adjust_campaign_name, adjust_channel: adjust_channel, installs: installs }.merge(_hash_events)
    end

    arr_daily = []
    daily_group_campaign_datas = app_datas.sort_by { |c| c['day'] }.group_by {|c| [c['day'], c['campaign'], c['partner_name']] }
    daily_group_campaign_datas.each do |key, items|
      installs = items.sum{|c|c['installs'].to_i}
      _hash_events = {}
      events.each do |event|
        _hash_events[event] = items.sum{|c|c[event].to_i}
      end
      next if installs == 0 && _hash_events.values.sum == 0

      event_date, adjust_campaign_name, adjust_channel = key
      mapped_click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel, Client::TEXTNOW_ID, false)
      click_url_id = ''
      click_url_id = mapped_click_url.id.to_s if mapped_click_url.present?

      arr_daily << { event_date: event_date, click_url_id: click_url_id, adjust_campaign: adjust_campaign_name, adjust_channel: adjust_channel, installs: installs }.merge(_hash_events)
    end

    [arr, arr_daily]
  end

  def get_scopely_data_from_csv(app_datas, events)
    arr = []
    group_campaign_datas = app_datas.group_by {|c| [c['campaign'], c['partner_name']] }
    group_campaign_datas.each do |key, items|
      installs = items.sum{|c|c['installs'].to_i}
      _hash_events = {}
      events.each do |event|
        _hash_events[event] = items.sum{|c|c[event].to_i}
      end
      next if installs == 0 && _hash_events.values.sum == 0

      adjust_campaign_name, adjust_channel = key
      mapped_click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel,  Client::SCOPELY_ID, false)
      click_url_id = ''
      click_url_id = mapped_click_url.id.to_s if mapped_click_url.present?

      arr << { click_url_id: click_url_id, adjust_campaign: adjust_campaign_name, adjust_channel: adjust_channel, installs: installs }.merge(_hash_events)
    end

    arr_daily = []
    daily_group_campaign_datas = app_datas.sort_by { |c| c['day'] }.group_by {|c| [c['day'], c['campaign'], c['partner_name']] }
    daily_group_campaign_datas.each do |key, items|
      installs = items.sum{|c|c['installs'].to_i}
      _hash_events = {}
      events.each do |event|
        _hash_events[event] = items.sum{|c|c[event].to_i}
      end
      next if installs == 0 && _hash_events.values.sum == 0

      event_date, adjust_campaign_name, adjust_channel = key
      mapped_click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel, Client::SCOPELY_ID, false)
      click_url_id = ''
      click_url_id = mapped_click_url.id.to_s if mapped_click_url.present?

      arr_daily << { event_date: event_date, click_url_id: click_url_id, adjust_campaign: adjust_campaign_name, adjust_channel: adjust_channel, installs: installs }.merge(_hash_events)
    end

    [arr, arr_daily]
  end
end
