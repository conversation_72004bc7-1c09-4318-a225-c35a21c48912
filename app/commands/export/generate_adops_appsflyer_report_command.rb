require "csv"

class Export::GenerateAdopsAppsflyerReportCommand
  prepend SimpleCommand

  attr_accessor :begin_date, :end_date, :client

  def initialize(begin_date:, end_date:, client:)
    @begin_date = begin_date
    @end_date = end_date
    @client = client
  end

  def call
    get_appsflyer_data
    if client[:install].present?
      filename = "#{client[:app_id]}_install_#{begin_date}-#{end_date}"
      client[:install_url] = generate_aggrate_csv_url(filename, install_headers, client[:install_datas])

      daily_filename = "#{client[:app_id]}_daily_install_#{begin_date}-#{end_date}"
      client[:daily_install_url] = generate_aggrate_csv_url(daily_filename, daily_install_headers, client[:daily_install_datas])
    end

    if client[:events].present?
      filename = "#{client[:app_id]}_event_#{begin_date}-#{end_date}"
      client[:event_url] = generate_aggrate_csv_url(filename, events_headers(client[:events]), client[:event_datas])

      daily_filename = "#{client[:app_id]}_daily_event_#{begin_date}-#{end_date}"
      client[:daily_event_url] = generate_aggrate_csv_url(daily_filename, daily_events_headers(client[:events]), client[:daily_event_datas])
    end

    {
      month: begin_date.to_date.strftime('%Y-%m'),
      pod_name: pod_name, client_id: client[:client_id], app_name: client[:app_name], app_id: client[:app_id],
      install_report_url: client[:install_url], event_report_url: client[:event_url],
      daily_install_report_url: client[:daily_install_url], daily_event_report_url: client[:daily_event_url],
      raw_install_report_url: client[:raw_install_url], raw_event_report_url: client[:raw_event_url]
    }
  end

  def pod_name
    config = TrackingConfig.find_by(name: 'ADOPS_REPORT_PODS').value.to_h
    config.find{|k,v|client[:client_name].in?(v)}&.first
  end

  def generate_aggrate_csv_url(filename, headers, datas)
    export_service = ExportService.new(filename, zipping: true)
    export_service.write do |down_file|
      csv_stream = Enumerator.new do |yielder|
        yielder << CSV.generate_line(headers)
        datas.each do |k, v|
          yielder << CSV.generate_line([k, v].flatten)
        end
      end
      File.open(down_file, "w") do |f|
        csv_stream.each do |line|
          f.write(line)
        end
      end
    end

    download_url = export_service.upload_to_s3
  end

  def install_headers
    [
      'click_url_id',
      'install_count'
    ]
  end

  def daily_install_headers
    [
      'install_date',
      'click_url_id',
      'install_count'
    ]
  end

  def events_headers(events)
    ['click_url_id'] + Array(events)
  end

  def daily_events_headers(events)
    ['event_date', 'click_url_id'] + Array(events)
  end

  def get_appsflyer_data
    if client[:events].present?
      client[:event_datas], client[:daily_event_datas], client[:raw_event_url] = get_api_raw_events(client[:client_id], client[:app_id], client[:event_keys], client[:events] )
    end

    if client[:install] == 's3'
      client[:install_datas], client[:daily_install_datas], client[:raw_install_url] = get_s3_raw_installs(client[:client_id], client[:app_id])
    elsif client[:install] == 'api'
      client[:install_datas], client[:daily_install_datas], client[:raw_install_url] = get_api_raw_installs(client[:client_id], client[:app_id])
    end

    client
  end

  def get_s3_raw_installs(client_id, app_id)
    _hash = {}
    _hash_daily = {}

    filename = "#{app_id}_raw_install_#{begin_date}-#{end_date}"
    export_service = ExportService.new(filename, zipping: true)
    export_service.write do |down_file|
      csv_stream = Enumerator.new do |yielder|
        (begin_date.to_date..end_date.to_date).each do |date|
          service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: date, end_date: date)

          service.installs_report do |temple_file|
            return [] if temple_file.blank?
            records = CSV.foreach(temple_file, headers: true)

            if begin_date.to_s == date.to_s && records.first.present?
              headers = records.first.to_h.keys
              header_line = CSV.generate_line(headers)
              yielder << header_line
            end

            records.each do |record|
              values = record.to_h.values
              yielder << CSV.generate_line(values)

              click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
              event_date = record['Event Time'].to_date.to_s
              _hash[click_url_id] = _hash[click_url_id].to_i + 1
              _hash_daily[[event_date, click_url_id]] = _hash_daily[[event_date, click_url_id]].to_i + 1
            end
          end
        end
      end
      File.open(down_file, "w") do |f|
        csv_stream.each do |line|
          f.write(line)
        end
      end
    end
    raw_install_url = export_service.upload_to_s3

    [_hash, _hash_daily, raw_install_url]
  end

  def get_api_raw_installs(client_id, app_id)
    _hash = {}
    _hash_daily = {}
    service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: begin_date, end_date: end_date)

    filename = "#{app_id}_raw_install_#{begin_date}-#{end_date}"
    export_service = ExportService.new(filename, zipping: true)
    export_service.write do |down_file|
      csv_stream = Enumerator.new do |yielder|
        service.installs_report do |temple_file|
          return [] if temple_file.blank?
          records = CSV.foreach(temple_file, headers: true)

          yielder << CSV.generate_line(records.first.to_h.keys)
          records.each do |record|
            yielder << CSV.generate_line(record.to_h.values)

            click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
            event_date = record['Event Time'].to_date.to_s
            _hash[click_url_id] = _hash[click_url_id].to_i + 1
            _hash_daily[[event_date, click_url_id]] = _hash_daily[[event_date, click_url_id]].to_i + 1
          end
        end
      end
      File.open(down_file, "w") do |f|
        csv_stream.each do |line|
          f.write(line)
        end
      end
    end
    raw_install_url = export_service.upload_to_s3

    _hash_daily = _hash_daily.to_a.reverse.to_h
    [_hash, _hash_daily, raw_install_url]
  end

  def get_api_raw_events(client_id, app_id, event_keys, af_events)
    _hash = {}
    _hash_daily = {}

    service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: begin_date, end_date: end_date)

    filename = "#{app_id}_raw_event_#{begin_date}-#{end_date}"
    export_service = ExportService.new(filename, zipping: true)
    export_service.write do |down_file|
      csv_stream = Enumerator.new do |yielder|
        service.selected_in_app_events_report(via_api: false, events: event_keys, af_events: af_events) do |temple_file|
          return [] if temple_file.blank?
          records = CSV.foreach(temple_file, headers: true)

          headers = records.first.to_h.keys
          yielder << CSV.generate_line(headers)

          records.each do |record|
            yielder << CSV.generate_line(record.to_h.values)

            click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
            event_date = record['Event Time'].to_date.to_s
            if af_events.size > 1
              af_event_names = @client[:events]
              current = get_events_count_by(record, af_event_names)
              _hash_counts = _hash[click_url_id].nil? ? get_events_count_by({}, af_event_names) : _hash[click_url_id]
              _hash_daily_counts = _hash_daily[[event_date, click_url_id]].nil? ? get_events_count_by({}, af_event_names) : _hash_daily[[event_date, click_url_id]]

              _hash[click_url_id] = [_hash_counts[0] + current[0], _hash_counts[1] + current[1]]
              _hash_daily[[event_date, click_url_id]] = [_hash_daily_counts[0] + current[0], _hash_daily_counts[1] + current[1]]
            else
              _hash[click_url_id] = _hash[click_url_id].to_i + 1
              _hash_daily[[event_date, click_url_id]] = _hash_daily[[event_date, click_url_id]].to_i + 1
            end
          end
        end
      end
      File.open(down_file, "w") do |f|
        csv_stream.each do |line|
          f.write(line)
        end
      end
    end
    raw_event_url = export_service.upload_to_s3

    _hash_daily = _hash_daily.to_a.reverse.to_h
    [_hash, _hash_daily, raw_event_url]
  end

  def get_events_count_by(record, af_event_names)
    af_event_names.map do |event|
      record['Event Name'] == event ? 1 : 0
    end
  end
end
