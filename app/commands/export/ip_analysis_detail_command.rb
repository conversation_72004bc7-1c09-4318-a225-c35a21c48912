require "csv"
class Export::IpAnalysisDetailCommand
  prepend SimpleCommand

  REPEAT_HEADER = [
    "ID", "Click At", "Campaign", "Vendor", "App", "Conversion ID",
    "Status", "IP", "IP duplicate", "IP Pattern", "Proxy", "Event Time",
    "Device Platform ID", "Click IP", "Install IP", "Duration "
  ].freeze

  attr_reader :campaign_id, :vendor_id, :ip_status, :ip_address, :ip_pattern_address,
              :sequence_from, :sequence_to, :nonbillable, :start_date, :end_date, :develop_mode
  attr_accessor :ip_lookups

  def initialize(options = {})
    @vendor_id          = options[:vendor_id]
    @campaign_id        = options[:campaign_id]
    @start_date         = options[:start_date].try(:to_date)
    @end_date           = options[:end_date].try(:to_date)
    @ip_status          = options[:ip_status]
    @nonbillable        = options[:nonbillable]
    @ip_address         = options[:ip_address]
    @ip_pattern_address = options[:ip_pattern_address].to_s.split('.').first(3)&.join('.')
    @sequence_from      = options[:sequence_from].to_i
    @sequence_to        = options[:sequence_to].to_i
    @develop_mode       = options[:develop] == 'true'
    @ip_lookups         = {}
  end

  def call
    export_by_es
  end

  private

  def export_by_es
    header = ["ID", "Click At", "Event Time", "Campaign", "Vendor", "App", "Conversion ID", "Status", "IP", "IP duplicate", "IP Pattern", "Proxy"]

    CSV.generate(headers: true) do |csv|
      csv << header
      begin
        search_conditions = ip_status == "ip_pattern" ? ip_pattern_conditions : conditions
        results = ElasticsearchClient.search_conversion_records(
          query: { bool: search_conditions },
          sort: [ { ip_last_section: "asc" } ],
          size: 9999
        )
        data = results["hits"]["hits"].map { |item| OpenStruct.new(item["_source"]) }
        if !develop_mode && ip_status == 'ip_duplicate'
          set_ip_repeat_lookup
          remote_ips = ip_lookups.values.map{|r| r.map{|_, v| v.keys}}.flatten.uniq
          search_conditions = conditions
          search_conditions[:must] << {terms: {remote_ip: remote_ips}} if remote_ips.present?
          results = ElasticsearchClient.search_conversion_records(
            query: { bool: conditions },
            sort: [ { ip_last_section: 'asc' } ],
            size: 9999
          )['hits']['hits'].map{|item| OpenStruct.new(item['_source'])}
          es_ip_repeat_data(results, csv)
        else
          results = ElasticsearchClient.search_conversion_records(
            query: { bool: conditions },
            sort: [ { ip_last_section: 'asc' } ],
            size: 9999
          )['hits']['hits'].map{|item| OpenStruct.new(item['_source'])}
          results.each do |cr|
            csv << [
              cr.id,
              cr.click_event_time&.to_datetime,
              cr.event_time&.to_datetime,
              cr.campaign_name,
              cr.vendor_name,
              cr.app_identity,
              cr.conversion_id,
              cr.status,
              cr.remote_ip,
              get_ip_repeat_count(cr),
              get_ip_pattern_count(cr),
              cr.try(:proxy),
            ]
          end
        end
      rescue => e
        Sentry.capture_exception(e)
        nil
      end
    end
  end

  def es_ip_repeat_data(records, csv)
    records.each do |cr|
      next unless ip_lookups[cr.event_date][cr.campaign_id] && ip_lookups[cr.event_date][cr.campaign_id][cr.remote_ip]
      csv << [
        cr.id,
        cr.click_event_time&.to_datetime,
        cr.event_time&.to_datetime,
        cr.campaign_name,
        cr.vendor_name,
        cr.app_identity,
        cr.conversion_id,
        cr.status,
        cr.remote_ip,
        ip_lookups[cr.event_date][cr.campaign_id][cr.remote_ip],
        get_ip_pattern_count(cr),
        cr.try(:proxy),
      ]
    end
  end

  def set_ip_repeat_lookup
    (start_date..end_date).each do |date|
      ip_repeat_condition = [{match: {event_date: date}}, {match: {track_type: "install"}}]
      ip_repeat_condition << {match: {campaign_id: campaign_id.to_i}} if campaign_id.present?

      result = ElasticsearchClient.search_conversion_records(
        query: { bool: { must: ip_repeat_condition } },
        aggregations: {
          campaign: {
            terms: { size: Campaign.count, field: :campaign_id, order: { '_count': 'desc' } },
            aggs: {
              remote_ip: {
                terms: { size: 100000, field: :remote_ip, min_doc_count: 3 }
              }
            }
          }
        }
      )
      ip_lookups[date.to_s] = {}
      result['aggregations']['campaign']['buckets'].each do |campaign_data|
        ip_lookups[date.to_s][campaign_data['key']] = campaign_data['remote_ip']['buckets'].map{|r| [r['key'], r['doc_count']]}.to_h
      end
    end
  end

  def ip_pattern_conditions
    results = ElasticsearchClient.search_conversion_records(
      query: { bool: conditions.merge(must_not: { match: { ip_first3_section: "" } }) },
      sort: [ { ip_last_section: "asc" } ],
      aggs: { ip_pattern: { terms: { field: :ip_first3_section, size: 1000, order: { '_count': "desc" } } } }
    )

    ip_patterns = []
    results["aggregations"]["ip_pattern"]["buckets"].each do |re|
      if re["doc_count"] > 5
        ip_patterns << re["key"]
      else
        break
      end
    end

    if ip_patterns.present?
      { must: conditions[:must].push({ terms: { ip_first3_section: ip_patterns } }) }
    else
      {}
    end
  end

  def conditions
    @conditions ||= begin
      must_conditions = [
        { range: { event_date: { "gte": start_date, "lte": end_date } } },
        { match: { repeated: false } },
        { match: { track_type: "install" } }
      ]

      must_conditions << { match: { vendor_id: vendor_id.to_i } } if vendor_id.present?
      must_conditions << { match: { campaign_id: campaign_id.to_i } } if campaign_id.present?
      must_conditions << { match: { remote_ip: ip_address } } if ip_address.present?
      must_conditions << { match: { ip_first3_section: ip_pattern_address } } if ip_pattern_address.present?

      case ip_status
      when "ip_duplicate"
        must_conditions << { range: { ip_repeat_count: { gt: 2 } } } if develop_mode
      when "ip_proxy"
        must_conditions << { match: { proxy: true } }
      when "ip_sequence"
        must_conditions << { range: { ip_last_section: { gte: sequence_from, lte: sequence_to } } }
      end

      must_conditions << { terms: { status: NewConversionRecord::NonBillable.dup } } if nonbillable.present?

      { must: must_conditions }
    end
  end

  def get_ip_pattern_count(cr)
    NewConversionRecord.get_ip_pattern_count(cr)
  end

  def get_ip_repeat_count(cr)
    NewConversionRecord.get_ip_repeat_count(cr)
  end
end
