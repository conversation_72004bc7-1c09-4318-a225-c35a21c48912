require "csv"

class Export::AppsflyerReportCommand
  prepend SimpleCommand

  attr_reader :date, :report_type, :client_id

  def initialize(date, report_type, client_id)
    @date         = date
    @report_type  = report_type
    @client_id    = client_id
  end

  def call
    mappings.map do |m|
      url = get_app_export_url(m)
      [m.af_app_id, date, m.client.name, url]
    end
  end

  def data
    mappings.map { |m| get_app_hash_data(m) }.flatten
  end

  def get_app_hash_data(mapping)
    key = s3_object_key(mapping)
    obj = s3.get_object(bucket: s3_bucket_name, key: key)
    CSV.parse(obj.body.read, headers: true).map(&:to_h)
  rescue Aws::S3::Errors::NoSuchKey => e
    []
  end

  def get_app_export_url(mapping)
    key = s3_object_key(mapping)
    s3.get_object(bucket: s3_bucket_name, key: key)
    s3_signer.presigned_url(:get_object, bucket: s3_bucket_name, key: key, expires_in: 3600)
  rescue Aws::S3::Errors::NoSuchKey => e
    nil
  end

  def mappings
    query = AppsflyerEventMapping.includes(:client)
    query = query.where(client_id: client_id) if client_id.present?
    query.order(client_id: :desc)
  end

    def s3_object_key(mapping)
    "#{report_type}/#{mapping.client.name.gsub(/\s+/, '')}/#{mapping.af_app_id}/#{file_name(mapping.af_app_id)}"
  end

  def file_name(af_app_id)
    if date >= '2022-12-13'.to_date  # 从2022-12-13开始, S3文件名前缀改为日期范围了
      "#{date.strftime('%Y%m%d')}_#{date.strftime('%Y%m%d')}_#{af_app_id}.csv"
    else
      "#{date.strftime('%Y%m%d')}_#{af_app_id}.csv"
    end
  end

  def s3_bucket_name
    Rails.env.production? ? 'feedmob-backup' : 'feedmob-testing'
  end

  def s3
    @s3 ||= Aws::S3::Client.new(region: "us-east-1", credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY'], ENV['AWS_SECRET_ACCESS_KEY']))
  end

  def s3_signer
    @s3_signer ||= Aws::S3::Presigner.new(client: s3)
  end
end
