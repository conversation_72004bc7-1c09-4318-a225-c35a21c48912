# frozen_string_literal: true

module Export::Scopely
  class CreateScorecardReportCommand
    prepend SimpleCommand

    def initialize(start_date:, end_date:)
      @start_date = start_date
      @end_date = end_date
    end

    def call
      if end_date < start_date
        errors.add(:create_scorecard_report, 'end_date must be greater than or equal to start_date')
        return
      end

      fetch_report_from_db
      fetch_report_from_adjust_api
      fetch_scorecard_mappings_from_db
      if failure?
        return
      end

      if report_from_adjust_dash.blank? && report_from_db.blank?
        errors.add(:create_scorecard_report, 'no data found')
        return
      end

      [create_csv_report_group_by_campaign_vendor, create_csv_report_group_by_app]
    end

    private

    attr_reader :start_date, :end_date, :report_from_adjust_dash, :report_from_db, :scorecard_mappings

    CAMPAIGN_VENDOR_HEADERS = [
      'Campaign',
      'Vendor',
      'Spend',
      'Clicks',
      'Install',
      'D1 Retained users',
      'D3 Retained users',
      'D7 Retained users',
      'All Revenue Total D1',
      'All Revenue Total D3',
      'All Revenue Total D7',
    ].freeze

    APP_HEADERS = [
      'App',
      'Spend',
      'Clicks',
      'Install',
      'D1 Retained users',
      'D3 Retained users',
      'D7 Retained users',
      'All Revenue Total D1',
      'All Revenue Total D3',
      'All Revenue Total D7',
    ].freeze

    def fetch_report_from_adjust_api
      command = FetchReportFromAdjustApiCommand.call(start_date: start_date, end_date: end_date)
      if command.failure?
        errors.add(:create_scorecard_report, command.errors[:fetch_report_from_adjust_api].join(', '))
        return
      end

      @report_from_adjust_dash = command.result.group_by { |r| r.campaign_network }
    end

    def fetch_report_from_db
      command = FetchReportFromDbCommand.call(start_date: start_date, end_date: end_date)
      if command.failure?
        errors.add(:create_scorecard_report, command.errors[:fetch_report_from_db].join(', '))
        return
      end

      @report_from_db = command.result.index_by { |r| [r.campaign_id, r.vendor_id] }
    end

    def fetch_scorecard_mappings_from_db
      report_campaign_networks = report_from_adjust_dash&.keys
      @scorecard_mappings = ScopelyScorecardMapping.kept.where(report_campaign_network: report_campaign_networks).index_by(&:report_campaign_network)
    end

    def create_csv_report_group_by_campaign_vendor
      CSV.generate do |csv|
        csv << CAMPAIGN_VENDOR_HEADERS

        aggregate_report_by_campaign_vendor.each do |(campaign, vendor), report|
          csv << [
            campaign,
            vendor,
            report&.sum {|item| item.spend.to_f },
            report&.sum {|item| item.clicks.to_i },
            report&.sum {|item| item.installs.to_i },
            report&.sum {|item| item.retained_users_d1.to_i },
            report&.sum {|item| item.retained_users_d3.to_i },
            report&.sum {|item| item.retained_users_d7.to_i },
            report&.sum {|item| item.all_revenue_total_d1.to_f },
            report&.sum {|item| item.all_revenue_total_d3.to_f },
            report&.sum {|item| item.all_revenue_total_d7.to_f },
          ]
        end
      end
    end

    def create_csv_report_group_by_app
      CSV.generate do |csv|
        csv << APP_HEADERS

        aggregate_report_by_app.each do |app, report|
          csv << [
            app,
            report&.sum {|item| item.spend.to_f },
            report&.sum {|item| item.clicks.to_i },
            report&.sum {|item| item.installs.to_i },
            report&.sum {|item| item.retained_users_d1.to_i },
            report&.sum {|item| item.retained_users_d3.to_i },
            report&.sum {|item| item.retained_users_d7.to_i },
            report&.sum {|item| item.all_revenue_total_d1.to_f },
            report&.sum {|item| item.all_revenue_total_d3.to_f },
            report&.sum {|item| item.all_revenue_total_d7.to_f },
          ]
        end
      end
    end

    def aggregate_report_by_campaign_vendor
      map_report_to_scorecard.group_by { |r| [r.campaign, r.vendor, r.campaign_id, r.vendor_id] }
        .map do |(campaign, vendor, campaign_id, vendor_id), report|
          db_report = report_from_db[[campaign_id, vendor_id]]
          OpenStruct.new(
            campaign: campaign,
            vendor: vendor,
            spend: db_report&.spend.to_f,
            clicks: report.sum {|item| item.clicks.to_i },
            installs: report.sum {|item| item.installs.to_i },
            retained_users_d1: report.sum {|item| item.retained_users_d1.to_i },
            retained_users_d3: report.sum {|item| item.retained_users_d3.to_i },
            retained_users_d7: report.sum {|item| item.retained_users_d7.to_i },
            all_revenue_total_d1: report.sum {|item| item.all_revenue_total_d1.to_f },
            all_revenue_total_d3: report.sum {|item| item.all_revenue_total_d3.to_f },
            all_revenue_total_d7: report.sum {|item| item.all_revenue_total_d7.to_f },
          )
        end.group_by { |r| [r.campaign, r.vendor] }
    end

    def aggregate_report_by_app
      map_report_to_scorecard.group_by { |r| [r.app, r.campaign_id, r.vendor_id] }
        .map do |(app, campaign_id, vendor_id), report|
          db_report = report_from_db[[campaign_id, vendor_id]]
          OpenStruct.new(
            app: app,
            spend: db_report&.spend.to_f,
            clicks: report.sum {|item| item.clicks.to_i },
            installs: report.sum {|item| item.installs.to_i },
            retained_users_d1: report.sum {|item| item.retained_users_d1.to_i },
            retained_users_d3: report.sum {|item| item.retained_users_d3.to_i },
            retained_users_d7: report.sum {|item| item.retained_users_d7.to_i },
            all_revenue_total_d1: report.sum {|item| item.all_revenue_total_d1.to_f },
            all_revenue_total_d3: report.sum {|item| item.all_revenue_total_d3.to_f },
            all_revenue_total_d7: report.sum {|item| item.all_revenue_total_d7.to_f },
          )
        end.group_by { |r| r.app }
    end

    def map_report_to_scorecard
      @map_report_to_scorecard ||=
        report_from_adjust_dash.map do |campaign_network, adjust_report|
          scorecard_mapping = scorecard_mappings[campaign_network]
          OpenStruct.new(
            campaign: scorecard_mapping&.report_campaign.presence || campaign_network,
            vendor: scorecard_mapping&.report_vendor,
            app: scorecard_mapping&.report_app,
            campaign_id: scorecard_mapping&.campaign_id,
            vendor_id: scorecard_mapping&.vendor_id,
            clicks: adjust_report.sum {|item| item.clicks.to_i },
            installs: adjust_report.sum {|item| item.installs.to_i },
            retained_users_d1: adjust_report.sum {|item| item.retained_users_d1.to_i },
            retained_users_d3: adjust_report.sum {|item| item.retained_users_d3.to_i },
            retained_users_d7: adjust_report.sum {|item| item.retained_users_d7.to_i },
            all_revenue_total_d1: adjust_report.sum {|item| item.all_revenue_total_d1.to_f },
            all_revenue_total_d3: adjust_report.sum {|item| item.all_revenue_total_d3.to_f },
            all_revenue_total_d7: adjust_report.sum {|item| item.all_revenue_total_d7.to_f },
          )
        end
    end
  end
end
