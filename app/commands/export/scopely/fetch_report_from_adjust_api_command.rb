module Export::<PERSON><PERSON>ly
  class FetchReportFromAdjustApiCommand < FetchReportBaseCommand
    prepend SimpleCommand
 
    def initialize(start_date:, end_date:)
      @start_date = start_date
      @end_date = end_date
    end

    def call
      if !validate_arguments
        return
      end

      res = make_request_to_adjust
      if res.code != 200
        errors.add(:fetch_report_from_adjust_api, res.code)
        return
      end

      parse_response_to_struct_array(res)
    end

    private

    attr_reader :start_date, :end_date

    API_HOST = 'https://dash.adjust.com'.freeze
    SCOPELY_ADJUST_ACCOUNT_ID = '1091'.freeze

    def access_token
      @access_token ||= ENV['SCOPELY_ADJUST_API_ACCESS_TOKEN']
    end

    def validate_arguments
      if end_date < start_date
        errors.add(:fetch_report_from_adjust_api, 'end_date must be greater than or equal to start_date')
        return false
      end

      if access_token.blank?
        errors.add(:fetch_report_from_adjust_api, 'access_token is not set')
        return false
      end

      true
    end

    def make_request_to_adjust
      Retriable.retriable(base_interval: 3) do
        HTTParty.get(
          "#{API_HOST}/control-center/reports-service/csv_report?#{adjust_request_queries}",
          headers: { 'Authorization' => "Bearer #{access_token}", 'x-account-id' => SCOPELY_ADJUST_ACCOUNT_ID })
      end
    end

    def adjust_request_queries
      {
        full_data: true,
        attribution_source: :first,
        ad_spend_mode: :network,
        reattributed: :all,
        attribution_type: :all,
        readable_names: false,
        format_dates: false,
        metrics: 'attribution_clicks,installs,all_revenue_total_d1,all_revenue_total_d3,all_revenue_total_d7,retained_users_d1,retained_users_d3,retained_users_d7',
        dimensions: 'campaign_network',
        assisting_attribution_type: :all,
        cohort_maturity: :immature,
        ironsource_mode: :ironsource,
        sandbox: false,
        utc_offset: '+00:00',
        date_period: "#{start_date}:#{end_date}", # -7d:-1d
        sort: '-installs'
      }.to_query

    end

    def parse_response_to_struct_array(response)
      csv_data = response.body.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding('UTF-8'), '')

      CSV.parse(csv_data, headers: true).map do |row|
        OpenStruct.new(
          campaign_network: row['campaign_network'],
          clicks: row['attribution_clicks'],
          installs: row['installs'],
          all_revenue_total_d1: row['all_revenue_total_d1'],
          all_revenue_total_d3: row['all_revenue_total_d3'],
          all_revenue_total_d7: row['all_revenue_total_d7'],
          retained_users_d1: row['retained_users_d1'],
          retained_users_d3: row['retained_users_d3'],
          retained_users_d7: row['retained_users_d7'],
        )
      end
    end
  end
end
