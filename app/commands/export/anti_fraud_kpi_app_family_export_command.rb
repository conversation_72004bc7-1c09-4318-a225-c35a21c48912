require "csv"

class Export::AntiFraudKpiAppFamilyExportCommand
  prepend SimpleCommand

  attr_accessor :start_date, :end_date, :client_id, :kpi_metrics, :options

  RESULT_ATTRIBUTES = [
    :campaign_name,
    :vendor_name,
    :app_family,
    :impression_count,
    :click_count,
    :install_count,
    :retained_count,
    :tutorial_count,
    :registration_count,
    :purchase_count,
    :first_purchase_count,
    :level_count,
    :open_count,
    :first_event_a_count,
    :spend,
    :net_spend,
    :revenue_0,
    :revenue_1,
    :revenue_3,
    :revenue_5,
    :revenue_7,
    :revenue_14,
    :revenue_30,
    :revenue_60,
    :status_mtti_sub_thres_count,
    :status_dismatch_country_count,
    :status_dismatch_device_count,
    :status_rejected_by_third_party_count,
    :sub_first_hour_count,
    :ip_proxy_count,
    :fingerprint_count,
    :total_cr_count,
    :total_install_count,
    :tutorial_retention_1,
    :tutorial_retention_3,
    :tutorial_retention_5,
    :tutorial_retention_7,
    :tutorial_retention_14,
    :tutorial_retention_30,
    :open_retention_0,
    :open_retention_1,
    :open_retention_3,
    :open_retention_5,
    :open_retention_7,
    :open_retention_14,
    :open_retention_30,
    :registration_open_retention_0,
    :registration_open_retention_1,
    :registration_open_retention_3,
    :registration_open_retention_5,
    :registration_open_retention_7,
    :registration_open_retention_14,
    :registration_open_retention_30,
    :registration_tutorial_retention_0,
    :registration_tutorial_retention_1,
    :registration_tutorial_retention_3,
    :registration_tutorial_retention_5,
    :registration_tutorial_retention_7,
    :registration_tutorial_retention_14,
    :registration_tutorial_retention_30,
    :hyperactive_total_clicks,
    :hyperactive_count
  ].freeze

  Result = Struct.new(*RESULT_ATTRIBUTES) do
    include KpiConcern
  end

  def initialize(options = {})
    @client_id = options[:client_id]
    @kpi_metrics = options[:kpi_metrics]
    @options = options
    @start_date = options[:start_date]
    @end_date = options[:end_date]
    @limit = 10000
    @offset = 0
  end

  def call
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(headers)
      loop do
        rows = ConversionRecordRedshift.connection.query(sql)
        rows.each do |row|
          yielder << CSV.generate_line(csv_report_row(Result.new(*row)))
        end
        @offset += @limit
        break if rows.to_a.size < @limit
      end
    end
  end

  private

  def headers
    title = [
      'CAMPAIGN',
      'VENDOR',
      'APP FAMILY',
      'IMPRESSION',
      'CLICKS',
      'INSTALLS',

      'RETAINED',
      'TUTORIAL',
      'REGISTRATION',
      'PURCHASE',
      'FIRST PURCHASE',
      'LEVEL',
      'OPEN',
      'FIRST EVENT A',

      'SPEND',
      'NET SPEND',
      'CPI',
      'NET CPI',
      'CVR',
    ]

    if kpi_metrics.present?
      kpi_metrics.map do |metric|
        next unless  %w[cpa cvr2 roas retention registrationretention].include?(metric[:metric])

        if metric[:metric] == 'cvr2'
          title << "#{metric[:event]} COUNT".upcase
          title << "#{metric[:event]}(#{metric[:metric]})".upcase
        elsif metric[:metric] == 'roas'
          title << "ROAS #{metric[:day]} DAY".upcase
          title << "ROAS #{metric[:day]} DAY PERCENT".upcase
        elsif metric[:metric] == 'retention'
          title << "#{metric[:event]} RETENTION #{metric[:day]} DAY".upcase
          title << "#{metric[:event]} RETENTION #{metric[:day]} DAY PERCENT".upcase
        elsif metric[:metric] == 'registrationretention'
          title << "REGISTRATION RETENTION #{metric[:day]} DAY".upcase
          title << "REGISTRATION RETENTION #{metric[:day]} DAY PERCENT".upcase
        elsif metric[:metric] == 'cpa'
          title << "#{metric[:event]} cpa".upcase
          title << "#{metric[:event]} count".upcase
        end
      end
    end

    title << 'MTTI'
    title << 'MTTI PERCENTAGE'

    title << 'COUNTRY MISMATCH'
    title << 'COUNTRY MISMATCH PERCENTAGE'

    title << 'DEVICE MISMATCH'
    title << 'DEVICE MISMATCH PERCENTAGE'

    title << 'REJECTED BY THIRD PARTY'
    title << 'REJECTED BY THIRD PARTY PERCENTAGE'

    title << 'FIRST HOUR INSTALL'
    title << 'FIRST HOUR INSTALL PERCENTAGE'

    title << 'IP PROXY'
    title << 'IP PROXY PERCENTAGE'

    title << 'FINGERPRINT'
    title << 'FINGERPRINT PERCENTAGE'

    title << 'TOTAL CLICKS WITH DEVICE IDs'
    title << 'HYPERACTIVE CLICKS PERCENTAGE'

    title << 'TOTAL INSTALL COUNT'
    title << 'TOTAL CONVERSION COUNT'

    title
  end

  def csv_report_row(result)
    csv_row = []

    csv_row << result.campaign_name
    csv_row << result.vendor_name
    csv_row << result.app_family
    csv_row << result.impression_count
    csv_row << result.click_count
    csv_row << result.install_count

    csv_row << result.retained_count
    csv_row << result.tutorial_count
    csv_row << result.registration_count
    csv_row << result.purchase_count
    csv_row << result.first_purchase_count
    csv_row << result.level_count
    csv_row << result.open_count
    csv_row << result.first_event_a_count

    csv_row << result.spend
    csv_row << result.net_spend
    csv_row << result.cpi.round(2)
    csv_row << result.net_cpi.round(2)
    csv_row << to_percentage(result.cvr)

    if kpi_metrics.present?
      kpi_metrics.map do |metric|
        next unless %w[cpa cvr2 roas retention registrationretention].include?(metric[:metric])

        if metric[:metric] == 'cvr2'
          method = "#{metric[:event]}_#{metric[:metric].to_s.downcase}"

          if display_post_open_event_value?(metric[:event])
            csv_row << result.send("#{metric[:event]}_count")
            csv_row << to_percentage(result.send(method))
          else
            csv_row << 0
            csv_row << to_percentage(0)
          end
        elsif metric[:metric] == 'roas'
          csv_row << result.send("revenue_#{metric[:day]}")
          csv_row << to_percentage(result.send("roas_#{metric[:day]}"))
        elsif metric[:metric] == 'retention'
          csv_row << result.send("#{metric[:event]}_retention_#{metric[:day]}")
          csv_row << to_percentage(result.event_retention(metric[:event], metric[:day]))
        elsif metric[:metric] == 'registrationretention'
          csv_row << result.send("registration_#{metric[:event]}_retention_#{metric[:day]}")
          csv_row << to_percentage(result.event_registration_retention(metric[:event], metric[:day]))
        elsif metric[:metric] == 'cpa'
          if display_post_open_event_value?(metric[:event])
            csv_row << result.send("#{metric[:event]}_cpa").floor(2)
            csv_row << result.send("#{metric[:event]}_count")
          else
            csv_row << 0
            csv_row << 0
          end
        end
      end
    end

    csv_row << result.status_mtti_sub_thres_count
    csv_row << to_percentage(fraud_ratio('status_mtti_sub_thres_count', result))

    csv_row << result.status_dismatch_country_count
    csv_row << to_percentage(fraud_ratio('status_dismatch_country_count', result))

    csv_row << result.status_dismatch_device_count
    csv_row << to_percentage(fraud_ratio('status_dismatch_device_count', result))

    csv_row << result.status_rejected_by_third_party_count
    csv_row << to_percentage(fraud_ratio('status_rejected_by_third_party_count', result))

    csv_row << result.sub_first_hour_count
    csv_row << to_percentage(fraud_ratio('sub_first_hour_count', result))

    csv_row << result.ip_proxy_count
    csv_row << to_percentage(fraud_ratio('ip_proxy_count', result))

    csv_row << result.fingerprint_count
    csv_row << to_percentage(fraud_ratio('fingerprint_count', result))

    csv_row << result.hyperactive_total_clicks
    csv_row << to_percentage(result.hyperactive_click_rate)

    csv_row << result.total_install_count
    csv_row << result.total_cr_count

    csv_row
  end

  def sql
    <<-SQL
      SELECT
      campaign_name,
      vendor_name,
      (CASE WHEN app_id IN ('no_fm_app_id', 'fm_less_than_thirty') THEN app_id WHEN app_id !~ '^.*[-|_].*$' THEN app_id ELSE split_part(replace(app_id, '-', '_'), '_', 1) END) AS app_family,
      #{count_query}
      FROM anti_fraud_kpi_view
      WHERE (event_date BETWEEN '#{start_date}' AND '#{end_date}')
      AND #{conditions}
      GROUP BY 1, 2, 3
      ORDER BY 1, 2, 3 ASC
      LIMIT #{@limit} OFFSET #{@offset}
    SQL
  end

  def count_query
    <<-SQL.squish
      SUM(impression_count) AS impression_count,
      SUM(click_count) AS click_count,
      SUM(install_count) AS install_count,
      SUM(#{cohort_normalize :retained_count}) AS retained_count,
      SUM(#{cohort_normalize :tutorial_count}) AS tutorial_count,
      SUM(#{cohort_normalize :registration_count}) AS registration_count,
      SUM(#{cohort_normalize :purchase_count}) AS purchase_count,
      SUM(#{cohort_normalize :first_purchase_count}) AS first_purchase_count,
      SUM(#{cohort_normalize :level_count}) AS level_count,
      SUM(#{cohort_normalize :open_count}) AS open_count,
      SUM(#{cohort_normalize :first_event_a_count}) AS first_event_a_count,
      SUM(spend) AS spend,
      SUM(net_spend) AS net_spend,
      SUM(revenue_0) AS revenue_0,
      SUM(revenue_1) AS revenue_1,
      SUM(revenue_3) AS revenue_3,
      SUM(revenue_5) AS revenue_5,
      SUM(revenue_7) AS revenue_7,
      SUM(revenue_14) AS revenue_14,
      SUM(revenue_30) AS revenue_30,
      SUM(revenue_60) AS revenue_60,
      SUM(status_mtti_sub_thres_count) AS status_mtti_sub_thres_count,
      SUM(status_dismatch_country_count) AS status_dismatch_country_count,
      SUM(status_dismatch_device_count) AS status_dismatch_device_count,
      SUM(status_rejected_by_third_party_count) AS status_rejected_by_third_party_count,
      SUM(#{install_normalize :sub_first_hour_count}) AS sub_first_hour_count,
      SUM(#{install_normalize :ip_proxy_count}) AS ip_proxy_count,
      SUM(#{install_normalize :fingerprint_count}) AS fingerprint_count,
      SUM(total_cr_count) AS total_cr_count,
      SUM(total_install_count) AS total_install_count,
      SUM(tutorial_retention_1) AS tutorial_retention_1,
      SUM(tutorial_retention_3) AS tutorial_retention_3,
      SUM(tutorial_retention_5) AS tutorial_retention_5,
      SUM(tutorial_retention_7) AS tutorial_retention_7,
      SUM(tutorial_retention_14) AS tutorial_retention_14,
      SUM(tutorial_retention_30) AS tutorial_retention_30,
      SUM(open_retention_0) AS open_retention_0,
      SUM(open_retention_1) AS open_retention_1,
      SUM(open_retention_3) AS open_retention_3,
      SUM(open_retention_5) AS open_retention_5,
      SUM(open_retention_7) AS open_retention_7,
      SUM(open_retention_14) AS open_retention_14,
      SUM(open_retention_30) AS open_retention_30,
      SUM(registration_open_retention_0) AS registration_open_retention_0,
      SUM(registration_open_retention_1) AS registration_open_retention_1,
      SUM(registration_open_retention_3) AS registration_open_retention_3,
      SUM(registration_open_retention_5) AS registration_open_retention_5,
      SUM(registration_open_retention_7) AS registration_open_retention_7,
      SUM(registration_open_retention_14) AS registration_open_retention_14,
      SUM(registration_open_retention_30) AS registration_open_retention_30,
      SUM(registration_tutorial_retention_0) AS registration_tutorial_retention_0,
      SUM(registration_tutorial_retention_1) AS registration_tutorial_retention_1,
      SUM(registration_tutorial_retention_3) AS registration_tutorial_retention_3,
      SUM(registration_tutorial_retention_5) AS registration_tutorial_retention_5,
      SUM(registration_tutorial_retention_7) AS registration_tutorial_retention_7,
      SUM(registration_tutorial_retention_14) AS registration_tutorial_retention_14,
      SUM(registration_tutorial_retention_30) AS registration_tutorial_retention_30,
      SUM(hyperactive_total_clicks) AS hyperactive_total_clicks,
      SUM(hyperactive_count) AS hyperactive_count
    SQL
  end

  def cohort_normalize(field)
    cohort? ? "cohort_#{field}" : field
  end

  def cohort?
    options[:cohort] == 'cohort'
  end

  def install_normalize(field)
    normal_install? ? field : "total_#{field}"
  end

  def normal_install?
    options[:install_type] == 'normal'
  end

  def conditions
    sql_condition = []
    sql_condition << "client_id = #{options[:client_id]}" if options[:client_id].present?
    sql_condition << "campaign_id IN (#{campaign_ids.join(',')})"  if campaign_ids.present?
    sql_condition << "vendor_id IN (#{ vendor_id.present? ? vendor_id.join(',') : 'FALSE' })"

    return '1=0' if sql_condition.blank?
    "#{sql_condition.join(' AND ')}"
  end

  def campaign_ids
    @campaign_ids ||= begin
     command_options = {
       campaign_ids:    options[:campaign_id],
       parent_campaign_id: options[:parent_campaign_id],
       develop:         options[:develop]
     }
     CampaignIdExtractionCommand.call(command_options).result
   end
  end

  def vendor_id
    @vendor_id ||= begin
      command_options = {
        vendor_id:         options[:vendor_id],
        vendor_collection: options[:vendor_collection],
        vendor_owner_id:   options[:vendor_owner_id],
        excluded_vendor_id: options[:excluded_vendor_id],
        develop:           options[:develop]
      }

      VendorIdExtractionCommand.call(command_options).result
    end
  end

  def fraud_ratio(field, result)
    installs = normal_install? ? result.install_count : result.total_install_count
    installs.to_i.zero? ? 0 : result.send(field).to_i  / installs.to_d
  end

  # Only Uber and Retailme display post_open, others display 0
  def display_post_open_event_value?(event)
    if ![6, 17].include?(client_id.to_i) && options[:cohort] == 'cohort' && event == 'open'
      false
    else
      true
    end
  end

  def to_percentage(num)
    "#{(num * 100).round(2)} %"
  end
end
