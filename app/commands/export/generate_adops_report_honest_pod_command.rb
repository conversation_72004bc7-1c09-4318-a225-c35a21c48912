require "csv"

class Export::GenerateAdopsReportHonestPodCommand
  prepend SimpleCommand
  include SingularApiConcern

  attr_accessor :begin_date, :end_date, :appsflyer_clients, :adjust_clients, :singular_clients, :report_ids, :possible_finance_reports

  def initialize(begin_date:, end_date:, report_ids: [])
    @begin_date = begin_date
    @end_date = end_date
    @report_ids = report_ids

    @appsflyer_clients = [
      { client_name: '<PERSON>tor', client_id: 151, app: 'Realtor iOS', app_id: 'id336698281', install: 's3'},
      { client_name: 'Redfin', client_id: 119, app: 'Redfin iOS', app_id: 'id327962480', install: 'api' },
      { client_name: 'Redfin', client_id: 119, app: 'Redfin Android', app_id: 'com.redfin.android', install: 'api' },
      { client_name: 'Stash', client_id: 118, app: 'Stash iOS', app_id: 'id1017148055', install: 'api', events: ['deposit_initiated'], event_keys: ['L']},
      { client_name: 'Stash', client_id: 118, app: 'Stash Android', app_id: 'com.stash.stashinvest', install: 'api', events: ['deposit_initiated'], event_keys: ['L']},
    ]

    @adjust_clients = [
      { client_name: 'TextNow', client_id: 86, app: 'TextNow Android', app_id: 'com.enflick.android.TextNow', store_id: 'com.enflick.android.TextNow', events: ["unique_signup_events", "early_mover_event_events"] },
      { client_name: 'TextNow', client_id: 86, app: 'TextNow iOS', app_id: '314716233', store_id: '314716233', events: ["unique_signup_events", "early_mover_event_events"] },
    ]

    @singular_clients = [
      { client_name: 'Possible Finance', client_id: 172, app: 'Possible Finance iOS', app_id: 'id1380384597', store_id: '1380384597', install: 'api' },
      { client_name: 'Possible Finance', client_id: 172, app: 'Possible Finance Android', app_id: 'com.possible_mobile', store_id: 'com.possible_mobile', install: 'api' },
    ]
  end

  def call
    get_appsflyer_data
    get_adjust_data
    get_singular_data

    daily_datas = []
    @appsflyer_clients.each do |client|
      daily_install_datas = client[:daily_install_datas].to_h
      daily_event_datas = client[:daily_event_datas].to_h
      uniq_keys = daily_install_datas.keys.to_a + daily_event_datas.keys.to_a
      uniq_keys.uniq.sort.each do |key|
        event_date, click_url_id = key
        next if click_url_id.blank?
        daily_data = {'event_date' => event_date, 'click_url_id' => click_url_id, 'install' => daily_install_datas[key].to_i}
        daily_datas << daily_data.merge(daily_event_datas[key].to_h)
      end
    end

    @adjust_clients.each do |client|
      daily_install_event_datas = client[:daily_install_event_datas].to_a
      daily_install_event_datas.each do |key, events|
        event_date, click_url_id = key
        next if click_url_id.blank?
        daily_data = {'event_date' => event_date, 'click_url_id' => click_url_id}
        daily_datas << daily_data.merge(events.to_h)
      end
    end

    @singular_clients.each do |client|
      daily_install_event_datas = client[:daily_install_event_datas].to_a
      daily_install_event_datas.each do |key, events|
        event_date, click_url_id = key
        next if click_url_id.blank?
        daily_data = {'event_date' => event_date, 'click_url_id' => click_url_id}
        daily_datas << daily_data.merge(events.to_h)
      end
    end

    daily_headers = daily_datas.map {|c| c.keys }.flatten.uniq

    filename = "KeyPod_install_and_event_#{begin_date}-#{end_date}"
    daily_url = generate_aggrate_csv_url(filename, daily_headers, daily_datas)
    {pod: 'KeyPod', daily_url: daily_url}
  end

  def generate_aggrate_csv_url(filename, headers, datas)
    export_service = ExportService.new(filename, zipping: true)
    export_service.write do |down_file|
      csv_stream = Enumerator.new do |yielder|
        yielder << CSV.generate_line(headers)
        datas.each do |item|
          yielder << CSV.generate_line(headers.map {|header| item[header] })
        end
      end
      File.open(down_file, "w") do |f|
        csv_stream.each do |line|
          f.write(line)
        end
      end
    end

    download_url = export_service.upload_to_s3
  end

  def get_singular_report_ids
    report_id = fetch_report_id('possible_finance_inc_', begin_date, end_date)
    [report_id]
  end

  def get_singular_data
    @possible_finance_reports = []
    @report_ids.each do |report_id|
      download_url = fetch_download_url(report_id)
      reports = fetch_singular_report(download_url)
      @possible_finance_reports << reports
    end
    @possible_finance_reports = @possible_finance_reports.flatten

    @singular_clients.select{|c|c[:client_id] == 172}.each do |client|
      client[:daily_install_event_datas] = get_install_from_singular_possible_finance(client[:store_id])
    end
  end

  def get_install_from_singular_possible_finance(store_id)
    _hash_daily = {}
    @possible_finance_reports.select{|c|c['site_public_id'] == store_id}.group_by do |row|
      [row['start_date'], get_click_url_id(row['affiliate_id'])]
    end.each do |key, items|
      install_count = items.sum{|item| item['custom_installs'].to_i}
      _hash_daily[key] = { 'install' => install_count } if install_count > 0
    end

    _hash_daily
  end


  def get_adjust_data
    textnow_datas = FetchTextnowAdjustReportCommand.new.fetch_adjust_data(begin_date, end_date)
    @adjust_clients.select{|c|c[:client_id] == 86}.each do |client|
      app_datas = textnow_datas.select {|c|c['store_id'] == client[:store_id]}
      client[:daily_install_event_datas] = get_textnow_data_from_csv(app_datas, client[:events])
    end

    @adjust_clients
  end

  def get_textnow_data_from_csv(app_datas, events)
    arr_daily = []
    daily_group_campaign_datas = app_datas.sort_by { |c| c['day'] }.group_by {|c| [c['day'], c['campaign'], c['partner_name']] }
    daily_group_campaign_datas.each do |key, items|
      installs = items.sum{|c|c['installs'].to_i}
      _hash_events = {}
      events.each do |event|
        _hash_events[event] = items.sum{|c|c[event].to_i}
      end
      next if installs == 0 && _hash_events.values.sum == 0

      event_date, adjust_campaign_name, adjust_channel = key
      mapped_click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel, Client::TEXTNOW_ID, false, false)
      click_url_id = ''
      click_url_id = mapped_click_url.id.to_s if mapped_click_url.present?

      arr_daily << { event_date: event_date, click_url_id: click_url_id, adjust_campaign: adjust_campaign_name, adjust_channel: adjust_channel, install: installs }.merge(_hash_events)
    end

    _hash_daily = {}
    arr_daily_group = arr_daily.group_by {|c| [c[:event_date], c[:click_url_id]] }
    arr_daily_group.each do |key, items|
      install_count = items.sum{|c| c[:install] }

      daily = {'install' => install_count}
      events.each do |event|
        daily[event] = items.sum{|c| c[event] }
      end
      _hash_daily[key] = daily
    end

    # 数据结构 {[event_date, click_url_id] => { 'install' => 10, 'board_11_events' => 5 } }
    _hash_daily
  end

  def get_appsflyer_data
    @appsflyer_clients.each do |client|
      if client[:events].present?
        client[:daily_event_datas] = get_api_raw_events(client[:client_id], client[:app_id], client[:event_keys], client[:events] )
      end

      if client[:install] == 's3'
        client[:daily_install_datas] = get_s3_raw_installs(client[:client_id], client[:app_id])
      elsif client[:install] == 'api'
        client[:daily_install_datas] = get_api_raw_installs(client[:client_id], client[:app_id])
      end
    end

    @appsflyer_clients
  end

  def get_s3_raw_installs(client_id, app_id)
    _hash_daily = {}
    (begin_date.to_date..end_date.to_date).each do |date|
      service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: date, end_date: date)

      service.installs_report do |temple_file|
        return [] if temple_file.blank?
        records = CSV.foreach(temple_file, headers: true)

        records.each do |record|
          click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
          event_date = record['Event Time'].to_date.to_s
          _hash_daily[[event_date, click_url_id]] = _hash_daily[[event_date, click_url_id]].to_i + 1
        end
      end
    end

    _hash_daily
  end

  def get_api_raw_installs(client_id, app_id)
    _hash_daily = {}
    service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: begin_date, end_date: end_date)

    service.installs_report do |temple_file|
      return [] if temple_file.blank?
      records = CSV.foreach(temple_file, headers: true)

      records.each do |record|
        click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
        event_date = record['Event Time'].to_date.to_s
        _hash_daily[[event_date, click_url_id]] = _hash_daily[[event_date, click_url_id]].to_i + 1
      end
    end

    _hash_daily = _hash_daily.to_a.reverse.to_h
    _hash_daily
  end

  def get_api_raw_events(client_id, app_id, event_keys, af_events)
    _hash_daily = {}

    service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: begin_date, end_date: end_date)
    service.selected_in_app_events_report(via_api: false, events: event_keys, af_events: af_events) do |temple_file|
      return [] if temple_file.blank?
      records = CSV.foreach(temple_file, headers: true)

      records.each do |record|
        event_date = record['Event Time'].to_date.to_s
        event_name = record['Event Name'].to_s
        click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)

        event_count = _hash_daily[[event_date, click_url_id]].to_h
        event_count[event_name] = event_count[event_name].to_i + 1
        _hash_daily[[event_date, click_url_id]] = event_count
      end
    end

    # 数据结构 {[event_date, click_url_id] => { 'cobrokeLead' => 10 } }
    _hash_daily
  end
end
