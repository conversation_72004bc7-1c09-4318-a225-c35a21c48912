require "csv"

class Export::FeSourcesAppFamilyExportCommand
  prepend SimpleCommand
  include RefreshDataService

  RESULT_ATTRIBUTES = [
    :vendor_id,
    :vendor_name,
    :campaign_id,
    :campaign_name,
    :click_id, 
    :paid_action,
    :app_family,
    :first_click_date,
    :click_count,
    :impression_attributed_click_count,
    :impression_count,
    :install_count,
    :event_count,
    :spend,
    :gross_spend,
    :net_spend,
    :rejected_by_third_party_count
  ].freeze

  Result = Struct.new(*RESULT_ATTRIBUTES) do 
    include FeExportConcern
  end

  attr_reader :options, :conditions, :all_status_conditions, :start_date, :develop, :install_statuses, :spend_statuses, :event, :spend, :limit, :offset, :cohort

  def initialize(options = {})
    @options = options
    @conditions = options[:conditions]
    @all_status_conditions = options[:all_status_conditions]
    @start_date = options[:start_date]
    @develop = options[:develop]
    @install_statuses = options[:install_statuses]
    @spend_statuses = options[:spend_statuses]
    @event = options[:event] || "purchase"
    @spend = options[:spend] == 'net' ? 'net_spend' : 'spend'
    @cohort = options[:cohort] == 'non-cohort' ? false : true
    @per_page = 10000
    @offset = 0
  end

  def call
    get_approved_countries
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(headers)
      loop do
        rows = ConversionRecordRedshift.connection.execute(sql)
        rows.each do |row|
          yielder << CSV.generate_line(csv_report_row(Result.new(*row.values)))
        end
        @offset += @per_page
        break if rows.to_a.size < @per_page
      end
    end
  end

  def sql
    read_sql_from_file("app/data_views/fe_sources_app_family_export.sql", {
      start_date: start_date.to_date, 
      develop: develop, 
      install_statuses: install_statuses, 
      spend_statuses: spend_statuses,
      event: event,
      spend: spend,
      conditions: conditions,
      init_event_count: cohort ? "post_#{event}_count" : "#{event}_count",
      per_page: @per_page,
      offset: @offset
      })
  end

  private

  def headers
    ["APP FAMILY", "CAMPAIGN", "VENDOR","VENDOR ID", "CLICK URL ID", "PAID ACTION", "IMPRESSION", "CLICK", "CTR2",
      "INSTALL", "REJECTED BY THIRD PARTY", "#{@event.upcase} EVENTS","SPEND","MARGIN","NET REVENUE", "CPI", "CPA", "CVR", "CVR2","COUNTRY", "FIRST CLICK DATE"]
  end

  def csv_report_row(result)
    csv_row = []
    csv_row << result.app_family
    csv_row << result.campaign_name
    csv_row << result.vendor_name
    csv_row << result.vendor_id
    csv_row << result.click_id
    csv_row << result.display_paid_action
    csv_row << result.impression_count
    csv_row << result.click_count
    csv_row << "#{result.ctr2.round(2)}%"
    csv_row << result.install_count
    csv_row << result.rejected_by_third_party_percentage
    csv_row << result.event_count
    csv_row << result.spend
    csv_row << "#{result.margin.round(2)}%"
    csv_row << result.net_revenue
    csv_row << result.cpi.round(2)
    csv_row << result.cpa.round(2)
    csv_row << "#{result.cvr.round(2)}%"
    csv_row << "#{result.cvr2.round(2)}%"
    csv_row << get_click_country(result.click_id)
    csv_row << result.first_click_date
  end

  def get_approved_countries
    @approved_countries ||= ClickUrl.pluck(:id, :approved_countries)
  end

  def get_click_country(click_id)
    return "" if @approved_countries.blank?
    get_country = @approved_countries.find {|k| k[0].to_i == click_id.to_i}
    get_country.present? ? get_country[1].join(",") : ""
  end
end