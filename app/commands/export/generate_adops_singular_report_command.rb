require "csv"

class Export::GenerateAdopsSingularReportCommand
  prepend SimpleCommand
  include SingularApiConcern
  UBER_PURCHASE = ['7f07e6259473411ea43d4e3b68400c8a', 'e2938e2411a342bfb6df0959efbe393c']

  attr_accessor :begin_date, :end_date, :client, :report_ids, :possible_finance_reports, :uber_reports

  def initialize(begin_date:, end_date:, client:, report_ids: [])
    @begin_date = begin_date
    @end_date = end_date
    @client = client
    @report_ids = report_ids
  end

  def call
    fetch_reports

    if client[:install].present?
      client[:install_datas] = get_install_from_singular_possible_finance(client[:store_id])
      filename = "#{client[:app_id]}_install_#{begin_date}-#{end_date}"
      client[:install_url] = generate_csv_url(filename, install_headers, client[:install_datas])
    end

    if client[:events].present?
      client[:event_datas] = get_event_from_singular_uber(client[:store_id])
      filename = "#{client[:app_id]}_event_#{begin_date}-#{end_date}"
      client[:event_url] = generate_csv_url(filename, events_headers(client[:client_name]), client[:event_datas])
    end

    {
      month: begin_date.to_date.strftime('%Y-%m'),
      pod_name: pod_name, client_id: client[:client_id], app_name: client[:app_name], app_id: client[:app_id],
      install_report_url: client[:install_url], event_report_url: client[:event_url],
      daily_install_report_url: client[:daily_install_url], daily_event_report_url: client[:daily_event_url],
      raw_install_report_url: client[:raw_install_url], raw_event_report_url: client[:raw_event_url]
    }
  end

  def pod_name
    config = TrackingConfig.find_by(name: 'ADOPS_REPORT_PODS').value.to_h
    config.find{|k,v|client[:client_name].in?(v)}&.first
  end

  def get_report_ids
    report_id = fetch_report_id('possible_finance_inc_', begin_date, end_date)
    [report_id]
  end

  def fetch_reports
    if client[:client_id] == 172
      @possible_finance_reports = Rails.cache.fetch("adops_report:possible_finance_reports", expires_in: 5.minutes) do
        _possible_finance_reports = []
        @report_ids.each do |report_id|
          download_url = fetch_download_url(report_id)
          reports = fetch_singular_report(download_url)
          _possible_finance_reports << reports
        end
        _possible_finance_reports = _possible_finance_reports.flatten
      end
    elsif client[:client_id] == 169
      @uber_reports = Rails.cache.fetch("adops_report:fetch_uber_all_share_report", expires_in: 5.minutes) do
        fetch_uber_all_share_report(begin_date, end_date).dig('value', 'results')
      end
    end
  end

  def generate_csv_url(filename, headers, datas)
    export_service = ExportService.new(filename, zipping: true)
    export_service.write do |down_file|
      csv_stream = Enumerator.new do |yielder|
        yielder << CSV.generate_line(headers)
        datas.each do |item|
          yielder << CSV.generate_line(item)
        end
      end
      File.open(down_file, "w") do |f|
        csv_stream.each do |line|
          f.write(line)
        end
      end
    end

    download_url = export_service.upload_to_s3
  end

  def install_headers
    [
      'app_id',
      'source',
      'app',
      'singular_campaign_name',
      'click_url_id',
      'installs'
    ]
  end

  def events_headers(client_name)
    base = ['app_id', 'source', 'app', 'singular_campaign_name']
    events = if client_name == 'Lyft'
      ['Passenger Activation Count']
    else
      [
        'Eats First Order Count',
        'Rider First Trip Count'
      ]
    end
    base + events
  end

  def daily_events_headers(client_name)
    base = ['event_date', 'app_id', 'source', 'app', 'singular_campaign_name']
    events = if client_name == 'Lyft'
      ['Passenger Activation Count']
    else
      [
        'Eats First Order Count',
        'Rider First Trip Count'
      ]
    end
    base + events
  end

  def get_install_from_singular_possible_finance(store_id)
    @possible_finance_reports.to_a.select{|c|c['site_public_id'] == store_id}.group_by do |row|
      [row['site_public_id'], row['source'], row['app'], row['unified_campaign_name'], get_click_url_id(row['affiliate_id'])]
    end.map do |k, items|
      event_count = items.sum{|item| item['custom_installs'].to_i}

      [k, event_count].flatten
    end
  end

  def get_event_from_singular_uber(store_id)
    reports = @uber_reports.to_a
    reports.select{|c|c['site_public_id'] == store_id && c['source'].in?(['Feedmob Agency', 'FeedMob']) }.group_by do |row|
      [row['site_public_id'], row['source'], row['app'], row['unified_campaign_name']]
    end.map do |k, items|
      uuid_events = UBER_PURCHASE
      event_count = uuid_events.map do |event|
        items.sum{|item|item[event]['actual'].to_i}
      end

      [k, event_count].flatten
    end
  end

end
