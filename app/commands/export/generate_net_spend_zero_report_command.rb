require "csv"

class Export::GenerateNetSpendZeroReportCommand
  prepend SimpleCommand

  attr_accessor :begin_date, :end_date

  def initialize(begin_date:, end_date:)
    @begin_date = begin_date
    @end_date = end_date
  end

  def call
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(headers)
      datas.each do |row|
        yielder << CSV.generate_line(row)
      end
    end
  end

  private

  def headers
    [
      'click_url_id',
      'client_name',
      'campaign_name',
      'vendor_name',
      'created_at',
      'paused_at',
      'status',
      'link_type',
      'total_click_count',
      'normal_install_count',
      'install_count',
      'vendor_paid_action',
      'count of the vendor_paid_action',
      'total_net_spend'
    ]
  end

  def datas
    sql = <<-SQL
      with records as (
        SELECT
        t2.click_url_id,
        t2.client_name,
        t1.campaign_name,
        t1.vendor_name,
        t2.vendor_paid_action,
        sum(click_count) as total_click_count,
        sum(case when status = 'normal' then install_count else 0 end) as normal_install_count,
        sum(install_count) as install_count,
        sum(case
          when t2.vendor_paid_action = 1   then install_count
          when t2.vendor_paid_action = 2   then retained_count
          when t2.vendor_paid_action = 3   then tutorial_count
          when t2.vendor_paid_action = 4   then registration_count
          when t2.vendor_paid_action = 5   then first_purchase_count
          when t2.vendor_paid_action = 6   then level_count
          when t2.vendor_paid_action = 7   then open_count
          when t2.vendor_paid_action = 8   then impression_count
          when t2.vendor_paid_action = 9   then purchase_count
          when t2.vendor_paid_action = 10  then click_count
          when t2.vendor_paid_action = 100 then all_event_a_count
          when t2.vendor_paid_action = 101 then first_event_a_count
          when t2.vendor_paid_action = 102 then all_event_b_count
          when t2.vendor_paid_action = 103 then first_event_b_count
          else 0 end
        ) as vendor_paid_action_count,
        sum(net_spend) as total_net_spend
        FROM v4_campaigns_view as t1
        left join click_url_infos as t2 on t2.campaign_id = t1.campaign_id and t2.vendor_id = t1.vendor_id and t2.event_date = t1.calculate_date
        WHERE calculate_date BETWEEN '#{begin_date}' and '#{end_date}'
        AND t1.vendor_id NOT IN (#{test_vendor_ids.join(', ')})
        AND t1.campaign_id NOT IN (#{test_campaign_ids.join(', ')})
        group by 1,2,3,4,5
      )

      SELECT * FROM records
      WHERE total_net_spend = 0 and (total_click_count > 100 OR normal_install_count > 1)
      order by 2,4;
    SQL
    rows = ConversionRecordRedshift.connection.execute(sql).to_a

    rows.map do |row|
      click_url = ClickUrl.find_by(id: row['click_url_id'])
      [row['click_url_id'], row['client_name'], row['campaign_name'], row['vendor_name'],click_url&.created_at.to_s, click_url&.paused_time.to_s, click_url&.status,
       click_url&.link_type, row['total_click_count'], row['normal_install_count'], row['install_count'], ClickUrl.vendor_paid_actions.invert[row['vendor_paid_action'].to_i], row['vendor_paid_action_count'], row['total_net_spend']]
    end
  end

  def test_vendor_ids
    @test_vendor_ids ||= Vendor.where(for_test: true).pluck(:id)
  end

  def test_campaign_ids
    @test_campaign_ids ||= Campaign.joins(:client).where('clients.for_test': true).pluck('campaigns.id')
  end

end
