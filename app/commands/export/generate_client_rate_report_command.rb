require "csv"

class Export::GenerateClientRateReportCommand
  prepend SimpleCommand

  attr_accessor :begin_date, :end_date

  def initialize(begin_date:, end_date:)
    @begin_date = begin_date
    @end_date = end_date
  end

  def call
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(headers)
      datas.each do |row|
        yielder << CSV.generate_line(row)
      end
    end
  end

  private

  def headers
    [
      'event_date',
      'click_url_id',
      'client_name',
      'campaign_name',
      'vendor_name',
      'net_rate',
      'gross_rate',
      'margin'
    ]
  end

  def datas
    sql = <<-SQL
      with click_url_ids as (
      SELECT
        DISTINCT t2.click_url_id
      FROM
        v4_campaigns_view AS t1
      JOIN click_url_infos AS t2 ON t1.vendor_id = t2.vendor_id
        AND t1.campaign_id = t2.campaign_id
        AND t1.calculate_date = t2.event_date
        AND spend > 0
      WHERE t1.calculate_date BETWEEN '#{begin_date}' AND '#{end_date}'
        AND t1.vendor_name not ilike '%test%'
        AND t1.campaign_name not ilike '%test%'
      )

      SELECT
        t2.event_date,
        t2.click_url_id,
        t2.client_name,
        t2.campaign_name,
        t2.vendor_name,
        t2.net_cpi as net_rate,
        t2.gross_cpi as gross_rate,
        t2.margin as margin
      FROM
        click_url_infos as t2
      where click_url_id in (select * from click_url_ids)
      and event_date BETWEEN '#{begin_date}' AND '#{end_date}'
      order by 3,2,1 desc;
    SQL

    rows = ConversionRecordRedshift.connection.execute(sql).to_a
    rows.map do |row|
      click_url = ClickUrl.find_by(id: row['click_url_id'])
      [row['event_date'], row['click_url_id'], row['client_name'], row['campaign_name'], row['vendor_name'], 
        ClickUrl.show_rate(row['net_rate']), 
        ClickUrl.show_rate(row['gross_rate']), 
        ClickUrl.show_margin(row['margin'])
      ]
    end
  end

  def arbitrage_click_url_ids
    arbitrage_click_url_ids = ClickUrl.arbitrage_click_url_ids
    arbitrage_click_url_ids = [-999] if arbitrage_click_url_ids.blank?
    arbitrage_click_url_ids
  end

end
