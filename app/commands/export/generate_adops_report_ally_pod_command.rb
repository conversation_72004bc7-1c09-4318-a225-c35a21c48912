require "csv"

class Export::GenerateAdopsReportAllyPodCommand
  prepend SimpleCommand

  attr_accessor :begin_date, :end_date, :appsflyer_clients, :adjust_clients

  def initialize(begin_date:, end_date:)
    @begin_date = begin_date
    @end_date = end_date

    @appsflyer_clients = [
      { client_name: 'Chime', client_id: 74, app: 'Chime iOS', app_id: 'id836215269', events: ['successful_enrollment', 'Enrollment_Submitted'], event_keys: [] },
      { client_name: 'Chime', client_id: 74, app: 'Chime Android', app_id: 'com.onedebit.chime', install: 's3', events: ['successful_enrollment', 'Enrollment_Submitted'], event_keys: [] },
      { client_name: 'Binance', client_id: 189, app: 'Binance iOS', app_id: 'id1436799971', events: ['af_first_trade'], event_keys: ['P']},
      { client_name: 'Binance', client_id: 189, app: 'Binance Android', app_id: 'com.binance.dev', events: ['af_first_trade'], event_keys: ['P']},
      { client_name: 'KOH<PERSON>', client_id: 184, app: 'KOHO iOS', app_id: 'id1091010942', events: ['first_funded'], event_keys: ['P']},
      { client_name: 'KOHO', client_id: 184, app: 'KOHO Android', app_id: 'ca.koho', events: ['first_funded'], event_keys: ['P']},
      { client_name: 'Upside', client_id: 171, app: 'Upside Android', app_id: 'com.upside.consumer.android', install: 'api', events: ['Sign up', 'firstOfferReconciled'], event_keys: ['REG', 'P']},
      { client_name: 'Upside', client_id: 171, app: 'Upside iOS', app_id: 'id1099997174', install: 'api', events: ['Sign up', 'firstOfferReconciled'], event_keys: ['REG', 'P']},
      { client_name: 'Scopely', client_id: 3, app: 'GSN Casino iOS', app_id: 'id469231420', install: 'api'},
      { client_name: 'Scopely', client_id: 3, app: 'GSN Casino Android', app_id: 'com.gsn.android.casino', install: 'api'},
      { client_name: 'Mistplay', client_id: 198, app: 'Mistplay Android', app_id: 'com.mistplay.mistplay', install: 'api'}
    ]

    @adjust_clients = [
      { client_name: 'Scopely', client_id: 3, app: 'MonopolyGo iOS', app_id: 'id1621328561', store_id: '1621328561', events: ["board_5_events"] },
      { client_name: 'Scopely', client_id: 3, app: 'MonopolyGo Android', app_id: 'com.scopely.monopolygo', store_id: 'com.scopely.monopolygo', events: ["board_5_events"] },
      { client_name: 'Scopely', client_id: 3, app: 'Yahtzee iOS', app_id: 'id1206967173', store_id: '1206967173', events: [] },
      { client_name: 'Scopely', client_id: 3, app: 'GardenJoy iOS', app_id: 'id1618284064', store_id: '1618284064', events: [] }
    ]
  end

  def call
    get_appsflyer_data
    get_adjust_data

    daily_datas = []
    @appsflyer_clients.each do |client|
      daily_install_datas = client[:daily_install_datas].to_h
      daily_event_datas = client[:daily_event_datas].to_h
      uniq_keys = daily_install_datas.keys.to_a + daily_event_datas.keys.to_a
      uniq_keys.uniq.sort.each do |key|
        event_date, click_url_id = key
        next if click_url_id.blank?
        daily_data = {'event_date' => event_date, 'click_url_id' => click_url_id, 'install' => daily_install_datas[key].to_i}
        daily_datas << daily_data.merge(daily_event_datas[key].to_h)
      end
    end

    @adjust_clients.each do |client|
      daily_install_event_datas = client[:daily_install_event_datas].to_a
      daily_install_event_datas.each do |key, events|
        event_date, click_url_id = key
        next if click_url_id.blank?
        daily_data = {'event_date' => event_date, 'click_url_id' => click_url_id}
        daily_datas << daily_data.merge(events.to_h)
      end
    end

    daily_headers = daily_datas.map {|c| c.keys }.flatten.uniq

    filename = "AllyPod_install_and_event_#{begin_date}-#{end_date}"
    daily_url = generate_aggrate_csv_url(filename, daily_headers, daily_datas)
    {pod: 'AllyPod', daily_url: daily_url}
  end

  def generate_aggrate_csv_url(filename, headers, datas)
    export_service = ExportService.new(filename, zipping: true)
    export_service.write do |down_file|
      csv_stream = Enumerator.new do |yielder|
        yielder << CSV.generate_line(headers)
        datas.each do |item|
          yielder << CSV.generate_line(headers.map {|header| item[header] })
        end
      end
      File.open(down_file, "w") do |f|
        csv_stream.each do |line|
          f.write(line)
        end
      end
    end

    download_url = export_service.upload_to_s3
  end

  def get_adjust_data
    @adjust_clients.select{|c|c[:client_id] == 3}.each do |client|
      scopely_datas = FetchScopelyAdjustEventsCommand.call(start_date: begin_date, end_date: end_date, app_id: client[:store_id]).result
      client[:daily_install_event_datas] = get_scopely_data_from_csv(scopely_datas, client[:events])
    end

    @adjust_clients
  end

  def get_scopely_data_from_csv(app_datas, events)
    arr_daily = []
    daily_group_campaign_datas = app_datas.sort_by { |c| c['day'] }.group_by {|c| [c['day'], c['campaign'], c['partner_name']] }
    daily_group_campaign_datas.each do |key, items|
      installs = items.sum{|c|c['installs'].to_i}
      _hash_events = {}
      events.each do |event|
        _hash_events[event] = items.sum{|c|c[event].to_i}
      end
      next if installs == 0 && _hash_events.values.sum == 0

      event_date, adjust_campaign_name, adjust_channel = key
      mapped_click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel, Client::SCOPELY_ID, false, false)
      click_url_id = ''
      click_url_id = mapped_click_url.id.to_s if mapped_click_url.present?

      arr_daily << { event_date: event_date, click_url_id: click_url_id, adjust_campaign: adjust_campaign_name, adjust_channel: adjust_channel, install: installs }.merge(_hash_events)
    end

    _hash_daily = {}
    arr_daily_group = arr_daily.group_by {|c| [c[:event_date], c[:click_url_id]] }
    arr_daily_group.each do |key, items|
      install_count = items.sum{|c| c[:install] }

      daily = {'install' => install_count}
      events.each do |event|
        daily[event] = items.sum{|c| c[event] }
      end
      _hash_daily[key] = daily
    end

    # 数据结构 {[event_date, click_url_id] => { 'install' => 10, 'board_11_events' => 5 } }
    _hash_daily
  end

  def get_appsflyer_data
    @appsflyer_clients.each do |client|
      if client[:events].present?
        client[:daily_event_datas] = get_api_raw_events(client[:client_id], client[:app_id], client[:event_keys], client[:events] )
      end

      if client[:install] == 's3'
        client[:daily_install_datas] = get_s3_raw_installs(client[:client_id], client[:app_id])
      elsif client[:install] == 'api'
        client[:daily_install_datas] = get_api_raw_installs(client[:client_id], client[:app_id])
      end
    end

    @appsflyer_clients
  end

  def get_s3_raw_installs(client_id, app_id)
    _hash_daily = {}
    (begin_date.to_date..end_date.to_date).each do |date|
      service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: date, end_date: date)

      service.installs_report do |temple_file|
        return [] if temple_file.blank?
        records = CSV.foreach(temple_file, headers: true)

        records.each do |record|
          click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
          event_date = record['Event Time'].to_date.to_s
          _hash_daily[[event_date, click_url_id]] = _hash_daily[[event_date, click_url_id]].to_i + 1
        end
      end
    end

    _hash_daily
  end

  def get_api_raw_installs(client_id, app_id)
    _hash_daily = {}
    service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: begin_date, end_date: end_date)

    service.installs_report do |temple_file|
      return [] if temple_file.blank?
      records = CSV.foreach(temple_file, headers: true)

      records.each do |record|
        click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
        event_date = record['Event Time'].to_date.to_s
        _hash_daily[[event_date, click_url_id]] = _hash_daily[[event_date, click_url_id]].to_i + 1
      end
    end

    _hash_daily = _hash_daily.to_a.reverse.to_h
    _hash_daily
  end

  def get_api_raw_events(client_id, app_id, event_keys, af_events)
    _hash_daily = {}

    service = AppsflyerApiServiceV2.new(af_app_id: app_id, client_id: client_id, start_date: begin_date, end_date: end_date)
    service.selected_in_app_events_report(via_api: false, events: event_keys, af_events: af_events) do |temple_file|
      return [] if temple_file.blank?
      records = CSV.foreach(temple_file, headers: true)

      records.each do |record|
        event_date = record['Event Time'].to_date.to_s
        event_name = record['Event Name'].to_s
        click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)

        event_count = _hash_daily[[event_date, click_url_id]].to_h
        event_count[event_name] = event_count[event_name].to_i + 1
        _hash_daily[[event_date, click_url_id]] = event_count
      end
    end

    # 数据结构 {[event_date, click_url_id] => { 'cobrokeLead' => 10 } }
    _hash_daily
  end
end
