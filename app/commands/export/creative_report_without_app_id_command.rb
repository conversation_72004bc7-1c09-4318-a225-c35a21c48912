require "csv"

class Export::CreativeReportWithoutAppIdCommand
  prepend SimpleCommand

  attr_reader :options, :event, :conditions, :install_statuses, :normalized_event

  def initialize(options = {})
    @options = options
    @event = options[:event]
    @conditions = options[:conditions]
    @install_statuses = options[:install_statuses]
    @normalized_event = options[:normalized_event]
    @limit = 10000
    @offset = 0
  end

  def call
    Enumerator.new do |yielder|
      yielder << CSV.generate_line(headers)
      loop do
        break if data_total.zero?
        rows = ConversionRecordRedshift.connection.execute(sql)
        rows.each do |row|
          yielder << CSV.generate_line(row.values)
        end
        @offset += @limit
        break if rows.to_a.size < @limit
      end
    end
  end

  def sql
    <<-SQL
      SELECT
        campaign_name,
        vendor_name,
        creative,
        SUM(CASE WHEN impression IS NULL THEN 0 ELSE impression END) AS impression_count,
        SUM(CASE WHEN click IS NULL THEN 0 ELSE click END) AS click_count,
        SUM(CASE WHEN status IN (#{install_statuses.join(',')}) THEN first_install ELSE 0 END) AS install_count,
        SUM(#{normalized_event}) AS event_count
      FROM v5_stat_records
      #{conditions}
      GROUP BY 1,2,3
      ORDER BY 1,2,3 ASC
      LIMIT #{@limit}
      OFFSET #{@offset}
    SQL
  end

  def data_total
    sql = <<-SQL.squish
      SELECT
        SUM(CASE WHEN impression IS NULL THEN 0 ELSE impression END) AS impression_total,
        SUM(CASE WHEN click IS NULL THEN 0 ELSE click END) AS click_total,
        SUM(CASE WHEN status IN (#{install_statuses.join(',')}) THEN first_install ELSE 0 END) AS install_total,
        SUM(#{normalized_event}) AS event_total
      FROM v5_stat_records
      #{conditions}
    SQL

    sql_result = ConversionRecordRedshift.connection.execute(sql).to_a[0]
    total_data = 0
    ["impression_total", "click_total", "install_total", "event_total"].map do |i|
      total_data += sql_result[i].to_i
    end
    total_data
  end

  def headers
    ["CAMPAIGN", "VENDOR", "CREATIVE ID", "IMPRESSION", "CLICK", "INSTALL", "#{event.upcase} EVENTS"]
  end
end
