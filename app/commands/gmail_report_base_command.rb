# frezen_string_literal: true

require 'googleauth'
require 'googleauth/stores/redis_token_store'
require 'google/apis/gmail_v1'

class GmailReportBaseCommand
  include FileUtils

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'.freeze
  SCOPE = 'https://www.googleapis.com/auth/gmail.readonly'.freeze

  private

  def get_credentials
    Timeout::timeout 60.seconds do
      credentials = refresh_token_credential || authorizer.get_credentials(ENV['GMAIL_USER_ID'])
      if credentials.nil? || credentials.expired?
        url = authorizer.get_authorization_url(base_url: OOB_URI )
        puts "Open #{url} in your browser and enter the resulting code:"
        code = gets
        credentials = authorizer.get_and_store_credentials_from_code(user_id: ENV['GMAIL_USER_ID'], code: code, base_url: OOB_URI)
      end
      return credentials
    end
  rescue Timeout::Error => e
    SlackService.send_notification_to_channel("#{self.class}: Gmail token was expired.", :mighty)
    raise e
  end

  def refresh_token_credential
    token = token_store.load(ENV['GMAIL_USER_ID'])
    return unless token.present?
    cred_json_text = MultiJson.dump({
      client_secret: client_id.secret,
      client_id: client_id.id,
      refresh_token: JSON.parse(token)["refresh_token"]
    })
    Google::Auth::UserRefreshCredentials.make_creds(
      json_key_io: StringIO.new(cred_json_text),
      scope: SCOPE
    )
  end

  def authorizer
    @authorizer ||= Google::Auth::UserAuthorizer.new(
      client_id, SCOPE, token_store
    )
  end

  def client_id
    @client_id ||= Google::Auth::ClientId.from_file(client_secrets_file_path)
  end

  def token_store
    @token_store ||= Google::Auth::Stores::RedisTokenStore.new(redis: RedisClient.main)
  end

  def client_secrets_file_path
    File.join(current_path, "config", 'gmail_client_secrets.json')
  end

  def current_path
    Dir["#{Rails.root}"]
  end
end
