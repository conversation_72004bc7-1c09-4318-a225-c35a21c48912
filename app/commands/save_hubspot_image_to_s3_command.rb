# frozen_string_literal: true

class SaveHubspotImageToS3Command
  prepend SimpleCommand

  def initialize(image_url)
    if image_url.blank?
      errors.add(:image_url, "Image URL can't be blank.")
      return
    end

    @image_api_url = format_image_api_url(image_url)
  end

  def call
    image_info = get_image_info_from_api
    save_image_to_s3(image_info)
  end

  private
    attr_reader :image_api_url

    def format_image_api_url(image_url)
      # https://api.hubspot.com/filemanager/api/v2/files/176178340658/signed-url-redirect?portalId=2992171
      image_url.strip.gsub("signed-url-redirect", "signed-url") # fix the url
    end

    def get_image_info_from_api
      response = HubSpot::Client.get(image_api_url)
      if response.success?
        result = response.parsed_response
        return OpenStruct.new(url: result["url"], name: result["name"], extension: result["extension"])
      end

      errors.add(:image_url, "Failed to fetch image from HubSpot API.")
    end

    def save_image_to_s3(image_info)
      if image_info.blank? || image_info.url.blank?
        errors.add(:image_url, "Image URL can't be blank.")
        return
      end

      temp_image_file = download_image_to_tmp_file(image_info)
      return if temp_image_file.blank?

      s3_key = generate_filename(image_info)
      s3_client.put_object(bucket: bucket_name, key: s3_key, body: temp_image_file)
      "https://#{bucket_name}.s3.amazonaws.com/#{s3_key}"
    rescue Aws::S3::Errors::ServiceError => e
      errors.add(:image_url, e.message)
    ensure
      # Clean up temporary file
      temp_image_file.close
      temp_image_file.unlink
    end

    def download_image_to_tmp_file(image_info)
      response = HTTParty.get(image_info.url)
      if response.success?
        name = generate_filename(image_info)
        temp_file = Tempfile.new(name)
        temp_file.binmode
        temp_file.write(response.body)
        temp_file.rewind
        return temp_file
      end

      errors.add(:image_url, "Failed to download image from url #{url}.")
    end

    def generate_filename(image_info)
      "hubspot/#{SecureRandom.hex(16)}__#{image_info.name}.#{image_info.extension}"
    end

    def bucket_name
      @bucket_name ||= Rails.env.production? ? "tracking-download" : "tracking-env-stage"
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new(region: ENV["AWS_S3_REGION"] || "us-east-1")
    end
end
