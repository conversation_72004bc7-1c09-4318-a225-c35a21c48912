# frezen_string_literal: true

class CheckBinanceMediaPlanDailyReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1mhEKh0pcV3AFLy0bza-0H8FLOLYtzhJa4DkuslRWNCw'.freeze
  attr_reader :start_date, :end_date, :smadex_report_id

  def initialize(start_date, end_date, smadex_report_id)
    @start_date = start_date
    @end_date = end_date
    @smadex_report_id = smadex_report_id
    @service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    values = @service.read_values("Margin and Performance Tracking")
    google_datas = process_spreadsheet_values(values)

    diff_datas = []
    (start_date.to_date..end_date.to_date).each do |date|
      report_datas = CheckBinanceReportDataCommand.call(date, smadex_report_id).result
      report_datas.each do |data|
        google_data = google_datas.find {|c|
          google_date = Date.strptime(c['Date'], '%m/%d/%Y').to_s rescue nil
          google_date == Date.strptime(data['Date'], '%m/%d/%Y').to_s && c['Click URL'] == data['Click URL']
        }.to_h

        google_net = google_data['Partner Dashboard Spend'].to_s.gsub(/[$,]/, '').to_f.round(2)
        google_gross = google_data['SPEND Gross'].to_s.gsub(/[$,]/, '').to_f.round(2)
        diff_net = google_net - data['Partner Dashboard Spend'].to_f
        diff_gross = google_gross - data['SPEND Gross'].to_f

        if diff_net.abs >= 1 || diff_gross.abs >= 1
          diff_datas << data.merge({
            'Partner Dashboard Spend(google)' => google_net,
            'SPEND Gross(google)' => google_gross,
            'Diff Net' => diff_net.round(2),
            'Diff Gross' => diff_gross.round(2)
          })
        end
      end
    end

    diff_datas
  end

  def process_spreadsheet_values(values)
    if values.empty?
      return []
    end

    headers = values.shift # 获取表头行
    result = []
    values.each do |row|
      hash = {}
      headers.each_with_index do |header, index|
        hash[header] = row[index] unless row[index].nil? # 处理空单元格
      end
      result << hash
    end
    result
  end
end
