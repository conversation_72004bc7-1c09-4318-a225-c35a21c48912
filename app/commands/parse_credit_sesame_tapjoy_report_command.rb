# frezen_string_literal: true

class ParseCreditSesameTapjoyReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  CHIME_NEWSBREAK_QUERY = 'from:<EMAIL> newer_than:1d subject:TapJoyEvent Report'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    user_messages = gmail.list_user_messages(user_id, q: CHIME_NEWSBREAK_QUERY)
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:no_email, "*No Email In Last One Day Received*")
      0
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    email_content = email.payload.parts.last.body.data
    doc = Nokogiri::HTML(email_content)
    report_link = doc.xpath("//a").first['href']
    response = HTTParty.get(report_link)
    csv_content = CSV.parse(response.body, headers: true)
    csv_content.select{|row| row['event_name'] == 'Signup successful' && row['timestamp_utc'][0..9] == date.to_s}.size
  end
end
