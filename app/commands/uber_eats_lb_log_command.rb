class UberEatsLbLogCommand
  prepend SimpleCommand

  include FileUtils

  attr_reader :date, :client, :format_date, :next_date, :app_id

  def initialize(date, app_id = nil)
    @app_id = app_id
    @date = date.to_date
    @next_date = date.to_date + 1.day
    @format_date = date.to_s.gsub("-", "/")
    @client = Aws::Athena::Client.new(region: 'us-east-1', credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY'], ENV['AWS_SECRET_ACCESS_KEY']))
  end

  # 创建/ 查询 athena 上表的
  def call
    Enumerator.new do |yielder|
      response = athena_query_exection(query_athena_sql)
      query_info = get_query_info(response.query_execution_id)

      if query_info[0] == 'SUCCEEDED'
        results_response = client.get_query_results(query_execution_id: response.query_execution_id)
        results = results_response.result_set.rows
        results.each {|row| yielder << CSV.generate_line(row.data.map{|c|c.var_char_value}) }

        while results_response.next_token.present?
          results_response = client.get_query_results(query_execution_id: response.query_execution_id, next_token: results_response.next_token)
          results = results_response.result_set.rows
          results.each {|row| yielder << CSV.generate_line(row.data.map{|c|c.var_char_value}) }
        end

        if results.count == 1
          SlackService.send_notification_to_star("GenerateAllPingGaidReportJob generates suppression list for #{app_id} with #{results.count - 1} rows, please check!")
        end
      else
        SlackService.send_notification_to_star("GenerateAllPingGaidReportJob Generate data failed, please check!")
      end
    end
  end

  # 创建 athena 表的
  def create_athena_table
    delete_old_records = delete_athena_table
    if delete_old_records[0] == 'SUCCEEDED'
      response = athena_query_exection(create_table_query)
      get_query_info(response.query_execution_id)
    else
      delete_old_records
    end
  end

  # 删除 athena 上表的
  def delete_athena_table
    drop_table_query = "DROP TABLE IF EXISTS #{athena_table_name};"
    response = athena_query_exection(drop_table_query)
    get_query_info(response.query_execution_id)
  end

  # status: [RUNNING, nil]
  def get_query_info(query_execution_id)
    response = Retriable.retriable(base_interval: 3, tries: 10) do
      query_response = client.get_query_execution(query_execution_id: query_execution_id)
      if ['QUEUED', 'RUNNING'].include?(query_response.query_execution.status.state)
        raise "Query is still running"
      end
      query_response
    end

    [response.query_execution.status.state, response.query_execution.status.state_change_reason]
  end

  # 查询的时候，返回 query_id，然后根据query_id 查询状态，如果是 running，那么就循环下去，否则的话，就返回状态
  def athena_query_exection(query_sql)
    client.start_query_execution(
        query_string: query_sql,
        query_execution_context: {
          database: athena_database_name
        },
        result_configuration: {
          output_location: query_location_path
        }
      )
  end

  def s3_bucket_name
    'fm-lb-access-log'
  end

  def athena_table_name
    "worker_uber_eats_lb_logs"
  end

  def athena_database_name
    'default'
  end

  def query_athena_sql
    <<~SQL
      SELECT
        DISTINCT url_extract_parameter(request_url,'FEEDMOB_GAID') as gaid
      FROM #{athena_table_name}
      WHERE url_extract_parameter(request_url,'ACTION') IN ('P', 'R')
      AND url_extract_parameter(request_url,'BUNDLE_ID') = '#{app_id}'
    SQL
  end

  def query_location_path
    "s3://aws-athena-query-results-246648750489-us-east-1/Unsaved/#{format_date}"
  end

  def create_table_query
    end_date_format = date.to_date.end_of_day.to_i
    <<~SQL
      CREATE EXTERNAL TABLE IF NOT EXISTS #{athena_table_name} (
        `type` string COMMENT '',
        `timestamp` string COMMENT '',
        `elb_name` string COMMENT '',
        `request_ip` string COMMENT '',
        `request_port` int COMMENT '',
        `backend_ip` string COMMENT '',
        `backend_port` int COMMENT '',
        `request_processing_time` double COMMENT '',
        `backend_processing_time` double COMMENT '',
        `client_response_time` double COMMENT '',
        `elb_response_code` string COMMENT '',
        `backend_response_code` string COMMENT '',
        `received_bytes` bigint COMMENT '',
        `sent_bytes` bigint COMMENT '',
        `request_verb` string COMMENT '',
        `request_url` string COMMENT '',
        `protocol` string COMMENT '',
        `user_agent` string COMMENT '')
      ROW FORMAT SERDE
        'org.apache.hadoop.hive.serde2.RegexSerDe'
      WITH SERDEPROPERTIES (
        'input.regex'='^([^ ]*) ([^ ]*) ([^ ]*) ([^ ]*):([0-9]*) ([^ ]*)[:-]([0-9]*) ([-.0-9]*) ([-.0-9]*) ([-.0-9]*) (|[-0-9]*) (-|[-0-9]*) ([-0-9]*) ([-0-9]*) \\\"([^ ]*) ([^ ]*) (- |[^ ]*)\\\" (\"[^\"]*\") .*$'
      )
      STORED AS INPUTFORMAT
        'org.apache.hadoop.mapred.TextInputFormat'
      OUTPUTFORMAT
        'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
      LOCATION
        "s3://#{s3_bucket_name}/uber-eats/AWSLogs/246648750489/elasticloadbalancing/us-east-1/#{format_date}"
      TBLPROPERTIES ('transient_lastDdlTime'="#{end_date_format}")
    SQL
  end
end
