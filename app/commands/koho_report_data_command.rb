# frezen_string_literal: true

class KohoReportDataCommand
  prepend SimpleCommand
  include ActionView::Helpers::NumberHelper

  HEADERS = [
    'Date',
    'CAMPAIGN on AF',
    'First_funded - AF',
    'First_funded - client report',
    'delta %',
    'SPEND Gross - client report $50',
    'Spend gross - AF number',
    'True Gross, Client Payout (80% of AF)',
    'Net spend - AF/vendor dash',
    'Margin (*true gross client payout 80%)',
    'Profit/Loss (client report)',
    'Margin (client report)',
    'MONTH',
  ].freeze

  attr_reader :date

  def initialize(date)
    @date = date.to_date
  end

  def call
    report_datas = []
    campaigns = get_all_campaigns
    purchase_count_lookup = get_purchase_count
    net_spend_lookup = get_net_spends
    campaigns.each do |campaign_name|
      purchase_count = purchase_count_lookup[campaign_name].to_i
      net_spend = net_spend_lookup[campaign_name]

      report_datas << HEADERS.zip([
        date.strftime("%-m/%-d/%y"),
        campaign_name,
        purchase_count,
        "",
        "=(C{{ROW}}-D{{ROW}})/C{{ROW}}",
        "=D{{ROW}}*50",
        "=C{{ROW}}*50",
        "=G{{ROW}}*80%",
        net_spend.to_f,
        "=(H{{ROW}}-I{{ROW}})/H{{ROW}}",
        "=F{{ROW}}-I{{ROW}}",
        "=(F{{ROW}}-I{{ROW}})/F{{ROW}}",
        date.strftime("%B")
      ]).to_h
    end

    report_datas
  end

  private

  def get_all_campaigns
    sql = <<-SQL
      SELECT DISTINCT v.vendor_name, c.os
      FROM click_urls cu 
      LEFT JOIN vendors v ON v.id = cu.vendor_id
      LEFT JOIN campaigns c ON c.id = cu.campaign_id 
      WHERE c.client_id = 184 
        AND cu.status IN (4, 99, 100) 
        AND v.for_test = false
    SQL
    ClickUrl.connection.query(sql).map{|r| "FeedMob_KOHO_#{r[1]}_#{r[0]}"}
  end

  def get_purchase_count
    sql = <<-SQL
      SELECT ar.vendor_name, c.os, sum(ar.purchase_count) AS purchase_count
      FROM agency_records ar
      LEFT JOIN core_main.campaigns c ON c.id = ar.campaign_id 
      WHERE ar.calculate_date = '#{date}'
        AND c.client_id = #{Client::KOHO_ID}
      GROUP BY 1,2
      ORDER BY 1,2
    SQL
    AgencyRecord.connection.query(sql).map{|r| ["FeedMob_KOHO_#{r[1]}_#{r[0]}", r[-1]]}.to_h
  end

  def get_net_spends
    sql = <<-SQL
      SELECT v.vendor_name, c.os, sum(ns.net_spend_cents/100.0) AS net
      FROM net_spends ns 
      LEFT JOIN vendors v ON v.id = ns.vendor_id
      LEFT JOIN campaigns c ON ns.campaign_id = c.id
      WHERE c.client_id = #{Client::KOHO_ID}
        AND ns.spend_date = '#{date}'
      GROUP BY 1,2
      ORDER BY 1,2
    SQL
    pg_res = NetSpend.connection.query(sql).map{|r| ["FeedMob_KOHO_#{r[1]}_#{r[0]}", r[-1]]}.to_h

    sql = <<-SQL
      SELECT vsr.vendor_name,
             (
                CASE
                WHEN vsr.campaign_name ilike '%iOS%' THEN 'iOS'
                WHEN vsr.campaign_name ilike '%Android%' THEN 'Android'
                END
              ) AS os,
             sum(vsr.net_spend_cents/100.0) AS net
      FROM v4_stat_records vsr 
      LEFT JOIN click_url_infos info ON vsr.click_url_id = info.click_url_id AND vsr.report_date = info.event_date
      WHERE info.direct_spend_input = false
        AND vsr.report_date = '#{date}'
        AND vsr.status = 'normal'
        AND vsr.net_spend_cents > 0
        AND info.client_id = #{Client::KOHO_ID}
      GROUP BY 1,2
      ORDER BY 1,2
    SQL
    redshift_res = V4StatRecordRedshift.connection.query(sql).map{|r| ["FeedMob_KOHO_#{r[1]}_#{r[0]}", r[-1]]}.to_h
    pg_res.merge(redshift_res)
  end
end
