# frozen_string_literal: true

class ProcessHistoricalClickUrlCommand
  prepend SimpleCommand

  attr_reader :user, :options

  def initialize(email, options={})
    @user    = User.find_by(email: email)
    @options = options
  end

  def call
    raise 'user not exist' unless user.present?

    requests = HistoricalClickUrlChangeRequest.where(status: :pending)
    requests = requests.where(click_url_id: options[:click_url_id]) if options[:click_url_id].present?
    requests = requests.limit(options[:limit]) if options[:limit].present?

    dates = requests.distinct.order(:date).pluck(:date)
    client_ids = requests.map{|request| request.click_url.client_id}.uniq
    RefreshStatRecordsSpendJob.perform_later(dates[0].to_s, dates[-1].to_s, client_ids: client_ids)

    requests.each{ |request| update_stat_record(request) }
  end

  def update_stat_record(request)
    date = request.date.to_s
    click_url = request.click_url
    net_cpi = click_url.net_cpi_by_date(date)
    margin = click_url.margin_by_date(date)
    gross_cpi = click_url.gross_cpi_by_date(date)

    request.update(status: :finished, memo: {
      user_id: user.id,
      net_cpi: net_cpi,
      margin: margin,
      gross_cpi: gross_cpi,
      message: 'stat records refreshed'
    })
    puts "id: #{request.id}, click url: #{request.click_url_id}, finish"
  end
end
