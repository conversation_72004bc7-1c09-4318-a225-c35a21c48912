# frezen_string_literal: true

class ZipRecruiterMarginDailyReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = if Rails.env.production?
                     '1_glSPLccAZQ2iNJCF0AOde41k8aDNThul9CQ7gaaGac'
                   else
                     '1CPWaOM41FxFcgRCiIpr32VShOjHKu3kcxR7Ln3KTq7M'
                   end

  SHEET_NAME_ROW_DATA = 'Margin Tracker'

  attr_reader :start_date, :end_date, :feedmob_service

  def initialize(start_date:, end_date:)
    @start_date = start_date.to_date
    @end_date = end_date.to_date
    @feedmob_service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    datas = fetch_datas
    datas.each do |data|
      search_hash = {0 => data[:date], 1 => data.campaign_name, 2 => data.vendor_name}
      @feedmob_service.update_or_append_row2(SHEET_NAME_ROW_DATA, search_hash, data.values, exclude_columns: [])
    end
  end

  def fetch_datas
    datas = ZipRecruiterReportDataCommand.new(start_date, end_date).call.result

    select_uber_datas = datas
  end

end
