# frezen_string_literal: true

class CheckZipRecruiterMarginDailyReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1_glSPLccAZQ2iNJCF0AOde41k8aDNThul9CQ7gaaGac'

  SHEET_NAME_ROW_DATA = 'Margin Tracker'

  attr_reader :start_date, :end_date, :feedmob_service

  def initialize(start_date:, end_date:)
    @start_date = start_date.to_date
    @end_date = end_date.to_date
    @feedmob_service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    values = @feedmob_service.read_values(SHEET_NAME_ROW_DATA)
    google_datas = process_spreadsheet_values(values)

    diff_datas = []
    (start_date.to_date..end_date.to_date).each do |date|
      db_datas = ZipRecruiterReportDataCommand.new(date, date).fetch_spends(date)
      db_datas.each do |data|
        click_url = ClickUrl.find(data['click_url_id'])
        google_data = google_datas.find {|c|
          google_date = Date.strptime(c['Date'], '%m/%d/%Y').to_s rescue nil
          google_date == data['date'] && click_url.campaign.name == c['CAMPAIGN'] && click_url.vendor.vendor_name == c['Partner']
        }.to_h

        google_net = google_data['Current Net spend'].to_s.gsub(/[$,]/, '').to_f.round(2)
        google_gross = google_data['SPEND Gross'].to_s.gsub(/[$,]/, '').to_f.round(2)
        diff_net = google_net - data['net_spend'].to_f
        diff_gross = google_gross - data['gross_spend'].to_f

        if diff_net.abs >= 1 || diff_gross.abs >= 1
          diff_datas << data.merge({
            'campaign_name' => click_url.campaign.name,
            'vendor_name' => click_url.vendor.vendor_name,
            'Current Net spend(google)' => google_net,
            'SPEND Gross(google)' => google_gross,
            'Diff Net' => diff_net.round(2),
            'Diff Gross' => diff_gross.round(2)
          })
        end
      end
    end

    diff_datas
  end

  def process_spreadsheet_values(values)
    if values.empty?
      return []
    end

    headers = values.shift # 获取表头行
    result = []
    values.each do |row|
      hash = {}
      headers.each_with_index do |header, index|
        hash[header] = row[index] unless row[index].nil? # 处理空单元格
      end
      result << hash
    end
    result
  end

end
