# frozen_string_literal: true

class UberSpendReportCommand < ReportBaseCommand
  prepend SimpleCommand
  include ErbBinding

  EXCLUDE_CLICK_URL_IDS = [17975].freeze

  MAPPING = {
    169 => {
      campaign_id: 500,
      campaign_name: 'CM2337155-affiliates-feedmob_1_-99_US-National_e_all_acq_cpa_en_Null',
      product_name: 'eaters',
      objective_name: 'ACQ',
      billing_type: 'cpa',
      os_targeted: 'Android',
      country_id: 1,
      city_id: -99
    }
  }

  attr_reader :start_date, :end_date, :client_id, :client, :filename, :opts
  attr_accessor :mismatch_mapping

  def initialize(end_date:, opts: {}, **_)
    @start_date = end_date
    @end_date   = end_date
    @filename   = "feedmob_#{end_date.strftime('%Y%m%d')}.txt"
    @client_id  = Client.where(name: 'Uber Technologies').pluck(:id)
    @client     = Client.find_by(name: 'Uber Technologies')
    @opts       = opts
    @mismatch_mapping = []
  end

  def has_data?
    data.present?
  end

  def report_csv_stream
    generate_csv_stream(data).strip
  end

  def conditions
    @conditions ||= begin
      sql_condition = []

      sql_condition << "report_date >= '#{start_date}' AND report_date <= '#{end_date}'"
      sql_condition << "campaign_id IN (#{campaign_ids.join(',')})"    if campaign_ids.present?
      sql_condition << "vendor_id IN (#{vendor_ids.join(',')})"     if vendor_ids.present?
      sql_condition << "campaign_id NOT IN (#{exclude_campaign_ids.join(',')})"  if exclude_campaign_ids.present?
      sql_condition << "status in ('#{v2_statuses.join("','")}')" if v2_statuses.present?

      return if sql_condition.blank?

      "WHERE #{sql_condition.join(' AND ')}"
    end
  end

  def agency_conditions
    @agency_conditions ||= begin
      sql_condition = []

      sql_condition << "calculate_date >= '#{start_date}' AND calculate_date <= '#{end_date}'"
      sql_condition << "campaign_id IN (#{campaign_ids.join(',')})"    if campaign_ids.present?
      sql_condition << "vendor_id IN (#{vendor_ids.join(',')})"     if vendor_ids.present?
      sql_condition << "campaign_id NOT IN (#{exclude_campaign_ids.join(',')})"  if exclude_campaign_ids.present?
      sql_condition << "ar.status in ('#{statuses.join("','")}')" if statuses.present?

      return if sql_condition.blank?

      "WHERE #{sql_condition.join(' AND ')}"
    end
  end

  def net_spend_conditions
    @net_spend_conditions ||= begin
      sql_condition = []

      sql_condition << "spend_date >= '#{start_date}' AND spend_date <= '#{end_date}'"
      sql_condition << "campaign_id::INTEGER IN (#{campaign_ids.join(',')})"    if campaign_ids.present?
      sql_condition << "vendor_id::INTEGER IN (#{vendor_ids.join(',')})"        if vendor_ids.present?
      sql_condition << "campaign_id::INTEGER NOT IN (#{exclude_campaign_ids.join(',')})"  if exclude_campaign_ids.present?

      return if sql_condition.blank?
      "WHERE #{sql_condition.join(' AND ')}"
    end
  end

  def uber_eats_mappings
    @uber_eats_mappings ||= UberEatsMapping.without_deleted.each_with_object({}) do |r, hash|
      hash[[r.internal_campaign_id, r.vendor_id]] = {
        campaign_id: r.report_campaign_id,
        campaign_name: r.report_campaign_name,
        product_name: r.product_name,
        objective_name: r.objective_name,
        billing_type: r.billing_type,
        os_targeted: r.os_targeted,
        country_id: r.country_id,
        city_id: r.city_id,
      }
    end
  end

  def exclude_campaign_ids
    ClickUrl.where(id: EXCLUDE_CLICK_URL_IDS).pluck(:campaign_id).uniq
  end

end
