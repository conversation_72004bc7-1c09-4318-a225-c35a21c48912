# frezen_string_literal: true
class FetchScopelyAdjustReportCommand < FetchScopelyAdjustReportBaseCommand
  prepend SimpleCommand
  include AdjustApiConcern
  include MmpAggregateDataConcern

  attr_reader :start_date, :end_date, :report_not_mappings
  SYSTEM_EMAIL = '<EMAIL>'

  def initialize(start_date: Date.yesterday.to_s, end_date: Date.yesterday.to_s)
    @start_date = start_date
    @end_date = end_date
    @report_not_mappings = []
    @mmp_aggrate_datas = []
    @update_paid_actions = [:impression, :click, :install]
  end

  def call
    scopely_datas = fetch_scopely_adjust_data(start_date, end_date)

    if scopely_datas.nil?
      SlackService.send_notification_to_star("#{self.class.name}: start_date #{start_date} end_date #{end_date} faild to create report, please check!")
      return
    end
    (start_date.to_date..end_date.to_date).each do |date|
      save_report(date, scopely_datas)
    end

    save_mmp_aggregate_datas

    return if report_not_mappings.empty?

    SlackService.send_notification_to_star("#{self.class.name}: 在 #{start_date} ~ #{end_date}, adjust api 存在数据，但没有对应的 adjust campaign mapping，请确认以下列表是否需要添加 mapping。\n#{report_not_mappings.uniq.map { |r| r.join(', ') }.join("\n")}")
  end

  def save_report(date, campaign_datas)
    date_campaign_datas = campaign_datas.select { |c| c['day'].to_date.to_s == date.to_s }
    group_campaign_datas = date_campaign_datas.group_by {|c| [c['store_id'], c['campaign'], c['os_name'], c['partner_name'].to_s.downcase] }
    group_campaign_datas.each do |key, items|
      app_id, adjust_campaign_name, adjust_os_name, adjust_channel = key

      impressions = items.sum{|c|c['impressions'].to_i}
      clicks = items.sum{|c|c['clicks'].to_i}
      installs = items.sum{|c|c['installs'].to_i}

      send_mapping_notification = AdjustCampaignMapping.send_adjust_mapping_notification(impressions, clicks, installs)

      if adjust_campaign_name == 'unknown'
        report_not_mappings << key if send_mapping_notification
        next
      end

      click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel, Client::SCOPELY_ID, false, send_mapping_notification)

      if click_url.blank?
        report_not_mappings << key if !adjust_campaign_name.in?(exclude_data[app_id].to_a) && send_mapping_notification
        next
      end

      model = {
        impression: impressions.to_i,
        click: clicks.to_i,
        install: installs.to_i
      }

      mmp_aggrate_data = model.dup
      @mmp_aggrate_datas << mmp_aggrate_data.merge({source_report_type: table_by(click_url).to_s, click_url_id: click_url.id, date: date})

      report = table_by(click_url).find_by(date: date, adjust_campaign_name: adjust_campaign_name, adjust_channel: adjust_channel, adjust_os_name: adjust_os_name, skan: false)
      if report.blank?
        report = table_by(click_url).new(click_url_id: click_url.id, campaign_id: click_url.campaign_id, vendor_id: click_url.vendor_id, date: date, adjust_campaign_name: adjust_campaign_name, adjust_channel: adjust_channel, adjust_os_name: adjust_os_name, skan: false)
      end

      Audited.audit_class.as_user(User.find_by(email: SYSTEM_EMAIL)) do
        report.update model
      end
    end
  end

  def exclude_data
    @exclude_data ||= TrackingConfig.find_by(config_type: "system", name: "EXCLUDE_SCOPELY_ADJUST_REPORT_DATA")&.value&.with_indifferent_access
  end
end
