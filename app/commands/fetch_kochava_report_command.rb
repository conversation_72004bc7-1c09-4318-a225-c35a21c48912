# frezen_string_literal: true
class FetchKochavaReportCommand < FetchKochavaReportBaseCommand
  prepend SimpleCommand
  attr_reader :time_start, :time_end, :date, :summary_token, :event_token, :mapping, :network

  def initialize(date: Date.yesterday.to_s, summary_token: nil, event_token: nil, mapping: nil)
    @date = date
    @summary_token = summary_token
    @event_token = event_token
    @time_start = date.to_date.beginning_of_day.to_i
    @time_end = date.to_date.end_of_day.to_i
    @mapping = mapping
    @network = mapping.net_work
  end

  def api_data(send_slack: true)
    if (summary_token.blank? || event_token.blank?) && send_slack
      send_star_slack("#{self.class}: date #{date} faild to create report, token is nil, please check!")
      return
    end

    click_urls = ClickUrl.where(campaign_id: mapping.campaign_id, vendor_id: mapping.vendor_id, status: ['cu_normal', 'active'])
    if click_urls.length == 0
      click_url = ClickUrl.where(campaign_id: mapping.campaign_id, vendor_id: mapping.vendor_id, status: 'paused').order(:id).first
      if click_url.blank?
        click_url = ClickUrl.where(campaign_id: mapping.campaign_id, vendor_id: mapping.vendor_id, status: 'archived').order(:id).first
      end
    elsif click_urls.length == 1
      click_url = click_urls.first
    else
      click_url = click_urls.order(:id).first
    end

    if click_url.blank? && send_slack
      send_star_slack("#{self.class}: found #{click_urls.length} click_urls, unable to create report")
      return
    end

    summary_s3_url = wait_job_finished(summary_token)
    summary_records = download_csv(summary_s3_url)

    event_s3_url = wait_job_finished(event_token)
    events_records = download_csv(event_s3_url)

    if (summary_records.nil? || events_records.nil?) && send_slack
      send_star_slack("#{self.class}: date #{date} faild to create report, please check!")
      return
    end

    return if summary_records.blank? && events_records.blank? && send_slack

    events_records = events_records.blank? ? [] : events_records
    summary_records = summary_records.blank? ? {} : summary_records
    events = {
      impression: summary_records.sum{|c|c['impression_count'].to_i},
      click: summary_records.sum{|c|c['click_count'].to_i},
      install: summary_records.sum{|c|c['install_count'].to_i}
    }
    mapping.events_in_json.each do |feedmob_event, kochava_events|
      events[feedmob_event.to_sym] = events_records.select { |c| kochava_events.include?(c['event_name']) }.size
    end

    [click_url, events]
  end

  def call
    click_url, events = api_data
    return if click_url.blank? || events.nil?

    report = KochavaAgencyReport.find_or_create_by(date: date, click_url_id: click_url.id, app_guid: mapping.app_guid, network: mapping.net_work)
    model = {
      campaign_id: click_url.campaign_id,
      vendor_id: click_url.vendor_id
    }.merge(events)
    report.update(model)
  end

  def send_star_slack(text)
    SlackService.send_notification_to_star text
  end
end
