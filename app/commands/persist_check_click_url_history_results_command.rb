# frozen_string_literal: true

class PersistCheckClickUrlHistoryResultsCommand
  def initialize(date)
    @date = date
  end

  def save_results(results)
    redis_client.rpush(redis_key, results.to_json)
    redis_client.expire(redis_key, 1.day.to_i)
  end

  def results
    redis_client.lrange(redis_key, 0, -1).map { |result| JSON.parse(result) }
  end

  def clear_results
    redis_client.del(redis_key)
  end

  private

    attr_reader :date

    def redis_client
      @redis_client ||= RedisClient.main
    end

    def redis_key
      @redis_key ||= "CheckClickUrlHistoryDataJob:#{date}:Results"
    end
end
