module ArbitrageLevel2SwitchConcern
  extend ActiveSupport::Concern

  def setting_arbitrage_level_config(click_url, user)
    arbitrage_click_url_ids = ClickUrl.arbitrage_click_url_ids.to_a
    if click_url.is_arbitrage_level_switch? && arbitrage_click_url_ids.exclude?(click_url.id)
      arbitrage_click_url_ids.push(click_url.id)
      notify_create_arbitrage_level_2_click_url(click_url, user)
    end

    if click_url.exclude_arbitrage_level_switch? && arbitrage_click_url_ids.include?(click_url.id)
      arbitrage_click_url_ids.delete(click_url.id)
    end

    if arbitrage_click_url_ids.size != ClickUrl.arbitrage_click_url_ids.to_a.size
      arbitrage_config = TrackingConfig.find_by(config_type: 'system', name: 'ARBITRAGE_CLICK_URLS')
      arbitrage_config.update(value: {click_url_ids: arbitrage_click_url_ids})
    end
  end

  def notify_create_arbitrage_level_2_click_url(click_url, user)
    return if click_url.vendor.for_test || click_url.client.for_test

    if user.is_arbitrage_level_2_beta?
      subject = "Arbitrage level 2 Click Url #{click_url.id} is created"
      content = <<-EOQ
        Arbitrage level 2 Click Url #{click_url.id} is created at #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}
      EOQ
      UserMailer.notify_arbitrage_level_2(subject, content).deliver_now
    end
  end

end
