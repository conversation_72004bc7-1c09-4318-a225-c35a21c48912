
# frezen_string_literal: true
class FetchScopelyAdjustEventsCommand
  prepend SimpleCommand
  include AdjustApiConcern

  API_HOST = 'https://automate.adjust.com'.freeze
  ACCESS_TOKEN = ENV['SCOPELY_ADJUST_API_ACCESS_TOKEN'].freeze
  SCOPELY_ADJUST_ACCOUNT_ID = '1091'.freeze

  SCOPELY_APP_TOKENS = {
    "**********" => "lga0lass93i8",
    "com.pieyel.scrabble" => "s6cawo3evq4g",
    "com.scopely.monopolygo" => "8uu91lb869z4",
    "**********" => "fb98bvo06ku8",
    "*********" => "s6cawo3evq4g",
    "com.scopely.yux" => "atg5domnfu9s",
    "com.scopely.gardenjoy" => "mqtek844d2ww",
    "**********" => "zfb89p6vqww0",
    "**********" => "mte1elttm9ds"
  }

  attr_reader :start_date, :end_date, :app_id

  def initialize(start_date: Date.yesterday.to_s, end_date: Date.yesterday.to_s, app_id: nil)
    @start_date = start_date
    @end_date = end_date
    @app_id = app_id
  end

  def call
    fetch_scopely_adjust_data(start_date, end_date, app_id)
  end

  private

  def fetch_scopely_adjust_data(start_date, end_date, app_id)
    base_metrics = %w[impressions clicks installs]
    event_metrics = fetch_event_slugs
    metrics = base_metrics + event_metrics
    app_token = SCOPELY_APP_TOKENS[app_id]

    params = {
      date_period: "#{start_date}:#{end_date}",
      app_token__in: app_token,
      dimensions: 'day,store_id,app,os_name,partner_name,campaign,campaign_id_network,campaign_network',
      metrics: metrics.join(','),
      attribution_type: 'all',
      attribution_source: 'first',
      utc_offset: '+00:00',
      os_names: 'ios,android',
      channel__in: channels.join(",")
    }

    res = Retriable.retriable(base_interval: 3) do
      HTTParty.get(
        "#{API_HOST}/control-center/reports-service/csv_report?#{params.to_query}",
        headers: { 'Authorization'=> "Bearer #{ACCESS_TOKEN}", 'x-account-id' => SCOPELY_ADJUST_ACCOUNT_ID })
    end

    if res.body.nil?
      SlackService.send_notification_to_star("#{self.class.name}: response_code: #{res.code}, return empty data for app_id##{app_id} from #{start_date} to #{end_date}, please verify!")
      return []
    else
      csv_data = res.body.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding("UTF-8"), '')
      CSV.parse(csv_data, headers: true)
    end
  end

  def fetch_event_slugs
    AdjustEventMapping.find_by(client_id: Client::SCOPELY_ID, app_id: app_id)&.events_in_json.to_h.values.flatten.uniq
  end

  def channels
    @channels ||= AdjustCampaignMapping.includes(:vendor).where(client_id: Client::SCOPELY_ID).where(vendors: {for_test: false}).pluck(:channel).uniq
  end
end
