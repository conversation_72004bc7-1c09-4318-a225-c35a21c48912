# frezen_string_literal: true

class ZipRecruiterRtbSpendReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = if Rails.env.production?
                     '1A0NUqo_Z_SNKDqJ37m_oVooOtq8cHTlNVLz2-oMY-Zk'
                   else
                     '1hdeY4MW0bSNY0V5nq9TMWkLI_hRbsYMIFGzd1Zhq_Vs'
                   end

  attr_reader :start_date, :end_date, :feedmob_service

  def initialize(start_date:, end_date:)
    @start_date = start_date.to_date
    @end_date = end_date.to_date
    @feedmob_service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    datas = fetch_datas
    datas.each do |data|
      search_hash = {0 => data['Date'], 1 => data['click url']}
      @service.update_or_append_row2('Sheet1', search_hash, data.values, exclude_columns: [])
    end
  end

  private

  def fetch_datas
    NetSpend.where(spend_date: start_date..end_date, campaign_id: campaign_ids, vendor_id: Vendor::RTB_HOUSE_ID).order(spend_date).map do |direct_spend|
      {
        'Date' => direct_spend.spend_date,
        'click url' => direct_spend.click_url_id,
        'Net Spend' => number_to_currency(direct_spend.net_spend.to_f),
        'Gross Spend' => number_to_currency(direct_spend.gross_spend.to_f)
      }
    end
  end

  def campaign_ids
    @campaign_ids ||= Campaign.where(client_id: Client::ZIP_RECRUITER).pluck(:id)
  end
end
