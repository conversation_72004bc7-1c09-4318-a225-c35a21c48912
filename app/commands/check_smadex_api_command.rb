# frezen_string_literal: true
class CheckSmadexApiCommand
  prepend SimpleCommand

  attr_reader :smadex_api_data, :db_spends, :report_id, :start_date, :end_date

  def initialize(report_id:, start_date: Date.yesterday, end_date: Date.yesterday)
    @end_date = end_date.to_date
    @start_date = start_date.to_date
    @report_id = report_id
    @smadex_api_data = get_api_data
    @db_spends = get_spends_from_db
  end

  def get_new_campaigns
    campaign_api_data = smadex_api_data.map { |multi_campaign_id, campaign_name, _, spend| [multi_campaign_id, campaign_name, spend] }
    campaign_api_data.group_by { |multi_campaign_id, _, _| multi_campaign_id }.map do |multi_campaign_id, rows|
      spend = rows.sum { |_, _, spend| spend }
      campaign_name = rows.first[1]  
      next if spend.zero? || campaign_mapping_multi_campaign_ids.include?(multi_campaign_id)

      [multi_campaign_id, campaign_name, spend]
    end.compact
  end

  def get_difference
    differences = []
    ClickUrl.includes(:campaign, :vendor).where(id: click_url_ids).each do |click_url|
      (start_date..end_date).to_a.reverse.each do |date|
        api_spends = get_daily_api_data(date).to_h || {}
        api_net_spend = api_spends[campaign_mappings[click_url.id]].to_f
        direct_spend = db_spends[[date, click_url.id]]
        audits = Audit.where(auditable_type: 'NetSpend', auditable_id: direct_spend.id) if direct_spend
        db_net_spend = direct_spend&.net_spend.to_f
        diff = db_net_spend - api_net_spend

        red_mark = diff.abs > 1 && (direct_spend.nil? ? 0 : audits.where.not(user_id: User::SYSTEM_USER_ID).where("audits.audited_changes->'net_spend_cents' IS NOT NULL").count).zero?

        difference = {
          click_url_id: click_url.id,
          date: date.to_s,
          campaign: click_url.campaign.name,
          api_net_spend: api_net_spend.round(2),
          diff: diff.round(2),
          red_mark: red_mark
        }

        if direct_spend
          difference[:db_last_update_user] = audits&.last&.user_name
          difference[:db_net_spend] = db_net_spend.round(2)
        end

        differences << difference
      end
    end
    differences
  end

  def get_spends_from_db
    NetSpend.where(spend_date: start_date..end_date, click_url_id: click_url_ids).to_a.index_by { |spend| [spend.spend_date.to_date, spend.click_url_id] }
  end

  def get_daily_api_data(date)
    smadex_api_data.select { |_, _, d, _| d == "#{date}-00-00" }.group_by { |multi_campaign_id, _, _, _| multi_campaign_id }.map { |multi_campaign_id, rows| [multi_campaign_id, rows.sum { |_, _, _, spend| spend }] }
  end

  def get_api_data
    smadex_report_url = SmadexApiService.new.download_url(report_id)
    data = SmadexApiService.new.smadex_report_csv(smadex_report_url) || []
    data.map { |c| [c['multi_campaign_id'].to_s, c['campaign_name'].to_s, c['date_time'], c['media_spend'].to_f] }
  end

  def click_url_ids
    @click_url_ids ||= DirectSpendJobStat.where(status: 'live', net_spend_source: 'Partner dashboard').where("? = ANY(vendor_ids)", Vendor::SMADEX_ID).pluck(:click_url_ids).flatten.uniq
  end

  def campaign_mapping_ids
    @campaign_mapping_ids ||= campaign_mappings.values
  end

  def campaign_mappings
    campaign_mappings = SmadexCampaignMapping.pluck(:click_url_id, :smadex_multi_campaign_id)
    @campaign_mappings ||= campaign_mappings.to_h
  end

  def campaign_mapping_multi_campaign_ids
    @campaign_mapping_multi_campaign_ids ||= SmadexCampaignMapping.pluck(:smadex_multi_campaign_id).uniq
  end
end
