module Comparisons::MonthlyPacing
  class ComparerResult
    attr_reader :fe, :portal, :factor
    attr_reader :fe_portal

    def initialize(fe, portal, factor)
      @factor = factor
      @fe = fe.dig(factor).to_f.round(2)
      @portal = portal.dig(factor).to_f.round(2)

      @fe_portal = difference_between(@fe, @portal).round(2)
    end

    def consistency?
      fe_portal < 0.1
    end

    private

    def difference_between(platform_a, platform_b)
      (platform_a - platform_b).abs
    end
  end
end