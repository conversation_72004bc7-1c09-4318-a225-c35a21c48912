module Comparisons::MonthlyPacing
  class MonthlyPacingCommand < ::Comparisons::ComparisonBase
    prepend SimpleCommand

    def call
      original_data = {}

      monthly_pacing_service = ::Comparisons::MonthlyPacing::Platform.new(:monthly_pacing, options)
      original_data[:monthly_pacing] = Array(monthly_pacing_service.perform)

      fe_service = ::Comparisons::MonthlyPacing::Platform.new(:fe, options)
      original_data[:fe] = fe_service.perform

      command = ::Comparisons::MonthlyPacing::ComparerCommand.call(original_data)
      if command.success?
        command.result
      else
        errors[:difference] = command.errors[:difference]
      end
    end
  end
end