module Comparisons::MonthlyPacing
  class ComparerCommand
    prepend SimpleCommand

    attr_reader :fe, :monthly_pacing

    def initialize(original_data)
      fe_spend = Hash(format_data(original_data[:fe])).dig('spend').to_f
      @fe = {spends: fe_spend }
      @monthly_pacing =
        format_data original_data[:monthly_pacing] do |data|
          data.dig('gross_spend_s')
        end
    end

    def call
      return nil  if no_data?

      compare(fe, monthly_pacing)
    end

    private

    COMPARE_FACTOR = %i[
      spends
    ].freeze

    def no_data?
      fe.blank? && monthly_pacing.blank?
    end

    def compare(fe, portal)
      COMPARE_FACTOR.each_with_object([]) do |factor, outcome|
        outcome << ::Comparisons::MonthlyPacing::ComparerResult.new(fe, portal, factor)
      end
    end

    def format_data(data)
      total_data = Array(data).select{ |s| s.dig('title') == 'Total' }.last

      if block_given?
        { spends: yield(Hash(total_data)) }
      else
        total_data
      end
    end
  end
end