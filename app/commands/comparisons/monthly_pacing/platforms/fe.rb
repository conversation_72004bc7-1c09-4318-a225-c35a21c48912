module Comparisons::MonthlyPacing::Platforms
  class Fe < ::Comparisons::ApiBase
    def url
      "#{ENV['FE_V2_ENDPOINT']}/api/v1/campaigns.json"
    end

    def params=(options)
      @params ||= begin
        {
          start_date: options[:date].to_date.beginning_of_month,
          end_date: options[:date],
          campaign_id: options[:campaign_id],
          vendor_id: options[:vendor_id],
          conversion: :purchase,
          spend: :gross,
          status_group:  options[:status_group] || :default,
          cohort: :cohort,
          cost_group: :cpi,
          sort: :campaign_name
        }
      end
    end
  end
end
