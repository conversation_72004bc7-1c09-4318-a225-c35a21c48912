module Comparisons
  class PlatformBase
    attr_reader :platform, :platform_name

    def initialize(platform_name, params)
      @platform_name = platform_name
      @platform = sub_platform.classify.constantize.new
      @platform.params = params
    end

    def perform
      @perform ||= platform.request_data
    end

    protected

    def sub_platform
      raise NotImplementedError
    end
  end
end
