module Comparisons
  class ApiBase
    attr_reader :params

    def url
      raise NotImplementedError
    end

    def params=(value)
      @params = value
    end

    def request_data
      response = HTTP.auth(auth_header).get(url, params: params.as_json)
      JSON.parse(response.to_s).with_indifferent_access[:data]
    rescue => e
      Sentry.capture_exception(e, extra: { url: url, params: params })
      nil
    end

    def auth_header
      "Bearer #{ENV['FE_API_KEY']}"
    end
  end
end
