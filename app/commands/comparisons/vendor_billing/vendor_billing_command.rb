module Comparisons::VendorBilling
  class VendorBillingCommand < ::Comparisons::ComparisonBase
    prepend SimpleCommand

    def call
      original_data = {}

      vendor_billing_service = ::Comparisons::VendorBilling::Platform.new(:vendor_billing, options)
      original_data[:vendor_billing] = vendor_billing_service.perform

      fe_service = ::Comparisons::VendorBilling::Platform.new(:fe, options)
      original_data[:fe] = fe_service.perform


      command = ::Comparisons::VendorBilling::ComparerCommand.call(original_data)
      if command.success?
        command.result
      else
        errors[:difference] = command.errors[:difference]
      end
    end
  end
end