module Comparisons::VendorBilling
  class ComparerCommand
    prepend SimpleCommand

    attr_reader :fe, :vendor_billing

    def initialize(original_data)
      @fe = format_fe_data(original_data[:fe])
      @fe[:spends] = fe[:spends].to_f


      @vendor_billing = Hash(original_data[:vendor_billing])
    end

    def call
      return nil  if no_data?

      compare(fe, vendor_billing)
    end

    private

    COMPARE_FACTOR = %i[
      clicks
      installs
      spends
    ].freeze

    def no_data?
      fe.blank? && vendor_billing.blank?
    end

    def compare(fe, portal)
      COMPARE_FACTOR.each_with_object([]) do |factor, outcome|
        outcome << ::Comparisons::VendorBilling::ComparerResult.new(fe, portal, factor)
      end
    end

    def format_fe_data(data)
      total_data = Array(data).select{ |s| s.dig('title') == 'Total' }.last

      {
        spends: total_data&.dig('spend'),
        installs: total_data&.dig('install_count'),
        clicks: total_data&.dig('click_count')
      }
    end
  end
end