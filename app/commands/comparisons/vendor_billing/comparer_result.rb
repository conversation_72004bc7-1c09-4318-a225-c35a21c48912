module Comparisons::VendorBilling
  class ComparerResult
    attr_reader :fe, :portal, :factor
    attr_reader :fe_portal

    def initialize(fe, portal, factor)
      @factor = factor
      @fe = format_value fe.dig(factor)
      @portal = format_value portal.dig(factor)

      @fe_portal = format_value difference_between(@fe, @portal)
    end

    def consistency?
      fe_portal < 0.1
    end

    private

    def difference_between(platform_a, platform_b)
      (platform_a - platform_b).abs
    end

    def format_value(value)
      if factor == :spends
        value.to_f.round(2)
      else
        value.to_i
      end
    end
  end
end