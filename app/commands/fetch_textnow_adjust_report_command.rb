# frezen_string_literal: true
class FetchTextnowAdjustReportCommand < FetchTextnowAdjustReportBaseCommand
  prepend SimpleCommand
  include AdjustApiConcern
  include MmpAggregateDataConcern

  attr_reader :start_date, :end_date, :report_not_mappings
  SYSTEM_EMAIL = '<EMAIL>'

  def initialize(start_date: Date.yesterday.to_s, end_date: Date.yesterday.to_s)
    @start_date = start_date
    @end_date = end_date
    @report_not_mappings = []
    @report_multiple_mappings = []
    @report_not_click_url = []
    @mmp_aggrate_datas = []
  end

  def call
    campaign_datas = fetch_adjust_data(start_date, end_date)

    if campaign_datas.nil?
      SlackService.send_notification_to_star("#{self.class.name}: start_date #{start_date} end_date #{end_date} faild to create report, please check!")
      return
    end
    (start_date.to_date..end_date.to_date).each do |date|
      save_report(date, campaign_datas)
    end

    save_mmp_aggregate_datas

    send_slack_alert(report_not_mappings)
  end

  def save_report(date, campaign_datas)
    date_campaign_datas = campaign_datas.select { |c| c['day'].to_date.to_s == date.to_s }
    group_campaign_datas = date_campaign_datas.group_by {|c| [c['store_id'], c['campaign'], c['os_name'], c['partner_name'].to_s.downcase] }
    group_campaign_datas.each do |key, items|
      app_id, adjust_campaign_name, adjust_os_name, adjust_channel = key
      next if adjust_campaign_name.to_s.in?(exclude_data[app_id].to_a)

      event_mapping = event_mappings.find{|c| c.app_id == app_id }

      impressions = items.sum{|c|c['impressions'].to_i}
      clicks = items.sum{|c|c['clicks'].to_i}
      installs = items.sum{|c|c['installs'].to_i}

      send_mapping_notification = AdjustCampaignMapping.send_adjust_mapping_notification(impressions, clicks, installs)

      if event_mapping.blank?
        report_not_mappings << key if app_id != 'unknown' && adjust_campaign_name != 'unknown' && send_mapping_notification
        next
      end

      click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel,  Client::TEXTNOW_ID, false, send_mapping_notification)

      next if click_url.blank?

      metrics = %w[impressions clicks installs] + event_mapping.events_in_json.values.flatten.uniq
      raw_log = metrics.each_with_object({}) do |metric, hash|
        hash[metric] = items.sum{|c|c[metric].to_i}
      end
      update_mmp_original_event_report(raw_log, click_url, date, event_mapping)

      model = {
        impression: impressions.to_i,
        click: clicks.to_i,
        install: installs.to_i,
        raw_log: raw_log
      }
      mmp_aggrate_data = model.dup

      event_mapping.events_in_json.each do |feedmob_event, adjust_events|
        mmp_aggrate_data["#{feedmob_event}_original".to_sym] = {}

        event_count = adjust_events.map do |event|
          item_event_count = items.sum{|c|c[event].to_i}
          mmp_aggrate_data["#{feedmob_event}_original".to_sym][event] = item_event_count
          item_event_count
        end.sum
        model[feedmob_event.to_sym] = event_count
        mmp_aggrate_data[feedmob_event.to_sym] = event_count
      end

      @mmp_aggrate_datas << mmp_aggrate_data.merge({source_report_type: table_by(click_url).to_s, click_url_id: click_url.id, date: date})

      report = table_by(click_url).find_by(date: date, adjust_campaign_name: adjust_campaign_name, adjust_channel: adjust_channel, adjust_os_name: adjust_os_name, skan: false)
      if report.blank?
        report = table_by(click_url).new(click_url_id: click_url.id, campaign_id: click_url.campaign_id, vendor_id: click_url.vendor_id, date: date, adjust_campaign_name: adjust_campaign_name, adjust_channel: adjust_channel, adjust_os_name: adjust_os_name, skan: false)
      end

      Audited.audit_class.as_user(User.find_by(email: SYSTEM_EMAIL)) do
        report.update model
      end
    end
  end

  def update_mmp_original_event_report(raw_log, click_url, date, event_mapping)
    return if event_mapping.blank?

    raw_log =  raw_log.select { |event, total| total.to_i > 0 }

    raw_log.each do |adjust_event, total|
      feedmob_event = event_mapping.get_feedmob_event(adjust_event)

      next if feedmob_event.blank?

      report = MmpOriginalEventReport.find_or_create_by(
        click_url_id: click_url.id,
        date: date,
        mmp_original_event_name: adjust_event,
        track_type: feedmob_event
      )

      report.update(
        total: total,
        click_url_id: click_url.id,
        campaign_id: click_url.campaign_id,
        vendor_id: click_url.vendor_id,
        vendor: click_url.vendor&.vendor_name,
        campaign: click_url.campaign&.name,
        mmp: 'adjust'
      )
    end
  end

  def send_slack_alert(report_not_mappings)
    if report_not_mappings.present?
      SlackService.send_notification_to_star("#{self.class.name}: 在 #{start_date} ~ #{end_date}, 以下 adjust api 数据没有对应的 mapping，需要补充mapping 关系。\n#{report_no_mappings.uniq.map { |r| r.join(', ') }.join("\n")}")
    end
  end

  def exclude_data
    @exclude_data ||= TrackingConfig.find_by(config_type: "system", name: "EXCLUDE_TEXTNOW_ADJUST_REPORT_DATA")&.value&.with_indifferent_access
  end
end
