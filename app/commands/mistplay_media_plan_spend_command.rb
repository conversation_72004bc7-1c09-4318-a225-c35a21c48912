class MistplayMediaPlanSpendCommand
  prepend SimpleCommand

  attr_reader :start_date, :end_date


  def initialize(start_date: , end_date: )
    @start_date = start_date
    @end_date = end_date
  end

  def call
    api_url = "https://docs.google.com/spreadsheets/d/1qVyhBrj-q2QCgzvACMZNLRl1VDtGaZTUkitQSHox9AU/export?gid=361337613&exportFormat=csv"
    datas = HTTParty.get(api_url)
    keys = datas[8]
    values = datas[9..-1]
    hash_datas = values.map{|row| keys.zip(row).to_h }

    media_plan_spends = []
    hash_datas.each do |media_plan_spend|
      _hash = {}
      _hash['campaign_name'] = media_plan_spend['FE campaign name'].to_s.strip
      _hash['vendor_name'] = media_plan_spend['Partner'].to_s.strip
      next if _hash['campaign_name'].blank?
      _hash['launch_date'] = media_plan_spend['Launch Date']
      (start_date.to_date..end_date.to_date).each do |date|
        _hash['date'] = date.to_s
        _hash['net_spend'] = media_plan_spend["Actual Net spend #{date.strftime('%m/%d')}"].to_s.gsub(/[$,]/, '').to_f
        next if _hash['net_spend'] == 0
        media_plan_spends << _hash
      end
    end

    media_plan_spends
  end

end