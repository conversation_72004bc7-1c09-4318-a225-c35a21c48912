
module MediaPlan::Chime
  class BuildReportOptionsCommand
    prepend SimpleCommand

    def call
      [
        build_samsung_android_option,
        build_tapjoy_ios_option,
        build_adspostx_option,
        build_admarketplace_web_option,
        build_tapjoiy_android_option,
        build_performarkt_option,
        build_pointblank_option,
        build_blastbucks_option,
        build_influnce_mobile_option,
        build_xapads_option,
        build_inmobi_option,
        build_play2pay_android_option,
        build_play2pay_universal_option,
        build_scrambly_ios_option,
        build_scrambly_android_option,
        build_adaction_ios_super_option,
        build_adaction_android_option,
        build_adaction_ios_fetch_option,
        build_aye_t_studios_android_option,
        build_adcommunal_android_option,
        build_mobsuccess_android_option,
        build_tmobile_option,
        build_jampp_option,
      ]
    end

    private

    def build_samsung_android_option
      { vendor_id: 313, campaign_ids: chime_android_campaign_ids, os: 'Android', source: 'Agency Dash', inventory: 'Android' }
    end

    def build_tapjoy_ios_option
      { vendor_id: 127, campaign_ids: chime_ios_campaign_ids, os: 'iOS', source: 'FE', inventory: 'iOS' }
    end

    def build_adspostx_option
      { vendor_id: 485, campaign_ids: chime_ios_campaign_ids, source: 'FE', inventory: 'Web' }
    end

    def build_admarketplace_web_option
      campaign_ids = Campaign.where(name: 'Chime_mweb_US_CPC').ids

      { vendor_id: 498, campaign_ids: campaign_ids, source: 'FE', inventory: 'Web' }
    end

    def build_tapjoiy_android_option
      { vendor_id: 127, campaign_ids: chime_android_campaign_ids, os: 'Android', source: 'Agency Dash', inventory: 'Android' }
    end

    def build_performarkt_option
      { vendor_id: 146, campaign_ids: chime_ios_campaign_ids, os: 'iOS', source: 'Agency Dash', inventory: 'iOS' }
    end

    def build_pointblank_option
      { vendor_id: 408, campaign_ids: chime_ios_campaign_ids, os: 'iOS', source: 'Agency Dash', inventory: 'iOS' }
    end

    def build_blastbucks_option
      { vendor_id: 491, campaign_ids: chime_android_campaign_ids, source: 'FE', inventory: 'Android' }
    end

    def build_influnce_mobile_option
      campaign_ids = Campaign.where(name: 'Chime_Android_US_CPI_InfluenceMobile_Agency').ids

      { vendor_id: 171, campaign_ids: campaign_ids, os: 'Android', source: 'Agency Dash', inventory: 'Android' }
    end

    def build_xapads_option
      campaign_ids = Campaign.where(name: 'Chime_Android_US_CPI_Xapads_Agency').ids

      { vendor_id: 358, campaign_ids: campaign_ids, os: 'Android', source: 'Agency Dash', inventory: 'Android' }
    end

    def build_inmobi_option
      campaign_ids = Campaign.where(name: 'Chime_Android_US_CPI_Glance_Agency').ids

      { vendor_id: 129, campaign_ids: campaign_ids, os: 'Android', source: 'Agency Dash', inventory: 'Android' }
    end

    def build_play2pay_android_option
      campaign_ids = Campaign.where(name: 'Chime_Android_US_CPI_Play2Pay_Agency').ids

      { vendor_id: 503, campaign_ids: campaign_ids, os: 'Android', source: 'Agency Dash', inventory: 'Android' }
    end

    def build_play2pay_universal_option
      campaign_ids = Campaign.where(name: 'Chime_Universal_US_CPI_Play2Pay_Agency').ids

      # os is for quering the Campaign. The os of Chime_Universal_US_CPI_Play2Pay_Agency is Android
      { vendor_id: 503, campaign_ids: campaign_ids, os: 'Android', source: 'Agency Dash', inventory: 'Universal'}
    end

    def build_scrambly_android_option
      campaign_ids = Campaign.where(name: "Chime_Android_US_CPA_Agency").ids

      { vendor_id: 468, campaign_ids: campaign_ids, os: "Android", source: "Agency Dash", inventory: "Android" }
    end

    def build_scrambly_ios_option
      campaign_ids = Campaign.where(name: "Chime_iOS_US_CPA_Agency").ids

      { vendor_id: 468, campaign_ids: campaign_ids, os: "iOS", source: "Agency Dash", inventory: "iOS" }
    end

    def build_adaction_android_option
      campaign_ids = Campaign.where(name: "Chime_Android_US_Incent_Super_Agency").ids

      { vendor_id: 264, campaign_ids: campaign_ids, os: "Android", source: "Agency Dash", inventory: "Android" }
    end

    def build_adaction_ios_super_option
      campaign_ids = Campaign.where(name: "Chime_iOS_US_Incent_Super_Agency").ids

      { vendor_id: 264, campaign_ids: campaign_ids, os: "iOS", source: "Agency Dash", inventory: "iOS" }
    end

    def build_adaction_ios_fetch_option
      campaign_ids = Campaign.where(name: "Chime_iOS_US_Incent_Fetch_Agency").ids

      { vendor_id: 264, campaign_ids: campaign_ids, os: "iOS", source: "Agency Dash", inventory: "iOS" }
    end

    # ayeT-Studios
    def build_aye_t_studios_android_option
      campaign_ids = Campaign.where(name: 'Chime_Android_US_CPA_Incent').ids

      { vendor_id: 316, campaign_ids: campaign_ids, os: 'Android', source: 'FE', inventory: 'Android' }
    end

    # Adcommunal
    def build_adcommunal_android_option
      campaign_ids = Campaign.where(name: 'Chime_Android_US_CPA_Incent').ids

      { vendor_id: 447, campaign_ids: campaign_ids, os: 'Android', source: 'FE', inventory: 'Android' }
    end

    # Mobsuccess
    def build_mobsuccess_android_option
      campaign_ids = Campaign.where(name: 'Chime_Android_US_CPA_Incent').ids

      { vendor_id: 413, campaign_ids: campaign_ids, os: 'Android', source: 'FE', inventory: 'Android' }
    end

    # Tmobile
    def build_tmobile_option
      campaign_ids = Campaign.where(name: 'Chime_Universal_US_CPI_Agency_TAds').ids

      { vendor_id: 459, campaign_ids: campaign_ids, os: 'Universal', source: 'Agency Dash', inventory: 'Universal' }
    end

    def chime_android_campaign_ids
      Campaign.where(client_id: 74, os: 'Android').ids
    end

    def chime_ios_campaign_ids
      Campaign.where(client_id: 74, os: 'iOS').ids
    end

    # Jampp
    def build_jampp_option
      campaign_ids = Campaign.where(name: 'Chime_iOS_US_CPA_SKAN_Agency').ids

      { vendor_id: 454, campaign_ids: campaign_ids, os: 'iOS', source: 'Agency Dash', inventory: 'iOS' }
    end
  end
end
