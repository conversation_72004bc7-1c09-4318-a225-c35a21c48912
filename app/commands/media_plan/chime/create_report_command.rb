module MediaPlan::Chime
  class CreateReportCommand
    prepend SimpleCommand

    attr_reader  :start_date, :end_date

    def initialize(end_date: Date.yesterday)
      @start_date = if end_date == end_date.beginning_of_month
        end_date
      else
        end_date.beginning_of_month
      end

      @end_date = end_date
    end

    def call
      CSV.generate(headers: true) do |csv|
        csv << headers

        generated_data.each do |result|
          # skip to generate csv row when column D to column G are all zero
          next if result[3].zero? && result[4].zero? && result[5].zero? && result[6].zero?

          csv << result
        end
        csv
      end
    end

    def generate_file_name
      "chime_media_plan_#{start_date}_#{end_date}.csv"
    end

    def generate_subject
      "Chime Media Plan Report #{start_date.strftime("%Y/%m/%d")} -  #{end_date.strftime("%Y/%m/%d")}"
    end

    def headers
      [
        'Source',
        'Current Partner',
        'Inventory',
        'Current Enrollments',
        'Gross Spend',
        'Net Spend',
        'Projection Enroll',
        "Click Url Links",
      ]
    end

    def generate_content
      @email_content ||= <<-HTML
        Please find the attached file

        Thanks!
      HTML
    end

    def generated_data
      options = MediaPlan::Chime::BuildReportOptionsCommand.call.result
      new_entry_options = MediaPlan::Chime::BuildNewEntryOptionsCommand.call(start_date: start_date, end_date: end_date, existing_entries: options).result

      options.concat(new_entry_options).map do |option|
        build_row(option)
      end
    end

    private

    def build_row(options)
      vendor = Vendor.find(options[:vendor_id])
      row_base = [ options[:source], vendor.vendor_name, options[:inventory] ]

      row_base + build_row_extras(options)
    end

    def build_row_extras(options)
      vendor_id = options[:vendor_id]
      campaign_ids = options[:campaign_ids]
      os = options[:os]

      registrations = if options[:source] == 'Paused - agency dashboard + FE'
        registration_query = fe_registration_query(vendor_id, [1505])

        reg_1505 = ConversionRecordRedshift.connection.execute(registration_query).first.values.first.to_i
        reg_2735 = AppsflyerInAppEvent.includes(:campaign).
          where(campaigns: {client_id: 74, os: 'Android'}).
          where(date: start_date..end_date, vendor_id: 6, campaign_id: 2735).
          sum(:registration)

        reg_1505 + reg_2735
      elsif options[:source] == 'Agency Dash'
        AppsflyerInAppEvent.includes(:campaign).
          where(campaigns: {client_id: 74, os: os}).
          where(date: start_date..end_date, vendor_id: vendor_id, campaign_id: campaign_ids).
          sum(:registration)
      else
        registration_query = fe_registration_query(vendor_id, campaign_ids)

        ConversionRecordRedshift.connection.execute(registration_query).first.values.first.to_i
      end

      spend_query = fe_spend_query(vendor_id, campaign_ids)

      gross_spend, net_spend = if options[:source] == 'Agency Dash'
        NetSpend.connection.execute(direct_spend_query(vendor_id, campaign_ids)).first.map { |k, v| v.to_f.round(2) }
      else
        # For the FE source, we need to add the direct spend from the previous day to the FE spend if the direct_spend was recoreded after v4_campaigns_view was refreshed.
        fe_spend = ConversionRecordRedshift.connection.execute(spend_query).first.map { |k, v| v.to_f.round(2) }
        yesterday_direct_spend = get_yesterday_direct_spend(vendor_id, campaign_ids).to_a
        fe_spend.zip(yesterday_direct_spend).map { |v| v.compact.inject(:+) }
      end

      projection_enroll = registrations.to_d *  end_date.end_of_month.day / end_date.day

      click_url_links = build_click_url_links(vendor_id, campaign_ids)

      [registrations.to_i, gross_spend, net_spend, projection_enroll.to_i, click_url_links]
    end

    def build_click_url_links(vendor_id, campaign_ids)
      rows = ConversionRecordRedshift.connection.execute(load_campaign_id_and_vendor_id_query(vendor_id, campaign_ids)).to_a +
        AppsflyerInAppEvent.connection.execute(load_campaign_id_and_vendor_id_from_in_app_events_query(vendor_id, campaign_ids)).to_a +
        AppsflyerAgencyReport.connection.execute(load_campaign_id_and_vendor_id_from_appsfyer_agency_report_query(vendor_id, campaign_ids)).to_a

      rows.uniq.map do |item|
        entity = OpenStruct.new(item)
        click_url = ClickUrl.find_by(vendor_id: entity.vendor_id, campaign_id: entity.campaign_id)
        next if click_url.blank?

        "https://admin.feedmob.com/click_urls/#{click_url.id}"
      end.compact.join("; ")
    end


    def get_yesterday_direct_spend(vendor_id, campaign_ids)
      yesterday = Date.yesterday
      return if end_date < yesterday

      sql = ActiveRecord::Base.sanitize_sql_array([<<-SQL, vendor_id: vendor_id, campaign_ids: campaign_ids, spend_date: yesterday, refresh_timestamp: v4_campaigns_view_refresh_timestamp])
        select
          sum(gross_spend_cents / 100.0) as gross_spend,
          sum(net_spend_cents / 100.0) as net_spend
        from net_spends
        where campaign_id in (:campaign_ids)
        and vendor_id = :vendor_id
        and spend_date = :spend_date
        and created_at > :refresh_timestamp
      SQL
      NetSpend.connection.execute(sql).first.values.map { |v| v.to_f.round(2) }
    end

    def v4_campaigns_view_refresh_timestamp
      cache_value = RedisClient.fb_tracking_redis.get('RefreshV4CampaignsViewJob')
      return if cache_value.nil?
      Time.zone.parse(cache_value)
    end

    def direct_spend_query(vendor_id, campaign_ids)
      <<-SQL
        select
          sum(gross_spend_cents / 100.0) as gross_spend,
          sum(net_spend_cents / 100.0) as net_spend
        from net_spends
        where campaign_id in (#{campaign_ids.join(',')})
        and vendor_id = #{vendor_id}
        and spend_date between '#{start_date}' and '#{end_date}'
      SQL
    end

    def fe_spend_status(vendor_id)
      if [Vendor::BLASTBUCKS_ID, Vendor::ADCOMMUNAL_ID].include?(vendor_id)
        ['normal', 'injected', 'over_cap']
      else
        ['normal', 'injected', 'over_cap', 'manual_stopped']
      end
    end

    def fe_spend_query(vendor_id, campaign_ids)
      <<-SQL
        select
          sum(spend) as gross_spend,
          sum(net_spend) as net_spend
        from v4_campaigns_view
        where campaign_id in (#{campaign_ids.join(',')})
        and vendor_id = #{vendor_id}
        and status in ('#{fe_spend_status(vendor_id).join("','")}')
        and calculate_date between '#{start_date}' and '#{end_date}'
      SQL
    end

    def fe_registration_query(vendor_id, campaign_ids)
      <<-SQL
        select sum(registration_count) as registrations
        from v4_campaigns_view
        where campaign_id in (#{campaign_ids.join(',')})
        and vendor_id = #{vendor_id}
        and status in ('normal', 'injected','over_cap', 'manual_stopped')
        and calculate_date between '#{start_date}' and '#{end_date}'
      SQL
    end

    def load_campaign_id_and_vendor_id_query(vendor_id, campaign_ids)
      ActiveRecord::Base.sanitize_sql([<<-SQL, start_date: start_date, end_date: end_date, vendor_id: vendor_id, campaign_ids: campaign_ids])
        SELECT
            campaign_id,
            vendor_id
        FROM
            v4_campaigns_view
        WHERE
            vendor_id = :vendor_id
            AND campaign_id IN (:campaign_ids)
            AND status IN ('normal', 'injected', 'over_cap', 'manual_stopped')
            AND calculate_date BETWEEN :start_date AND :end_date
        GROUP BY
            campaign_id,
            vendor_id
        HAVING SUM(registration_count) > 0;
      SQL
    end

    def load_campaign_id_and_vendor_id_from_in_app_events_query(vendor_id, campaign_ids)
      ActiveRecord::Base.sanitize_sql([<<-SQL, start_date: start_date, end_date: end_date, vendor_id: vendor_id, campaign_ids: campaign_ids])
        SELECT
            campaign_id,
            vendor_id
        FROM
            appsflyer_in_app_events
        WHERE
            vendor_id = :vendor_id
            AND campaign_id IN (:campaign_ids)
            AND date BETWEEN :start_date AND :end_date
        GROUP BY
            campaign_id,
            vendor_id
        HAVING SUM(registration) > 0;
      SQL
    end

    def load_campaign_id_and_vendor_id_from_appsfyer_agency_report_query(vendor_id, campaign_ids)
      ActiveRecord::Base.sanitize_sql([<<-SQL, start_date: start_date, end_date: end_date, vendor_id: vendor_id, campaign_ids: campaign_ids])
        SELECT
            campaign_id,
            vendor_id
        FROM
            appsflyer_agency_reports
        WHERE
            vendor_id = :vendor_id
            AND campaign_id IN (:campaign_ids)
            AND date BETWEEN :start_date AND :end_date
        GROUP BY
            campaign_id,
            vendor_id
        HAVING SUM(registration) > 0;
      SQL
    end
  end
end
