# frozen_string_literal: true

module MediaPlan::Chime
  class BuildNewEntryOptionsCommand
    prepend SimpleCommand

    def initialize(start_date:, end_date:, existing_entries: [])
      @start_date = start_date
      @end_date = end_date
      @existing_entries = existing_entries
    end

    def call
      campaigns = load_campaigns
      remove_existing_entry(campaigns)
      build_report_options(campaigns)
    end

      private

      attr_reader :start_date, :end_date, :existing_entries

      def load_campaigns
        rows = ConversionRecordRedshift.connection.execute(load_campaigns_sql).to_a + AppsflyerInAppEvent.connection.execute(load_agency_campaigns_sql).to_a

        @load_campaigns ||= rows.uniq.map do |item|
          OpenStruct.new(item)
        end.index_by { |item| [item.vendor_id.to_i, item.campaign_id.to_i] }
      end

      def remove_existing_entry(campaigns)
        existing_entries.each do |option|
          option[:campaign_ids].each do |campaign_id|
            campaigns.delete([option[:vendor_id].to_i, campaign_id.to_i])
          end
        end
      end

      def build_report_options(campaigns)
        campaigns.map do |(vendor_id, campaign_id), _data|
          click_url = ClickUrl.includes(:campaign).find_by(vendor_id: vendor_id, campaign_id: campaign_id)
          campaign = click_url.campaign
          {
            vendor_id: vendor_id,
            campaign_ids: [campaign_id],
            os: campaign.os,
            inventory: campaign.os,
            source: click_url.fm_link? ? 'FE' : 'Agency Dash'
          }
        end
      end

      def load_campaigns_sql
        ActiveRecord::Base.sanitize_sql([<<-SQL, start_date: start_date, end_date: end_date])
          WITH report AS (
              SELECT
                  campaign_id,
                  vendor_id,
                  SUM(registration_count) AS registration_count
              FROM
                  v4_campaigns_view
              WHERE
                  campaign_id IN (
                      SELECT DISTINCT campaign_id
                      FROM click_url_infos
                      WHERE client_id = 74 -- Chime
                  )
                  AND status IN ('normal', 'injected', 'over_cap', 'manual_stopped')
                  AND calculate_date BETWEEN :start_date AND :end_date
              GROUP BY
                  campaign_id,
                  vendor_id
          )
          SELECT
              campaign_id,
              vendor_id
          FROM
              report
          WHERE
              registration_count > 0;
        SQL
      end

      def load_agency_campaigns_sql
        ActiveRecord::Base.sanitize_sql([<<-SQL, start_date: start_date, end_date: end_date])
          SELECT
              t1.campaign_id,
              t1.vendor_id
          FROM
              appsflyer_in_app_events as t1
          JOIN campaigns as t2 ON t1.campaign_id = t2.id
          WHERE
            date BETWEEN :start_date AND :end_date
            AND t2.name = 'Chime'
            AND t1.registration > 0
        SQL
      end
  end
end
