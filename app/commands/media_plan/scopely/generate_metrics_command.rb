# frozen_string_literal: true

module MediaPlan::Scopely
  class GenerateMetricsCommand
    include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

    prepend SimpleCommand

    def initialize(start_date:, end_date:)
      @start_date = start_date
      @end_date = end_date
    end

    def call
      res = {}
      rejected_24metrics_count = download_from_24metrics_by_date(start_date, end_date)
      campaign_mappings = AdjustCampaignMapping.scopely_campaign_mappings
      rejected_24metrics_count.map do |report_campaign_network, count|
        mapping = campaign_mappings[report_campaign_network]
        tab_name = mapping[:report_category]
        column_title = mapping[:column_title]
        vendor = mapping[:vendor_name]
        res[tab_name] ||= {}
        res[tab_name][column_title] ||= Hash.new(0)
        res[tab_name][column_title][vendor] += count
      end
      res
    end

    private

    attr_reader :start_date, :end_date

    def download_from_24metrics_by_date(start_date, end_date)
      api_token = 'iZIXM58tdEgWQDCzQ3vo0IXKaLeKCvmNnolUXNiVpypTHfPKy4Yw41yeNMzn'
      user_id = 695
      tracker_id = 628
      timezone = 'UTC'
      count = 1000
      page = 1
      search_fields = { term: 'status', query: 'rejected' }.to_json
      url = "https://fraudshield.24metrics.com/api/v1/reports/conversion.json"
      all_data = []
      
      loop do    
        response = RestClient.get(url, {
          params: {
            tracker_id: tracker_id,
            date_start: start_date,
            date_end: end_date,
            'search_fields[]': search_fields,
            user_id: user_id,
            api_token: api_token,
            timezone: timezone,
            count: count,
            page: page
          },
          accept: :json
        })
    
        result = JSON.parse(response.body)
        filter_data = result['data']
        all_data.concat(filter_data)
    
        break if page >= result['last_page']
    
        page += 1
      end
  
      total = all_data.size
      if total == 10000
        SlackService.send_notification_to_channel("24metrics rejected records for #{date} has reached 10,000 records", :mighty)
      end
    
      all_data.group_by{|r| r['product_label']}.map{|k, records| [k, records.size]}.to_h
    end
  end
end
