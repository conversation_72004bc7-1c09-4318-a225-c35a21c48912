# frezen_string_literal: true

require 'googleauth'
require 'google/apis/sheets_v4'

class MediaPlan::Scopely::UpdateScopelyMediaPlanCommand
  include FileUtils
  include ActionView::Helpers::<PERSON><PERSON><PERSON>per

  prepend SimpleCommand

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1bmDIdWxYGec0RMOYlCqI4zYpdzTKdAlRqr7sOYyXzNo'.freeze

  attr_reader :start_date, :end_date, :date_range

  def initialize(start_date, end_date)
    @start_date = start_date.to_date
    @end_date = end_date.to_date
    @date_range = "#{start_date} ~ #{end_date}"
  end

  def call
    @service = Google::Apis::SheetsV4::SheetsService.new
    @service.authorization = authorizer
    performance_data.each do |tab_name, tab_data|
      response = @service.get_spreadsheet_values(SPREADSHEET_ID, "#{tab_name}!A:A")
      first_columns = response.values
      start_row_index = first_columns.find_index{|row| row.first == date_range}
      if start_row_index.nil?
        create_new_date_range_data(tab_name, tab_data)
      else
        update_date_range_data(tab_name, tab_data, start_row_index, first_columns)
      end
    end
  end

  def create_new_date_range_data(tab_name, tab_data)
    new_rows_needed = tab_data.sum{|_, records| records.size + 4}
    insert_new_rows(sheet_ids[tab_name], 0, new_rows_needed)

    @service.update_spreadsheet_value(SPREADSHEET_ID, "#{tab_name}!A1", { values: [[date_range]] }, value_input_option: 'USER_ENTERED')

    first_row_number = 2
    tab_data.each do |column_title, records|
      last_row_number = first_row_number
      values = []
      values << ([column_title] + headers)
      records.each_with_index do |record, index|
        rejected_24metrics_count = (rejected_24metrics_data[tab_name][column_title][record[0]] rescue 0)
        last_row_number += 1
        values << row_value(record, last_row_number, rejected_24metrics_count)
      end
      values << total_row(first_row_number + 1, last_row_number)
      last_row_number += 1
      value_range_object = {
        major_dimension: "ROWS",
        values: values
      }
      @service.update_spreadsheet_value(SPREADSHEET_ID, "#{tab_name}!A#{first_row_number}:U#{last_row_number}", value_range_object, value_input_option: 'USER_ENTERED')
      first_row_number = last_row_number + 2
    end
  end

  def update_date_range_data(tab_name, tab_data, start_row_index, first_columns)
    tab_data.each do |column_title, records|
      # 从匹配上的日期范围下面开始找到第一个匹配的tolumn title
      row_index = first_columns[start_row_index+1..-1].find_index{|row| row.first == column_title}
      row_index = row_index + start_row_index + 1
      row_count = get_row_count(row_index, first_columns)
      if records.size != row_count
        SlackService.send_notification_to_channel("#{self.class.name}: 更新的行数和原来的行数不一致，需要人工更新#{tab_name}_#{column_title}", :mighty)
        return
      end
      values = []
      sheet_row_number = row_index + 1
      records.each_with_index do |record, index|
        updated_row_number = sheet_row_number + index + 1
        rejected_24metrics_count = rejected_24metrics_data[tab_name][column_title][record[0]] rescue 0
        values << row_value(record, updated_row_number, rejected_24metrics_count)
      end
      value_range_object = {
        major_dimension: "ROWS",
        values: values
      }
      @service.update_spreadsheet_value(SPREADSHEET_ID, "#{tab_name}!A#{sheet_row_number+1}:S#{sheet_row_number+row_count}", value_range_object, value_input_option: 'USER_ENTERED')
    end
  end

  private

  def sheet_ids
    @sheet_ids ||= @service.get_spreadsheet(SPREADSHEET_ID).sheets.map{|sheet| [sheet.properties.title, sheet.properties.sheet_id]}.to_h
  end

  def row_value(record, row_number, rejected_24metrics_count)
    [
      record[0], # vendor
      record[1], # clicks
      record[2], # install
      record[3], # spend
      "=D#{row_number}/C#{row_number}", # cpi
      "=C#{row_number}/B#{row_number}", # cvr%
      record[4], # retained_users_d1
      record[5], # retained_users_d3
      "=G#{row_number}/C#{row_number}", # d1rr
      record[5], # retained_users_d3
      "=J#{row_number}/C#{row_number}", # d3rr
      record[6], # retained_users_d7
      "=L#{row_number}/C#{row_number}", # d7rr
      record[7], # all_revenue_total_d7
      "=N#{row_number}/D#{row_number}", # d7 recoup
      "", # 空列
      "=C#{row_number}", # ios us installs
      rejected_24metrics_count, # rejected 24 metrics
      "=R#{row_number}/Q#{row_number}" # rejected % this week
    ]
  end

  def insert_new_rows(sheet_id, new_row_start_index, new_row_end_index)
    insert_dimension_request = Google::Apis::SheetsV4::InsertDimensionRequest.new(
      range: Google::Apis::SheetsV4::DimensionRange.new(
        sheet_id: sheet_id,
        dimension: 'ROWS',
        start_index: new_row_start_index,
        end_index: new_row_end_index
      ),
      inherit_from_before: false
    )

    batch_update_request = Google::Apis::SheetsV4::BatchUpdateSpreadsheetRequest.new(
      requests: [Google::Apis::SheetsV4::Request.new(insert_dimension: insert_dimension_request)]
    )
    response = @service.batch_update_spreadsheet(SPREADSHEET_ID, batch_update_request)
  end

  def get_row_count(row_index, first_columns)
    row_count = 0
    first_columns[row_index+1..-1].each_with_index do |row, index|
      break if row.first&.strip == 'Total'
      row_count += 1
    end
    row_count
  end

  def authorizer
    @authorizer ||= begin
      authorizer = Google::Auth::ServiceAccountCredentials.make_creds(
        json_key_io: File.open(client_secrets_file_path),
        scope: SCOPE
      )
      authorizer.fetch_access_token!
      authorizer
    end
  end

  def client_secrets_file_path
    File.join(current_path, "config", 'feedmob-4bbd49e741a5.json')
  end

  def current_path
    Dir["#{Rails.root}"]
  end

  def performance_data
    @performance_data ||= MediaPlan::Scopely::GenerateEventSpendCommand.call(start_date: start_date, end_date: end_date).result
  end

  def rejected_24metrics_data
    @rejected_24metrics_data ||= MediaPlan::Scopely::GenerateMetricsCommand.call(start_date: start_date, end_date: end_date).result
  end

  def headers
    ["CLICKS", "INSTALL", "SPEND", "CPI", "CVR %", "D1 Retained users", "D3 Retained users", "D1RR", "D3 Retained users", "D3RR", "D7 Retained users", "D7RR", "D7 Rev", "D7 Recoup", "", "iOS US Installs", "Rejected 24 metrics", "Rejected % This Week", "Rejected % Previous Week", "% Change"]
  end

  def total_row(first_row_number, last_row_number)
    total_row = ['Total']
    ['B', 'C', 'D'].each do |col|
      total_row << "=SUM(#{col}#{first_row_number}:#{col}#{last_row_number})"
    end
    total_row << "=D#{last_row_number+1}/C#{last_row_number+1}"
    total_row << "=C#{last_row_number+1}/B#{last_row_number+1}"
    total_row << "=SUM(G#{first_row_number}:G#{last_row_number})"
    total_row << "=SUM(H#{first_row_number}:H#{last_row_number})"
    total_row << "=G#{last_row_number+1}/C#{last_row_number+1}"
    total_row << "=SUM(J#{first_row_number}:J#{last_row_number})"
    total_row << "=J#{last_row_number+1}/C#{last_row_number+1}"
    total_row << "=SUM(L#{first_row_number}:L#{last_row_number})"
    total_row << "=L#{last_row_number+1}/C#{last_row_number+1}"
    total_row << "=SUM(N#{first_row_number}:N#{last_row_number})"
    total_row << "=N#{last_row_number+1}/D#{last_row_number+1}"
    total_row
  end
end
