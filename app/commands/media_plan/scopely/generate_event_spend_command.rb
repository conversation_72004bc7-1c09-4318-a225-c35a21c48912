# frozen_string_literal: true

module MediaPlan::Scopely
  class GenerateEventSpendCommand
    include ActionView::Helpers::<PERSON><PERSON>elper

    prepend SimpleCommand

    def initialize(start_date:, end_date:)
      @start_date = start_date
      @end_date = end_date
    end

    def call
      if end_date < start_date
        errors.add(:create_scorecard_report, 'end_date must be greater than or equal to start_date')
        return
      end

      fetch_report_from_db
      fetch_report_from_adjust_api 
      if failure?
        return
      end

      if report_from_adjust_dash.blank? && report_from_db.blank?
        errors.add(:create_scorecard_report, 'no data found')
        return
      end

      create_report_group_by_tab
    end

    private

    attr_reader :start_date, :end_date, :report_from_adjust_dash, :report_from_db

    def fetch_report_from_adjust_api
      command = Export::Scopely::FetchReportFromAdjustApiCommand.call(start_date: start_date, end_date: end_date)
      if command.failure?
        errors.add(:create_scorecard_report, command.errors[:fetch_report_from_adjust_api].join(', '))
        return
      end

      @report_from_adjust_dash = command.result.group_by { |r| r.campaign_network }
    end

    def fetch_report_from_db
      command = Export::Scopely::FetchReportFromDbCommand.call(start_date: start_date, end_date: end_date)
      if command.failure?
        errors.add(:create_scorecard_report, command.errors[:fetch_report_from_db].join(', '))
        return
      end

      @report_from_db = command.result.index_by { |r| [r.campaign_id, r.vendor_id] }
    end

    def create_report_group_by_tab
      res = {}
      campaign_mappings = AdjustCampaignMapping.scopely_campaign_mappings
      aggregate_report_by_campaign_vendor.each do |(campaign, vendor), report|
        mapping = campaign_mappings[campaign]
        tab_name = mapping[:report_category]
        column_title = mapping[:column_title]
        next if tab_name.blank? || column_title.blank?
        res[tab_name] ||= {}
        res[tab_name][column_title] ||= []
        res[tab_name][column_title] << [
          vendor,
          report&.sum {|item| item.clicks.to_i },
          report&.sum {|item| item.installs.to_i },
          report&.sum {|item| item.spend.to_f },
          report&.sum {|item| item.retained_users_d1.to_i },
          report&.sum {|item| item.retained_users_d3.to_i },
          report&.sum {|item| item.retained_users_d7.to_i },
          report&.sum {|item| item.all_revenue_total_d7.to_f },
        ]
      end
      res
    end

    def aggregate_report_by_campaign_vendor
      campaign_mappings = AdjustCampaignMapping.scopely_campaign_mappings
      map_report_to_scorecard.group_by { |r| [r.campaign, r.vendor, r.campaign_id, r.vendor_id] }
        .map do |(campaign, vendor, campaign_id, vendor_id), report|
          db_report = @report_from_db[[campaign_id, vendor_id]]
          next if vendor.blank? || campaign_mappings[campaign].blank?
          OpenStruct.new(
            campaign: campaign,
            vendor: vendor,
            clicks: report.sum {|item| item.clicks.to_i },
            installs: report.sum {|item| item.installs.to_i },
            spend: db_report&.spend.to_f,
            retained_users_d1: report.sum {|item| item.retained_users_d1.to_i },
            retained_users_d3: report.sum {|item| item.retained_users_d3.to_i },
            retained_users_d7: report.sum {|item| item.retained_users_d7.to_i },
            all_revenue_total_d7: report.sum {|item| item.all_revenue_total_d7.to_f },
          )
        end.compact.sort_by{|r| r.vendor}.group_by { |r| [r.campaign, r.vendor] }
    end

    def map_report_to_scorecard
      @map_report_to_scorecard ||=
        @report_from_adjust_dash.map do |campaign_network, adjust_report|
          mapping = AdjustCampaignMapping.scopely_campaign_mappings[campaign_network] || {}
          OpenStruct.new(
            campaign: mapping[:campaign_name].presence || campaign_network,
            vendor: mapping[:vendor_name],
            campaign_id: mapping[:campaign_id],
            vendor_id: mapping[:vendor_id],
            clicks: adjust_report.sum {|item| item.clicks.to_i },
            installs: adjust_report.sum {|item| item.installs.to_i },
            retained_users_d1: adjust_report.sum {|item| item.retained_users_d1.to_i },
            retained_users_d3: adjust_report.sum {|item| item.retained_users_d3.to_i },
            retained_users_d7: adjust_report.sum {|item| item.retained_users_d7.to_i },
            all_revenue_total_d1: adjust_report.sum {|item| item.all_revenue_total_d1.to_f },
            all_revenue_total_d3: adjust_report.sum {|item| item.all_revenue_total_d3.to_f },
            all_revenue_total_d7: adjust_report.sum {|item| item.all_revenue_total_d7.to_f },
          )
        end
    end
  end
end
