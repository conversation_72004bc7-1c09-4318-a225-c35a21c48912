require 'sendgrid-ruby'

class MailCommand
  attr_reader :attachments, :date, :recipients

  EMAIL_SENDER = '<EMAIL>'

  def initialize(
    recipients: [],
    attachments: [],
    date: nil,
    filename: nil,
    csv_stream: nil
    )

    @attachments = attachments
    @date        = date
    @recipients  = recipients
    attachments_initializer(attachments, filename, csv_stream)
  end

  def send_mail(subject = nil, body_content = nil)
    subject ||= "Empty subject"
    content = body_content&.html_safe || 'Please check attachment'
    send_mail_with_attachment(subject, content, recipients, report_attachments)
    record = MailerLog.create(
      sender: EMAIL_SENDER,
      receipients: recipients,
      subject: subject,
      email_body: content,
      code: 200
    )
    if attachments && attachments.any?
      record.attachments = attachments.map do |filename, file_stream|
        MailLogAttachmentCommand.new.upload_stream(record.id, filename, file_stream)
      end
      record.save
    end
  end

  def report_attachments
    send_grid_attachments = []
    Array(attachments).each do |filename, csv_stream|
      attachment = SendGrid::Attachment.new
      attachment.content = Base64.strict_encode64(csv_stream)
      attachment.type = attachment_type(filename)
      attachment.filename = filename
      attachment.disposition = 'attachment'
      send_grid_attachments << attachment
    end

    send_grid_attachments
  end

  def attachment_type(filename)
    if filename.end_with?('tsv')
      'text/plain'
    else
      'text/csv'
    end
  end

  def cc_recipients
    # 暂不抄送
  end

  def send_mail_with_attachment(subject, content, emails, attachments)
    return if emails.blank?

    unless Rails.env.production?
      TestMailer.preview(emails, subject, content, attachments: attachments).deliver_now
      return
    end

    from = SendGrid::Email.new(email: EMAIL_SENDER)
    personalization = SendGrid::Personalization.new

    emails.each do |email|
      personalization.add_to(SendGrid::Email.new(email: email))
    end

    mail                  = SendGrid::Mail.new
    mail.from             = from
    mail.subject          = subject
    mail.add_personalization(personalization)
    content = SendGrid::Content.new(type: 'text/html', value: content)
    mail.add_content(content)
    attachments.flatten.each do |item|
      mail.add_attachment(item)
    end

    sg = SendGrid::API.new(api_key: ENV['SENDGRID_API_KEY'])
    sg.client.mail._('send').post(request_body: mail.to_json)
  end

  def attachments_initializer(attachments, filename, file_stream)
    @attachments = [].tap do |final_attachments|
      final_attachments.concat(attachments) if attachments.present?
      final_attachments << [filename, file_stream] if filename.present? && file_stream&.size&.positive?
    end
  end
end
