class Metrics::CheckDataCommand
  prepend SimpleCommand

  ACCEPTABLE_DAILY_SPEND_DIFFERENCE = 2

  attr_reader :start_date, :end_date, :options, :red_rows

  def initialize(options = {})
    @start_date = options[:start_date]
    @end_date   = options[:end_date]
    @options    = options.reverse_merge(
      status_group: 'default',
      cost_group:   'cpi',
      conversion:   'all_purchase'
    )
    @red_rows = []
  end

  def call
    run_spend_jobs if different_spend?

    send_email_alert
  end

  def difference?
    check_impression
    check_click
    check_install
    different_spend?
    @red_rows.length > 0
  end

  def check_impression
    result = realtime_data
    if (result['impressions'].to_i - v4_campaigns_view_data['impressions'].to_i).abs > 5
      @red_rows.append('impression')
      true
    end
  end

  def check_click
    result = realtime_data
    if (result['clicks'].to_i - v4_campaigns_view_data['clicks'].to_i).abs > 5
      @red_rows.append('click')
      true
    end
  end

  def check_install
    result = realtime_data
    if (result['install_count'].to_i - v4_campaigns_view_data['install_count'].to_i).abs >= 3
      @red_rows.append('install')
      true
    end
  end

  def different_spend?
    v4_spend_lookup = v4_campaigns_view_daily_data.map{|r| [r['calculate_date'], r]}.to_h
    realtime_daily_data.each do |row|
      return true unless v4_spend_lookup[row['report_date']]

      net_spend_result = (row['net_spend'].to_f - v4_spend_lookup[row['report_date']]['net_spend'].to_f).abs >= ACCEPTABLE_DAILY_SPEND_DIFFERENCE
      gross_spend_result = (row['gross_spend'].to_f - v4_spend_lookup[row['report_date']]['gross_spend'].to_f).abs >= ACCEPTABLE_DAILY_SPEND_DIFFERENCE
      @red_rows.append('net_spend') if net_spend_result
      @red_rows.append('gross_spend') if gross_spend_result
      return true if net_spend_result || gross_spend_result
    end
    false
  end

  def v4_campaigns_view_daily_data
    @v4_campaigns_view_daily_data ||= HourlyImpressionCount.connection.select_all(
      <<-SQL
        SELECT 
          calculate_date,
          SUM(impression_count) AS impressions,
          SUM(click_count)      AS clicks,
          SUM(
            CASE
            WHEN status IN ('#{redshift_install_statuses.join("','")}')
            THEN install_count
            ELSE 0
            END
          ) AS install_count,
          SUM(#{redshift_normalize_spend :net_spend})  AS net_spend,
          SUM(#{redshift_normalize_spend :spend})      AS gross_spend
        FROM v4_campaigns_view
        WHERE calculate_date >= '#{start_date}' AND calculate_date <= '#{end_date}'
          AND #{conditions(:redshift)}
        GROUP BY 1
        ORDER BY 1
      SQL
    )
  end

  def v4_campaigns_view_data
    %w[impressions clicks install_count net_spend gross_spend].map do |key|
      [key, v4_campaigns_view_daily_data.map{|r| key =~ /spend/ ? r[key].to_f : r[key].to_i }.reduce(0, :+)]
    end.to_h
  end

  def realtime_daily_data
    EventMetric.connection.select_all(
      <<-SQL
        SELECT
          report_date,
          SUM(impression) AS impressions,
          SUM(click) AS clicks,
          SUM(#{normalize_install(install_statuses)})     AS install_count,
          SUM(#{normalize_spend :net_spend} + #{normalize_spend :cpm_net_spend})     AS net_spend,
          SUM(#{normalize_spend :gross_spend} + #{normalize_spend :cpm_gross_spend}) AS gross_spend
        FROM event_metrics
        WHERE report_date >= '#{start_date}' AND report_date <= '#{end_date}'
          AND #{conditions}
        GROUP BY 1
        ORDER BY 1
      SQL
    )
  end

  def realtime_data
    daily_data = realtime_daily_data
    %w[impressions clicks install_count net_spend gross_spend].map do |key|
      [key, daily_data.map{ |r| key =~ /spend/ ? r[key].to_f : r[key].to_i }.reduce(0, :+)]
    end.to_h
  end

  def client_ids
    @client_ids = options[:client_id]
  end

  def campaign_ids
    @campaign_ids ||= begin
      command_options = {
          client_id:            client_ids,
          campaign_id:          options[:campaign_id],
          parent_campaign_id:   options[:parent_campaign_id],
          excluded_campaign_id: options[:excluded_campaign_id],
          os:                   options[:os],
          country:              options[:country],
          develop:              options[:develop],
          exclude_twitter_old_campaigns: options[:exclude_twitter_old_campaigns],
      }

      CampaignIdExtractionCommand.call(command_options).result
    end
  end

  def vendor_ids
    @vendor_ids ||= begin
      command_options = {
        vendor_id:         options[:vendor_id],
        excluded_vendor_id: options[:excluded_vendor_id],
        vendor_collection: options[:vendor_collection],
        client_id:         client_ids,
        develop:           options[:develop],
        strategic_vendor: options[:strategic_vendor]
      }

      VendorIdExtractionCommand.call(command_options).result
    end
  end

  def redshift_statuses
    @redshift_statuses ||= begin
      status_list = NewConversionRecord::STATUS_GROUPS.dig(options[:status_group], :value)
      return status_list unless FeedConst::ValidPostInstallStatusOptions.include?(options[:status_group])

      cpi_group? ? [:normal, :injected, :over_cap, :manual_stopped] : [:normal, :injected]
    end
  end

  def statuses
    @statuses ||= begin
      status_list = NewConversionRecord::STATUS_GROUPS.dig(options[:status_group], :value).map do |name|
        NewConversionRecord::STATUS_ENUM[name]
      end

      return status_list unless FeedConst::ValidPostInstallStatusOptions.include?(options[:status_group])

      cpi_group? ? [1, 3, 5, 6] : [1, 3]
    end
  end

  def cpi_group?
    return true unless options[:cost_group].present?

    options[:cost_group].to_s == 'cpi'
  end

  def conditions(type = nil)
    sql_conditions = []
    sql_conditions << "campaign_id IN (#{campaign_ids.join(',')})"  if campaign_ids.present?
    sql_conditions << "vendor_id IN (#{vendor_ids.join(',')})"      if vendor_ids.present?
    if type == :redshift
      sql_conditions << "status IN ('#{redshift_statuses.join("','")}')" if redshift_statuses.present?
    else
      sql_conditions << "status::INTEGER IN (#{statuses.join(',')})" if statuses.present?
    end
    sql_conditions.join(' AND ')
  end

  def run_spend_jobs
    @red_rows = []
    (start_date..end_date).each do |date|
      Metrics::RefreshEventMetricSpendJob.perform_later(date: date)
    end
  end

  def send_email_alert
    subject = "Realtime & FE data is inconsistent #{start_date} to #{end_date}"
    passed = !difference?
    MetricMailer.data_check_alert(subject, realtime_data, v4_campaigns_view_data, @red_rows, passed).deliver_now
  end

  def install_statuses
    status_list = NewConversionRecord::STATUS_GROUPS.dig(options[:status_group], :value)
    status_list.map { |name| NewConversionRecord::STATUS_ENUM[name] }
  end

  def redshift_install_statuses
    NewConversionRecord::STATUS_GROUPS.dig(options[:status_group], :value)
  end

  def normalize_install(statuses)
    <<-SQL
    CASE
    WHEN status::INTEGER IN (#{statuses.join(',')})
    THEN install
    ELSE 0
    END
    SQL
  end

  def normalize_spend(field)
    <<-SQL
    CASE
    WHEN status::INTEGER IN (#{spend_statuses.join(',')})
    THEN #{field}
    ELSE 0
    END
    SQL
  end

  def spend_statuses
    status_list = NewConversionRecord::STATUS_GROUPS.dig(options[:status_group], :spend_calculation)
    status_list.map { |name| NewConversionRecord::STATUS_ENUM[name] }
  end

  def redshift_normalize_spend(field)
    <<-SQL
    CASE
    WHEN status IN ('#{redshift_spend_statuses.join("','")}')
    THEN #{field}
    ELSE 0
    END
    SQL
  end

  def redshift_spend_statuses
    NewConversionRecord::STATUS_GROUPS.dig(options[:status_group], :spend_calculation)
  end
end
