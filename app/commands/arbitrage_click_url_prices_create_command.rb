class ArbitrageClickUrlPricesCreateCommand
  include ArbitrageLevel2SwitchConcern

  prepend SimpleCommand

  attr_reader :click_url, :user, :options, :updated

  def initialize(click_url, user, options)
    @click_url = click_url
    @user      = user
    @options   = options
    @updated = false
  end

  def call
    batch_update? ? batch_update : update
  end

  private

  def batch_update?
    options[:price] && options[:price].is_a?(Array)
  end

  def is_na?(value)
    value.to_s.strip.downcase.match?(/^(n\/a|na|n\/a|nan)$/)
  end

  def batch_update
    click_url_history_attributes = []
    source = options[:source] || "Admin"

    # 存放相同修改的分组的数据，包括 attrs(historical_click_urls) 和 request_attrs( historical_click_url_change_requests)S
    grouped_changes = []; #[ {key: 'key1', attrs: {}, request_attrs: {}, dates: [] }, ...]
    current_key = ''

    options[:price].each do |params|
      assign_labels_for_click_url(params[:gross_cpi], params[:net_cpi], params[:margin]) if params[:spend_date].to_date == Date.today
      params[:gross_cpi] = is_na?(params[:gross_cpi]) || params[:gross_cpi].blank? ? -1.0 : params[:gross_cpi]&.to_d
      params[:net_cpi] = is_na?(params[:net_cpi]) || params[:net_cpi].blank? ? -1.0 : params[:net_cpi]&.to_d
      params[:margin] = is_na?(params[:margin]) || params[:margin].blank? ? nil : params[:margin]&.to_d
      params[:spend_date] = params[:spend_date].to_date
      client_paid_action = convert_paid_action_to_db(params[:client_paid_action].presence) || click_url.client_paid_action
      vendor_paid_action = convert_paid_action_to_db(params[:vendor_paid_action].presence) || click_url.vendor_paid_action
      bind_action = is_na?(params[:bind_action]) ? '' : (params[:bind_action].presence || click_url.bind_action)

      direct_spend_input = params[:direct_spend_input] == 'on'

      previous_cpi    = params[:pre_net_cpi].presence || net_cpi_by_date(params[:spend_date])
      previous_gross_cpi = params[:pre_gross_cpi].presence || gross_cpi_by_date(params[:spend_date])
      previous_margin = params[:pre_margin].presence || margin_by_date(params[:spend_date])
      pre_client_paid_action = click_url.client_paid_action_by(date: params[:spend_date])
      pre_vendor_paid_action = click_url.vendor_paid_action_by(date: params[:spend_date])
      pre_direct_spend_input = click_url.direct_spend_input_by(date: params[:spend_date])
      pre_cap_action = click_url.bind_action_by(date: params[:spend_date])

      margin = nil
      # 历史记录中如果不是arbitrage level 2, margin 不为空
      if params[:spend_date] < Date.today && !click_url.is_arbitrage_level_2?(params[:spend_date])
        margin = params[:margin]
      end

      historical_click_url_attributes = {
        net_cpi:      params[:net_cpi],
        pre_net_cpi:  previous_cpi,
        margin:       margin,
        pre_margin:   previous_margin,
        gross_cpi:    params[:gross_cpi],
        pre_gross_cpi: previous_gross_cpi,
        user_id:      user.id,
        click_url_id: click_url.id,
        client_paid_action: client_paid_action,
        vendor_paid_action: vendor_paid_action,
        pre_client_paid_action: pre_client_paid_action,
        pre_vendor_paid_action: pre_vendor_paid_action,
        cap_action: bind_action,
        pre_cap_action: pre_cap_action,
        direct_spend_input: direct_spend_input,
        pre_direct_spend_input: pre_direct_spend_input
      }
      request_attributes = {
        requested_net_cpi: params[:net_cpi],
        prev_net_cpi:      previous_cpi,
        requested_margin:  margin,
        prev_margin:       previous_margin,
        requested_gross_cpi: params[:gross_cpi],
        prev_gross_cpi:      previous_gross_cpi,
        requested_client_paid_action: client_paid_action,
        prev_client_paid_action: pre_client_paid_action,
        requested_vendor_paid_action: vendor_paid_action,
        prev_vendor_paid_action: pre_vendor_paid_action,
        user_id:           user.id,
        click_url_id:      click_url.id
      }
      click_url_history_attributes << {
        event_date: params[:spend_date],
        net_cpi: params[:net_cpi],
        margin: margin,
        gross_cpi: params[:gross_cpi],
        previous_cpi: previous_cpi,
        previous_margin: previous_margin,
        previous_gross_cpi: previous_gross_cpi,
        client_paid_action: client_paid_action,
        vendor_paid_action: vendor_paid_action,
        bind_action: bind_action,
        direct_spend_input: direct_spend_input
      }

      if rate_changed?(params[:gross_cpi], params[:net_cpi], margin, previous_gross_cpi, previous_cpi, previous_margin) || paid_action_changed?(client_paid_action, vendor_paid_action, bind_action, pre_client_paid_action, pre_vendor_paid_action, pre_cap_action)
        # 处理 相同的修改分组
        is_today = params[:spend_date] == Date.today
        key_today = is_today ? 'today' : nil
        key = [params[:gross_cpi], params[:net_cpi], params[:margin], client_paid_action, vendor_paid_action, previous_gross_cpi, previous_cpi, previous_margin, pre_client_paid_action, pre_vendor_paid_action, key_today].compact.join('__')

        if current_key == '' || current_key != key
          grouped_changes << {
            key: key,
            attrs: historical_click_url_attributes,
            request_attrs: request_attributes,
            dates: [params[:spend_date]]
          }
        else
          item = grouped_changes.select{|it| it[:key] == current_key }.last
          item[:dates] << params[:spend_date]
        end
        current_key = key
      end
    end

    request_result = []
    ActiveRecord::Base.transaction do
      save_grouped_historical_click_urls(grouped_changes)
      request_result = save_grouped_requests(grouped_changes)

      click_url_history_attributes.each do |attributes|
        if attributes[:event_date].to_date == Date.today.to_date
          Audited.audit_class.as_user(user) do
            if !attributes[:direct_spend_input] && attributes[:client_paid_action] != attributes[:vendor_paid_action]
              click_url.arbitrage_level_switch = 'true'
            end
            click_url.update!(
              net_cpi: attributes[:net_cpi],
              gross_cpi: attributes[:gross_cpi],
              margin: attributes[:margin],
              client_paid_action: attributes[:client_paid_action],
              vendor_paid_action: attributes[:vendor_paid_action],
              bind_action: attributes[:bind_action],
              direct_spend_input: attributes[:direct_spend_input],
              audit_comment: source
            )
            setting_arbitrage_level_config(click_url, user)
          end
        end
        click_url_history = ClickUrlHistory.find_by(click_url_id: click_url.id, event_date: attributes[:event_date])
        if click_url_history.blank?
          click_url_history = ClickUrlHistory.build_by(click_url, attributes[:event_date])
        end
        if rate_changed?(attributes[:gross_cpi], attributes[:net_cpi], attributes[:margin], attributes[:previous_gross_cpi], attributes[:previous_cpi], attributes[:previous_margin]) || paid_action_changed?(attributes[:client_paid_action], attributes[:vendor_paid_action], attributes[:bind_action], click_url_history&.client_paid_action, click_url_history&.vendor_paid_action, click_url_history&.bind_action) || direct_spend_input_change?(click_url_history&.direct_spend_input, attributes[:direct_spend_input])
          click_url_history.update!(
            net_cpi: attributes[:net_cpi],
            margin: attributes[:margin],
            gross_cpi: attributes[:gross_cpi],
            client_paid_action: attributes[:client_paid_action],
            vendor_paid_action: attributes[:vendor_paid_action],
            bind_action: attributes[:bind_action],
            direct_spend_input: attributes[:direct_spend_input]
          )
        end
      end
    end
    @updated = request_result.all?
  rescue ActiveRecord::RecordInvalid => e
    @updated = false
    errors.add(:invalid, e.message)
  end

  def update
    net_cpi         = is_na?(options.dig(:price, :net_cpi)) || options.dig(:price, :net_cpi).blank? ? -1.0 : options.dig(:price, :net_cpi)&.to_d
    gross_cpi       = is_na?(options.dig(:price, :gross_cpi)) || options.dig(:price, :gross_cpi).blank? ? -1.0 : options.dig(:price, :gross_cpi)&.to_d
    client_paid_action = convert_paid_action_to_db(options.dig(:price, :client_paid_action)) || click_url.client_paid_action
    vendor_paid_action = convert_paid_action_to_db(options.dig(:price, :vendor_paid_action)) || click_url.vendor_paid_action

    bind_action = is_na?(options.dig(:price, :bind_action)) ? '' : (options.dig(:price, :bind_action) || click_url.bind_action)
    direct_spend_input = options.dig(:price, :direct_spend_input) == 'on' || click_url.direct_spend_input
    effective_at    = options.dig(:price, :effective_at)&.to_datetime || Date.today.to_datetime
    source          = options.dig(:price, :source)
    proposed_change_id = options.dig(:price, :proposed_change_id)
    previous_cpi    = net_cpi_by_date(effective_at)
    previous_gross_cpi = gross_cpi_by_date(effective_at)
    previous_margin = margin_by_date(effective_at)
    pre_client_paid_action = click_url.client_paid_action_by(date: effective_at)
    pre_vendor_paid_action = click_url.vendor_paid_action_by(date: effective_at)
    pre_direct_spend_input = click_url.direct_spend_input_by(date: effective_at)
    pre_cap_action = click_url.bind_action_by(date: effective_at)

    if effective_at && effective_at < Date.today.to_datetime
      errors.add(:invalid, "The effective from date must greater than today.")
      return
    end

    event_date = Time.zone.now.to_date
    click_url_history = ClickUrlHistory.find_by(click_url_id: click_url.id, event_date: event_date)

    if rate_changed?(gross_cpi, net_cpi, nil, previous_gross_cpi, previous_cpi, previous_margin) || paid_action_changed?(client_paid_action, vendor_paid_action, bind_action, click_url_history&.client_paid_action, click_url_history&.vendor_paid_action, click_url_history&.bind_action) || direct_spend_input_change?(pre_direct_spend_input, direct_spend_input)

      if effective_at == Date.today.to_datetime
        ClickUrl.transaction do
          Audited.audit_class.as_user(user) do
            if !direct_spend_input && client_paid_action != vendor_paid_action
              click_url.arbitrage_level_switch = 'true'
            end
            click_url.update!(
              net_cpi: net_cpi,
              margin: nil,
              gross_cpi: gross_cpi,
              client_paid_action: client_paid_action,
              vendor_paid_action: vendor_paid_action,
              bind_action: bind_action,
              direct_spend_input: direct_spend_input,
              audit_comment: source
            )
            setting_arbitrage_level_config(click_url, user)
          end

          generate_historical_url(
            click_url,
            user,
            previous_cpi,
            previous_margin,
            previous_gross_cpi,
            pre_client_paid_action,
            pre_vendor_paid_action,
            pre_cap_action,
            pre_direct_spend_input
          ) if rate_changed?(gross_cpi, net_cpi, nil, previous_gross_cpi, previous_cpi, previous_margin)

          if click_url_history.blank?
            click_url_history = ClickUrlHistory.build_by(click_url, event_date)
          end
          click_url_history.update!(
            direct_spend_input: direct_spend_input,
            bind_action: bind_action,
            billing_type: click_url.billing_type,
            net_cpi: net_cpi,
            margin: nil,
            gross_cpi: gross_cpi,
            client_paid_action: client_paid_action,
            vendor_paid_action: vendor_paid_action,
          )
        end
        assign_labels_for_click_url(gross_cpi, net_cpi, 'N/A')
      end
      @updated = true
    else
      errors.add(:invalid, "You haven't changed the field of net_cpi or gross_cpi or client_paid_action or vendor_paid_action or cap_action or direct_spend_input.")
    end
  end

  def generate_historical_url(click_url, user, pre_net_cpi, pre_margin, pre_gross_cpi, pre_client_paid_action, pre_vendor_paid_action, pre_cap_action, pre_direct_spend_input)
    HistoricalClickUrl.create!(
      start_date: Date.today,
      end_date: Date.new(2030, 1, 1),
      net_cpi: click_url.net_cpi,
      margin: click_url.margin,
      gross_cpi: click_url.gross_cpi,
      click_url_id: click_url.id,
      pre_net_cpi: pre_net_cpi,
      pre_margin: pre_margin,
      pre_gross_cpi: pre_gross_cpi,
      user_id: user.id,
      client_paid_action: click_url.client_paid_action,
      vendor_paid_action: click_url.vendor_paid_action,
      pre_client_paid_action: pre_client_paid_action,
      pre_vendor_paid_action: pre_vendor_paid_action,
      cap_action: click_url.bind_action,
      direct_spend_input: click_url.direct_spend_input,
      pre_cap_action: pre_cap_action,
      pre_direct_spend_input: pre_direct_spend_input
    )
  end

  def assign_labels_for_click_url(gross_cpi, net_cpi, margin)
    click_url_extra = ClickUrlExtra.find_or_initialize_by(click_url_id: click_url.id)
    updated_labels = click_url_extra.labels
    if is_na?(gross_cpi)
      updated_labels << 'dynamic_gross_rate'
    else
      updated_labels.delete_if{|v| v == 'dynamic_gross_rate'}
    end
    if is_na?(net_cpi)
      updated_labels << 'dynamic_net_rate'
    else
      updated_labels.delete_if{|v| v == 'dynamic_net_rate'}
    end
    if is_na?(margin)
      updated_labels.delete_if{|v| v == 'fixed_margin'}
      updated_labels << 'dynamic_margin'
    else
      updated_labels.delete_if{|v| v == 'dynamic_margin'}
      updated_labels << 'fixed_margin'
    end
    click_url_extra.update(labels: updated_labels.uniq)
  end

  def rate_changed?(gross_cpi, net_cpi, margin, previous_gross_cpi, previous_cpi, previous_margin)
    !(gross_cpi == previous_gross_cpi && net_cpi == previous_cpi && margin == previous_margin)
  end

  def paid_action_changed?(client_paid_action, vendor_paid_action, bind_action, pre_client_paid_action, pre_vendor_paid_action, pre_cap_action)
    !(pre_client_paid_action == client_paid_action && pre_vendor_paid_action == vendor_paid_action && pre_cap_action == bind_action)
  end

  def direct_spend_input_change?(pre_direct_spend_input, direct_spend_input)
    pre_direct_spend_input != direct_spend_input
  end

  def convert_paid_action_to_db(paid_action)
    case paid_action
    when 'first_retained' then 'retained'
    when 'first_level' then 'level'
    when 'first_tutorial' then 'tutorial'
    when 'first_open' then 'open'
    else
      paid_action
    end
  end

  def find_history(date)
    click_url.historical_click_urls.where("start_date <= ?", date).where("end_date >= ?", date).recent.first
  end

  def gross_cpi_by_date(date)
    history = find_history(date)
    if history.present?
      history.gross_cpi
    else
      click_url[:gross_cpi]
    end
  end

  def net_cpi_by_date(date)
    history = find_history(date)
    if history.present?
      history.net_cpi
    else
      click_url[:net_cpi]
    end
  end

  def margin_by_date(date)
    history = find_history(date)
    if history.present?
      history.margin
    else
      click_url.margin
    end
  end

  def save_grouped_requests(list)
    return [true] if list.blank?
    list.map do |item|
      sorted_dates = item[:dates].sort.chunk_while{|i, j| i + 1.day == j}
      sorted_dates.each do |d|
        start_date = d[0]
        end_date = d[-1]

        obj = {}.merge(item[:request_attrs])
        obj[:start_date] = start_date
        obj[:end_date] = end_date

        next if start_date.to_s == end_date.to_s && start_date.to_s == Date.today.to_s

        HistoricalClickUrlChangeRequest.new(obj).save!
      end
    end
  end

  def save_grouped_historical_click_urls(list)
    return if list.blank?
    tips = []
    list.each do |item|
      sorted_dates = item[:dates].sort.chunk_while{|i, j| i + 1.day == j}
      sorted_dates.each do |d|
        start_date = d[0]
        end_date = d[-1] == Date.today ? Date.new(2030, 1, 1) : d[-1]

        obj = {}.merge(item[:attrs])
        obj[:start_date] = start_date
        obj[:end_date] = end_date
        obj[:validate_date] = start_date == end_date && start_date != Date.today

        pass, tip = valid_row(obj)
        tips << tip if !pass
        HistoricalClickUrl.new(obj).save!
      end
    end

    content = tips.join("\n")
    SlackService.send_notification_to_channel("#{self.class.name}\n#{user&.name} 在修改 ClickUrl #{click_url.id} 时，以下修改不符合计算规则，请检查:\n#{content}", :mighty) if tips.present?

  end

  def valid_row(obj)
    ## 设置为 N/A 时，不检查是否符合计算规则
    if obj[:gross_cpi] == -1 || obj[:net_cpi] == -1 || obj[:margin].nil?
      return [true, nil]
    end
    margin_calculate = NetSpend.calculated_margin(obj[:gross_cpi], obj[:net_cpi])
    is_same = (obj[:margin].to_f.round(5) - margin_calculate.to_f.round(5)).abs <= 0.1
    tip = is_same ? nil : "#{obj[:start_date]} - #{obj[:end_date]}: gross_rate: #{obj[:gross_cpi]}, net_rate: #{obj[:net_cpi]}, margin: #{obj[:margin]}%, margin_calculate: #{margin_calculate}%"
    [is_same, tip]
  end
end
