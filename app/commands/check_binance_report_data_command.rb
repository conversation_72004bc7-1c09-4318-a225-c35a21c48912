class CheckBinanceReportDataCommand
  prepend SimpleCommand

  include ActionView::Helpers::NumberHelper
  include IronSourceApiConcern
  include IronSourceAuraApiConcern
  include QuoraApiConcern
  include SmadexApiConcern

  attr_reader :date, :smadex_report_id, :smadex_reports

  HEADERS = [
    "Date",
    "Click URL",
    "Campagin Name",
    "Partner",
    "Partner Dashboard Spend",
    "SPEND Gross"
  ].freeze

  def initialize(date, smadex_report_id)
    @date = date.to_date
    @smadex_report_id = smadex_report_id
    @smadex_reports = get_smadex_reports
  end

  def call
    report_datas = []
    click_urls = ClickUrl.includes(:campaign,:vendor).where(id: click_url_ids)
    af_datas = get_data_from_appsflyer

    click_urls.each do |click_url|
      af_data = af_datas.find {|c|c['click_url_id'] == click_url.id && c['event_date'] == date.to_s}.to_h

      if click_url.vendor_id.in?([497, 391, 130])  # InMobi DSP, Smadex, Applovin 的net和gross取值于direct spend
        gross_spend, net_spend = get_direct_spend(click_url, date)
      elsif click_url.id == 21124 # hot fix for click_url_id 12214, 等自动化 job 完成后，再来调整
        gross_spend, net_spend = get_direct_spend(click_url, date)
      else
        net_spend = get_net_spend(click_url, date, af_data)
        gross_spend = get_gross_spend(click_url, date, net_spend, af_data)
      end

      next if af_data.blank? && net_spend == 0 && gross_spend == 0

      report_datas << HEADERS.zip([
        date.to_date.strftime("%m/%d/%Y"),
        click_url.id.to_s,
        click_url.campaign.name,
        click_url.vendor.vendor_name,
        net_spend.round(2),
        gross_spend.round(2)
      ]).to_h
    end

    report_datas
  end

  def get_gross_spend(click_url, date, net_spend, af_data)
    gross = if click_url.vendor_id.in?([454, 447, 411, 408])
      gross_cpi = click_url.gross_cpi_by_date(date)
      af_data['af_first_trade'].to_i * gross_cpi
    elsif click_url.vendor_id == 358
      gross_cpi = click_url.gross_cpi_by_date(date)
      af_data['registration_verified'].to_i * gross_cpi
    else
      margin = click_url.margin_by_date(date)
      net_spend/(1 - margin.to_f / 100)
    end

    gross = 500 if click_url.vendor_id == 130 && gross > 500 && date.to_date <= '2025-04-04'.to_date
    gross
  end

  def get_net_spend(click_url, date, af_data)
    net_spend = if click_url.vendor_id == 192
      get_ironsrce_net_spend(click_url, date)
    elsif click_url.vendor_id == 288
      get_ironsrce_aura_net_spend(click_url, date)
    elsif click_url.vendor_id == 407
      get_quora_net_spend(click_url, date)
    elsif click_url.vendor_id == 391
      get_smadex_net_spend(click_url, date)
    elsif click_url.vendor_id == 358
      net_cpi = click_url.net_cpi_by_date(date)
      af_data['registration_verified'].to_i * net_cpi
    elsif [454, 447, 411, 408].include?(click_url.vendor_id)
      net_cpi = click_url.net_cpi_by_date(date)
      af_data['af_first_trade'].to_i * net_cpi
    else
      gross, net = get_direct_spend(click_url, date)
      net
    end

    net_spend = 500 if click_url.vendor_id == 130 && gross > 500 && date.to_date <= '2025-04-04'.to_date
    net_spend
  end

  def get_ironsrce_aura_net_spend(click_url, date)
    ironsrce_aura_reports.select {|c|
      c['day'] == date.to_s && c['campaign_name'].include?(click_url.campaign.name)
    }.sum{|c|c['spend']}
  end

  def get_ironsrce_net_spend(click_url, date)
    iron_source_mappings = get_iron_source_mappings
    ironsrce_reports.select {|c|
      c['date'].to_date.to_s == date.to_s && (c['campaignName'].include?(click_url.campaign.name) || iron_source_mappings[click_url.id].include?(c['campaignId'].to_s))
    }.sum{|c|c['spend']}
  end

  def get_quora_net_spend(click_url, date)
    quora_reports.select {|c|
      c['campaignName'].include?(click_url.campaign.name)
    }.sum{|c|c['spend']}.to_f/10000.0
  end

  def get_smadex_net_spend(click_url, date)
    smadex_reports[[click_url.id, date.to_s]].to_f
  end

  def get_direct_spend(click_url, date)
    direct_spend = NetSpend.find_by(click_url_id: click_url.id, spend_date: date)
    return [0, 0] unless direct_spend
    [direct_spend.gross_spend.to_f, direct_spend.net_spend.to_f]
  end

  def ironsrce_aura_reports
    @ironsrce_aura_reports ||= fetch_iron_source_aura_reports(date, date)
  end

  def ironsrce_reports
    @ironsrce_reports ||= fetch_reports_by_bundleids(date, date, ['com.binance.dev', 'com.czzhao.binance'])
  end

  def quora_reports
    @quora_reports ||= fetch_binance_quora_reports(date)
  end

  def get_smadex_reports
    smadex_report_url = SmadexApiService.new.download_url(smadex_report_id)
    csv_reports = SmadexApiService.new.smadex_report_csv(smadex_report_url) || []

    smadex_spend_hash = Hash.new(0)
    csv_reports.select { |item| item["campaign_name"].downcase.include?("binance") }.each do |item|
      click_url_id = SmadexCampaignMapping.find_by(smadex_multi_campaign_id: item["multi_campaign_id"].to_s)&.click_url_id
      next if click_url_id.blank?
      smadex_spend_hash[[click_url_id, item['date_time'].to_date.to_s]] = item["media_spend"].to_f + smadex_spend_hash[[click_url_id, item['date_time'].to_date.to_s]].to_f
    end

    smadex_spend_hash
  end

  def get_iron_source_mappings
    @iron_source_mappings ||= IronsourceCampaignMapping.where(click_url_id: click_url_ids).pluck(:click_url_id, :ironsource_campaign_id).group_by(&:first).transform_values { |arr| arr.map(&:last) }
  end

  def get_data_from_appsflyer
    sql = <<-SQL
        SELECT
          cuh.event_date,
          cuh.click_url_id,
          af_report.af_app_id,
          sum(af_report.impression) as impression,
          sum(af_report.click) as click,
          sum(af_report.install) as install,
          sum(af_event.registration) as registration_verified,
          sum(af_event.purchase) as af_first_trade
        FROM click_url_histories as cuh
        left join appsflyer_agency_reports as af_report on af_report.click_url_id = cuh.click_url_id and af_report.date = cuh.event_date
        left join appsflyer_in_app_events as af_event on af_event.click_url_id = cuh.click_url_id and af_event.date = cuh.event_date
        WHERE cuh.event_date = '#{date}'
        AND cuh.click_url_id IN (#{click_url_ids.join(',')})
        GROUP by 1, 2, 3
        HAVING SUM(af_report.impression) > 0 OR SUM(af_report.click) > 0 OR SUM(af_report.install) > 0 OR SUM(af_event.registration) > 0 OR SUM(af_event.purchase) > 0
        ;
      SQL
    AppsflyerAgencyReport.connection.execute(sql).to_a
  end

  def click_url_ids
    ClickUrl.includes(:campaign).where(campaigns: {client_id: Client::BINANCE_ID}).where.not(vendor_id: test_vendor_ids).where(direct_spend_input: true).live.pluck(:id)
  end

  def test_vendor_ids
    @test_vendor_ids ||= Vendor.where(for_test: true).pluck(:id)
  end
end
