class CheckAppsflyerInAppEventsDifferenceCommand
  prepend SimpleCommand

  include AppsflyerAgencyMappings
  include AppsflyerSubParam5Checker
  include AppsflyerEventDeduplication
  include StashAppsflyerEventDeduplication
  include AppsflyerRecordsConcern

  attr_reader :af_app_id, :client_id, :start_date, :end_date

  def initialize(af_app_id:, client_id:, start_date:, end_date:)
    @af_app_id = af_app_id
    @client_id = client_id
    @start_date = start_date
    @end_date = end_date
  end

  def call
    results = []
    rows = []
    @not_matched_sub5 = []

    job_stat_click_url_ids = DirectSpendJobStat.where(status: 'live').pluck(:click_url_ids).flatten.uniq

    service = AppsflyerApiServiceV2.new(af_app_id: af_app_id, client_id: client_id, start_date: start_date, end_date: end_date)
    service.in_app_events_report(via_api: true) do |temple_file|
      return [] if temple_file.nil?

      records = CSV.foreach(temple_file.path, headers: true)
      records = daily_deduplicate_events(af_app_id, records) if [Client::REALTOR_ID, Client::STASH_ID].include?(client_id)
      records = filter_skan_records(af_app_id, records)

      records.each  do |record|
        record = record.to_h
        event_name = record['Event Name']&.downcase
        event_value = record['Event Value']
        click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
        next if click_url_id.blank?

        click_url = Rails.cache.fetch("click_url_from_af_sub5_#{af_app_id}_#{click_url_id}", expires_in: 5.minutes) do
          ClickUrl.find_by(id: click_url_id)
        end

        next if click_url.blank?
        next if job_stat_click_url_ids.exclude?(click_url_id.to_i) # skip if click url has no direct spend job
        next if check_af_app_id(click_url, af_app_id)

        campaign_id = click_url&.campaign_id
        row = rows.find { |c| c.dig(:campaign_id) == campaign_id }
        if row.present?
          row[:purchase] = get_in_app_events_v2("P", event_name, event_value, af_app_id, row[:purchase])
          row[:registration] = get_in_app_events_v2("REG", event_name, event_value, af_app_id, row[:registration])
          row[:level] = get_in_app_events_v2("L", event_name, event_value, af_app_id, row[:level])
          row[:open] = get_in_app_events_v2("O", event_name, event_value, af_app_id, row[:open])
          row[:retained] = get_in_app_events_v2("R", event_name, event_value, af_app_id, row[:retained])
          row[:tutorial] = get_in_app_events_v2("T", event_name, event_value, af_app_id, row[:tutorial])
          row[:all_event_a] = get_in_app_events_v2("AA", event_name, event_value, af_app_id, row[:all_event_a])
        else
          rows << {
            campaign_id: campaign_id,
            purchase: get_in_app_events_v2("P", event_name, event_value, af_app_id, 0),
            registration: get_in_app_events_v2("REG", event_name, event_value, af_app_id, 0),
            level: get_in_app_events_v2("L", event_name, event_value, af_app_id, 0),
            open: get_in_app_events_v2("O", event_name, event_value, af_app_id, 0),
            retained: get_in_app_events_v2("R", event_name, event_value, af_app_id, 0),
            tutorial: get_in_app_events_v2("T", event_name, event_value, af_app_id, 0),
            all_event_a: get_in_app_events_v2("AA", event_name, event_value, af_app_id, 0)
          }.with_indifferent_access
        end
      end
    end

    send_not_matched_sub5_notice

    rows.each do |row|
      campaign_id = row[:campaign_id].to_i

      sql = ActiveRecord::Base.sanitize_sql([<<-SQL, start_date, end_date, af_app_id, campaign_id, job_stat_click_url_ids])
        SELECT campaign_id, sum(purchase) as purchase, sum(registration) as registration, sum(level) as level, sum(open) as open, sum(retained) as retained, sum(tutorial) as tutorial, sum(all_event_a) as all_event_a
        FROM appsflyer_in_app_events
        WHERE date >= ? AND date <= ?
          AND af_app_id = ?
          AND campaign_id = ?
          AND click_url_id in (?)
        group by 1
      SQL
      db = AppsflyerInAppEvent.connection.execute(sql).first.to_h.with_indifferent_access

      if db != row
        results << {
          af_app_id: af_app_id,
          campaign_id: campaign_id,
          purchase: {
            current: row[:purchase],
            history: db[:purchase]
          },
          registration: {
            current: row[:registration],
            history: db[:registration]
          },
          level: {
            current: row[:level],
            history: db[:level]
          },
          open: {
            current: row[:open],
            history: db[:open]
          },
          retained: {
            current: row[:retained],
            history: db[:retained]
          },
          tutorial: {
            current: row[:tutorial],
            history: db[:tutorial]
          },
          all_event_a: {
            current: row[:all_event_a],
            history: db[:all_event_a]
          }
        }
      end
    rescue ActiveRecord::RecordNotFound => e
      Sentry.capture_exception(e, extra: { af_app_id: af_app_id, start_date: start_date, end_date: end_date })
      next
    end

    results
  end

  def daily_deduplicate_events(af_app_id, records)
    new_records = []
    records.group_by { |r| r['Event Time'].to_date }.each do |date, rows|
      new_rows = if Client::REALTOR_ID == client_id
        check_realtor_deduplicate_events(client_id, af_app_id, date, rows)
      elsif Client::STASH_ID == client_id
        check_stash_deduplicate_events(client_id, date, rows)
      else
        rows
      end
      new_records.concat(new_rows)
    end

    new_records
  end
end
