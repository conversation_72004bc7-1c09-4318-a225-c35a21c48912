class VendorIdExtractionCommand
  prepend SimpleCommand

  attr_reader :vendor_collection, :vendor_owner_id, :client_id, :options, :excluded_vendor_id

  def initialize(options = {})
    @vendor_collection  = options[:vendor_collection]
    @vendor_owner_id    = options[:vendor_owner_id]
    @client_id          = options[:client_id]
    @excluded_vendor_id = options[:excluded_vendor_id]
    @options            = options
  end

  def call
    ids = vendor_id  & client_vendor_ids & collection_vendor_id & owner_vendor_id
    unless options[:develop]
      ids = Vendor.where(id: ids, for_test: false).pluck(:id)
    end
    Array(ids).delete_if { |item| item.to_s == excluded_vendor_id.to_s }
  end

  private

  def vendor_id
    return Vendor.cached.keys unless options[:vendor_id].present?
    [options[:vendor_id]].flatten.map(&:to_i)
  end

  def client_vendor_ids
    return [] if client_id == []
    return Vendor.cached.keys if client_id.blank?
    Client.where(id: client_id).map { |c| c.vendor_ids }.flatten.uniq
  end

  def collection_vendor_id
    return Vendor.cached.keys unless vendor_collection.present?
    Vendor.where(vendor_type: vendor_collection).pluck(:id)
  end

  def owner_vendor_id
    return Vendor.cached.keys unless vendor_owner_id.present?
    Vendor.where(business_development: vendor_owner_id).pluck(:id)
  end

  def client
    return unless client_id.present?
    @client ||= Client.find_by(id: client_id)
  end
end
