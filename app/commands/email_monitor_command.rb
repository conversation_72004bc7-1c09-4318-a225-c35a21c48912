class EmailMonitorCommand
  prepend SimpleCommand

  attr_reader :given_date, :subject, :job_name, :args, :rule

  def initialize(date: , subject: , job_name: ,args: nil, rule: nil)
    @given_date = date
    @subject = subject
    @job_name = job_name
    @args = args
    @rule = rule
  end

  def call
    if rule == 'per_hour_once'
      return check_per_hour_job_result
    end

    if not_triggered?
      return result_with(success: false, triggered: false)
    end

    if sent_failed?
      return result_with(success: false, triggered: true, record: failed_record)
    end

    result_with(success: true, triggered: true)
  end

  private

  SUCCESS_HTTP_CODE = [200, 202].freeze

  def warning?
    not_finished_job? && sent_failed?
  end

  def triggered?
    current_job_logs.any? || current_notifications.present?
  end

  def not_triggered?
    !triggered?
  end

  def sent_failed?
    !sent_success?
  end

  def sent_success?
    current_notifications.any? { |item| item.code.to_i.in?(SUCCESS_HTTP_CODE) || HashWithIndifferentAccess.new(item.memo)['category'] == 'success' }
  end

  def failed_record
    current_notifications.select { |item| !item.code.to_i.in?(SUCCESS_HTTP_CODE) || HashWithIndifferentAccess.new(item.memo)['category'] == 'success' }.last
  end

  def not_finished_job?
    !finished_job?
  end

  def finished_job?
    finished_job.present? || sent_success?
  end

  def finished_job
    @finished_job ||= current_job_logs.any? && current_job_logs[0].dig("_source", "error_messages").blank?
  end

  def current_job_logs
    @current_job_logs ||=
      begin
        if job_name.present?
          WorkerLogsIndex.get_job_by(job_name: job_name, date: given_date, args: args).dig("hits", "hits")
        else
          []
        end
      end
  end

  def current_notifications
    @current_notifications ||= begin
      MailerLog
        .by_date(given_date)
        .where("LOWER(subject) LIKE LOWER(?)", "%#{subject}%")
        .order(updated_at: :desc)
    end
  end

  def result_with(options)
    last_start_time, last_elapsed =
      if job_name.present?
        get_longest_job_start_time_and_elapsed
      else
        ['-', '-']
      end
    options.reverse_merge!(date: given_date, subject: subject, finished_job: finished_job?, warning: warning?, last_start_time: last_start_time, last_elapsed: last_elapsed)
    OpenStruct.new(options)
  end

  def get_longest_job_start_time_and_elapsed
    response = WorkerLogsIndex.get_longest_elapsed_job(job_name: job_name, date: given_date, args: args).dig("hits", "hits")
    if response.empty?
      ['-', '-']
    else
      [response[0].dig("_source", "start_time"), response[0].dig("_source", "elapsed")]
    end
  end

  def check_per_hour_job_result
    all_finished = true
    all_triggered = true
    hits ||= WorkerLogsIndex.data_range_list_jobs(job_name: job_name, start_date: given_date, end_date: (given_date.to_date + 1.day), size: 100)["hits"]

    hours = (0..23).map do |hour|
      hour = hour < 10 ? "0#{hour}" : hour
      log = hits["hits"].find { |job| job.dig("_source", "args")&.include?("#{given_date} #{hour}:30") }
      log.dig("_source", "start_time").to_datetime.hour if log
    end.compact

    if hits["total"] < 24
      all_finished = false
      all_triggered = false
    else
      hits["hits"].each do |job|
        break all_finished = false if job.dig("_source", "error_messages").present?
      end
      all_triggered = false if ((0..23).to_a - hours).present?
    end

    result_with(success: false, triggered: all_triggered, finished_job: all_finished, warning: !(all_finished && all_triggered) )
  end
end
