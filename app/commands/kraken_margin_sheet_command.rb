# frezen_string_literal: true

require 'googleauth'
require 'google/apis/sheets_v4'

class KrakenMarginSheetCommand
  include ReportConfigConcern
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1GabrpHICkH9WQ8oTtAxHDuzCv2IRfRarbI2h7mTL9Vs'.freeze

  attr_reader :date, :campaign_ids

  def initialize(date)
    @date = date.to_date
    @campaign_ids = Campaign.where(client_id: Client::KRAKEN_ID).pluck(:id)
  end

  def call
    service = Google::Apis::SheetsV4::SheetsService.new
    service.authorization = authorizer

    range_name = "Margin Tracking!A:A"
    response = service.get_spreadsheet_values(SPREADSHEET_ID, range_name)

    # 找到需要更新的序号
    date_str = @date.strftime("%-m/%-d/%y")
    date_values = response.values.flatten
    row_indexes = date_values.each_index.select{|index| date_str == date_values[index]}

    value_range_object = {
      major_dimension: "ROWS",
      values: data
    }
    # 直接加到sheet后面
    if row_indexes.blank?
      service.update_spreadsheet_value(SPREADSHEET_ID, "Margin Tracking!A#{date_values.length+1}:I#{date_values.length+data.size}", value_range_object, value_input_option: 'USER_ENTERED')
    else # 更新原来的行
      if row_indexes.length != data.length
        send_report_config_job_slack_alert("#{self.class.name}: Margin Tracking #{date} 的行数和数据行数不一致，不能直接覆盖，请检查google sheet.", :mighty)
        return
      end

      service.update_spreadsheet_value(SPREADSHEET_ID, "Margin Tracking!A#{row_indexes[0]}:I#{row_indexes[-1]}", value_range_object, value_input_option: 'USER_ENTERED')
    end
  end

  def data
    @data ||= NetSpend.where(campaign_id: campaign_ids, spend_date: date).map do |ns|
      gross_spend = ns.gross_spend.to_f
      net_spend = ns.net_spend.to_f
      profit = (gross_spend - net_spend).round(2)
      margin = gross_spend.zero? ? 'NA' : number_to_percentage(profit * 100.0 / gross_spend, precision: 0)
      month = date.strftime("%B %Y")
      [ns.spend_date.strftime("%-m/%-d/%y"), ns.campaign&.name, ns.vendor&.vendor_name, ns.client_paid_action_count, gross_spend, net_spend, profit, margin, month]
    end.sort_by{|r| r[0]}.reverse
  end

  private

  def authorizer
    @authorizer ||= begin
      authorizer = Google::Auth::ServiceAccountCredentials.make_creds(
        json_key_io: File.open(client_secrets_file_path),
        scope: SCOPE
      )
      authorizer.fetch_access_token!
      authorizer
    end
  end

  def client_secrets_file_path
    File.join(current_path, "config", 'feedmob-4bbd49e741a5.json')
  end

  def current_path
    Dir["#{Rails.root}"]
  end
end
