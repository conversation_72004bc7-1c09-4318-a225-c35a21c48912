class MakegoodsConversionRecordsCommand
  prepend SimpleCommand

  CSV_HEADERS = {
    "CONVERSION ID" => :conversion_id,
    "CLICK URL ID"  => :click_url_id,
    "EVENT TIME"    => :event_time,
    "STATUS"        => :status,
    "TRACK TYPE"    => :track_type,
    "REPEATED"      => :repeated,
  }

  attr_reader :cr, :mcr, :postback, :csv_log, :status, :click_url, :target_url, :options

  def initialize(csv_rows)
    @csv_rows = csv_rows
    @updated_crs = []
  end

  def call
    @csv_rows.each do |row|
      row = CSV_HEADERS.map{|k, v| [v, row[k]]}.to_h
      next if row[:status] != 'normal' && row[:repeated] == true

      @updated_crs << update_conversion_record(row)
    end
    @updated_crs
  end

  private

  def update_conversion_record(row)
    new_tags = ['makegood_a']
    makegood_process_time = Time.now
    event_date = row[:event_time].to_date
    uuids = []

    crs = NewConversionRecord.by_date(event_date).where(conversion_id: row[:conversion_id], status: row[:status], track_type: row[:track_type], repeated: row[:repeated])
    crs.each do |cr|
      cr.update!(status: :makegood_a, memo: cr.memo.merge(makegoods: true, makegood_process_time: makegood_process_time, tags: new_tags, origin_status: row[:status], origin_tags: cr.memo.dig("tags")))
    end

    daily_crs = NewDailyConversionRecord.table_by_date(event_date).by_date(event_date).where(conversion_id: row[:conversion_id], click_id: row[:click_url_id], status: row[:status], track_type: row[:track_type], repeated: row[:repeated])
    daily_crs.each do |daily_cr|
      uuids << daily_cr.uuid
      info = daily_cr.info
      daily_cr.update!(status: :makegood_a, memo: daily_cr.memo.merge(makegoods: true, makegood_process_time: makegood_process_time, tags: new_tags, origin_status: row[:status], origin_tags: daily_cr.memo.dig("tags")))

      info.update!(memo: info.memo.merge(makegoods: true, makegood_process_time: makegood_process_time, tags: new_tags, origin_tags: info.memo.dig("tags")))

      body = [{ index: { _id: daily_cr.uuid, data: daily_cr.as_indexed_json}, create: {_id: daily_cr.uuid, data: daily_cr.as_indexed_json}}]
      ElasticsearchClient.conversion_record.bulk(index: 'conversion_records', type: 'conversion_record', body: body, pipeline: 'monthlyindex')
    end

    ConversionRecordRedshift.connection.execute(
      <<-EOQ
        UPDATE conversion_records
        SET status = 'makegood_a'
        WHERE event_time >= '#{event_date}' AND event_time < '#{event_date + 1.day}'
          AND status = '#{row[:status]}'
          AND uuid IN ('#{uuids.join("','")}')
          AND track_type = '#{row[:track_type]}'
      EOQ
    )
    [row[:click_url_id], event_date, uuids]
  end
end
