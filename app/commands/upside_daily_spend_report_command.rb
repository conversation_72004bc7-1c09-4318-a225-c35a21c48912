# frezen_string_literal: true

require 'googleauth'
require 'google/apis/sheets_v4'

class UpsideDailySpendReportCommand
  include ReportConfigConcern
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = if Rails.env.production?
                     '1Lfed51wXIL3e3tDOjinridQia5gzul8fTmFxhpOVUG4'
                   else
                     '1C0uo5M2_WZMqU__NbmuRTppiMIzNP1Mf8Ib-vDzvm4A'
                   end

  def initialize(date)
    @date = date.to_date
    @google_sheets_service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    service = Google::Apis::SheetsV4::SheetsService.new
    service.authorization = authorizer

    header_range = 'Spend Sheet!1:1'
    response = service.get_spreadsheet_values(SPREADSHEET_ID, header_range)
    headers = response.values.first if response.values

    total = data.values.map(&:to_f).sum
    unless total > 0
      send_report_config_job_slack_alert("#{self.class.name}: 上传 Upside client facing report 时, #{@date} 当天没有获取到 spend, 需要检查!", :star)
    end

    range_name = "Spend Sheet!B:B"
    response = service.get_spreadsheet_values(SPREADSHEET_ID, range_name)
    if !response.values
      send_report_config_job_slack_alert("#{self.class.name}:  上传 Upside client facing report 时, 在线表格内容为空，需要检查!", :star)
      return
    end

    row_index = get_date_row_index(response) + 1
    @google_sheets_service.expand_sheet_rows("Spend Sheet", row_index)

    data_to_fill = headers.map do |v|
      case v
      when 'Week'
        "=(B#{row_index}-weekday(B#{row_index},3))"
      when 'Day'
        @date.strftime("%-m/%-d/%y")
      when 'Total Spend'
        "=(SUM(D#{row_index}:BE#{row_index}))"
      else
        number_to_currency(data[v.downcase] || 0)
      end
    end

    range_name = "Spend Sheet!A#{row_index}"

    service.update_spreadsheet_value(SPREADSHEET_ID, range_name, {
      values: [data_to_fill]
    }, value_input_option: 'USER_ENTERED')
  end

  def data
    @data ||= begin
      results = ConversionRecordRedshift.connection.query(sql).map{|r| [[r[0], r[1]], r[2]]}.to_h
      net_spends_res = ClickUrl.connection.query(net_spends_sql).map{|r| [[r[0], r[1]], r[2]]}.to_h
      (results.keys + net_spends_res.keys).uniq.each_with_object({}) do |key, hash|
        spend = results[key].to_f + net_spends_res[key].to_f
        if (key[1] == 'pinsight' && key[0].include?('_Push'))
          hash["pinsight push spend"] ||= 0
          hash["pinsight push spend"] += spend
        elsif key[1] == 'ironsource'
          if key[0] =~ /_iOS_/
            hash["ironsource iOS"] ||= 0
            hash["ironsource iOS"] += spend
          elsif key[0] =~ /_Android_/
            hash["ironsource android"] ||= 0
            hash["ironsource android"] += spend
          else
            send_report_config_job_slack_alert("#{self.class.name}: Upside<>ironSource campaign #{result[0]} 无法判断是Android还是iOS.", :mighty)
          end
        elsif key[1] == 'match group'
          if key[0] =~ /_iOS_/
            hash["matchgroup ios"] ||= 0
            hash["matchgroup ios"] += spend
          elsif key[0] =~ /_Android_/
            hash["matchgroup android"] ||= 0
            hash["matchgroup android"] += spend
          else
            send_report_config_job_slack_alert("#{self.class.name}: Upside<>MatchGroup campaign #{result[0]} 无法判断是Android还是iOS.", :mighty)
          end
        else
          hash[key[1].downcase] ||= 0
          hash[key[1].downcase] += spend
        end
      end
    end
  end

  private

  def get_date_row_index(column_b_data)
    rows = column_b_data.values.flatten
    row_index = rows.index(@date.strftime("%-m/%-d/%y"))

    if row_index.nil?
      row_index = rows.index((@date - 1.day).strftime("%-m/%-d/%y"))
      row_index = row_index.to_i + 1 unless row_index.nil?
    end

    if row_index.nil?
      last_date = rows.drop(1).max { |r| Date.strptime(r, '%-m/%-d/%y') }
      row_index = rows.index(last_date.strftime("%-m/%-d/%y")) + 1
      send_report_config_job_slack_alert("#{self.class.name}: 上传 Upside client facing report 时, 没有在 B 列获取到 '#{@date}' 的索引号, 需要检查！", :star)
    end

    row_index
  end

  def sql
    @sql ||= <<-SQL
      SELECT sr.campaign_name,
             lower(#{vendor_name_condition('sr', 'sr')}) AS vendor,
             sum(CASE WHEN info.direct_spend_input THEN 0 ELSE spend_cents END)/100.0 AS spend
      FROM v4_stat_records sr
      LEFT JOIN click_url_infos info ON info.click_url_id = sr.click_url_id AND info.event_date = sr.report_date
      WHERE sr.client_id = #{Client::UPSIDE_ID}
        AND sr.report_date = '#{@date}'
        AND sr.status = 'normal'
      GROUP BY 1,2;
    SQL
  end

  def net_spends_sql
    @net_spends_sql ||= <<-SQL
      SELECT
        c.name                        AS campaign_name,
        lower(#{vendor_name_condition('ns', 'v')}) AS vendor,
        SUM(gross_spend_cents)/100.0  AS spend
      FROM net_spends ns
      LEFT JOIN campaigns c on c.id = ns.campaign_id
      LEFT JOIN vendors v on v.id = ns.vendor_id
      WHERE c.client_id = #{Client::UPSIDE_ID}
        AND spend_date = '#{@date}'
      GROUP BY 1,2
    SQL
  end

  def authorizer
    @authorizer ||= begin
      authorizer = Google::Auth::ServiceAccountCredentials.make_creds(
        json_key_io: File.open(client_secrets_file_path),
        scope: SCOPE
      )
      authorizer.fetch_access_token!
      authorizer
    end
  end

  def client_secrets_file_path
    File.join(current_path, "config", 'feedmob-4bbd49e741a5.json')
  end

  def current_path
    Dir["#{Rails.root}"]
  end

  def upside_report_column_mapping
    @upside_report_column_mapping ||= TrackingConfig.find_by(config_type: 'system', name: 'UPSIDE_SPEND_REPORT_COLUMN_MAPPING')&.value || {}
  end

  def vendor_name_condition(table_alias, vendor_name_table)
    conditions = upside_report_column_mapping.map do |click_url_ids, value|
      "WHEN #{table_alias}.click_url_id IN (#{click_url_ids}) THEN '#{value}'"
    end
    if conditions.blank?
      "#{vendor_name_table}.vendor_name"
    else
      "CASE #{conditions.join(' ')} ELSE #{vendor_name_table}.vendor_name END"
    end
  end
end
