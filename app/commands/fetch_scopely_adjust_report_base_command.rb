# frezen_string_literal: true
class FetchScopelyAdjustReportBaseCommand
  prepend SimpleCommand
  include AdjustApiConcern

  API_HOST = 'https://dash.adjust.com'.freeze
  ACCESS_TOKEN = ENV['SCOPELY_ADJUST_API_ACCESS_TOKEN'].freeze
  SCOPELY_ADJUST_ACCOUNT_ID = '1091'.freeze

  def fetch_scopely_adjust_data(start_date, end_date, include_events = false)
    base_metrics = %w[impressions clicks installs]
    base_metrics = base_metrics + event_mappings.map { |m| m.events_in_json.values.flatten.uniq }.flatten.uniq if include_events
    params = {
      date_period: "#{start_date}:#{end_date}",
      dimensions: 'day,store_id,app,os_name,partner_name,campaign,campaign_id_network,campaign_network',
      metrics: base_metrics.join(','),
      attribution_type: 'all',
      attribution_source: 'first',
      utc_offset: '+00:00',
      os_names: 'ios,android',
      channel__in: channels.join(",")
    }
    res = Retriable.retriable(base_interval: 3) do
      HTTParty.get(
        "#{API_HOST}/control-center/reports-service/csv_report?#{params.to_query}",
        headers: { 'Authorization'=> "Bearer #{ACCESS_TOKEN}", 'x-account-id' => SCOPELY_ADJUST_ACCOUNT_ID })
    end

    csv_data = res.body.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding("UTF-8"), '')
    CSV.parse(csv_data, headers: true)
  end

  def fetch_scopely_adjust_all_data(start_date, end_date)
    base_metrics = %w[impressions clicks installs]
    params = {
      date_period: "#{start_date}:#{end_date}",
      dimensions: 'day,store_id,app,os_name,partner_name,campaign,campaign_id_network,campaign_network',
      metrics: base_metrics.join(','),
      attribution_type: 'all',
      attribution_source: 'first',
      utc_offset: '+00:00',
      os_names: 'ios,android'
    }
    res = Retriable.retriable(base_interval: 3) do
      HTTParty.get(
        "#{API_HOST}/control-center/reports-service/csv_report?#{params.to_query}",
        headers: { 'Authorization'=> "Bearer #{ACCESS_TOKEN}", 'x-account-id' => SCOPELY_ADJUST_ACCOUNT_ID })
    end

    csv_data = res.body.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding("UTF-8"), '')
    CSV.parse(csv_data, headers: true)
  end

  def event_mappings
    @event_mappings ||= AdjustEventMapping.where(client_id: Client::SCOPELY_ID).to_a
  end

  def channels
    @channels ||= AdjustCampaignMapping.includes(:vendor).where(client_id: Client::SCOPELY_ID).where(vendors: {for_test: false}).pluck(:channel).uniq
  end

end
