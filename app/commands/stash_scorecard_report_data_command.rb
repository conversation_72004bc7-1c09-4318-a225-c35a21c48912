class StashScorecardReportDataCommand
  prepend SimpleCommand

  include ActionView::Helpers::NumberHelper

  attr_reader :start_date, :end_date

  HEADERS = [
    "PARTNER",
    "CAMPAIGN",
    "CHANNEL",
    "CLICKS",
    "INSTALLS",
    "SPEND",
    "eCPI",
    "CVR",
    "Account Created",
    "Account Created CVR",
    "User Chargeable",
    "User Chargeable CPA",
    "Deposit Initiated",
    "Deposit Initiated eCPA",
    "Deposit Complete",
    "Deposit Complete eCPA"
  ].freeze

  def initialize(start_date, end_date)
    @start_date = start_date
    @end_date = end_date
  end

  def call
    fe_datas = get_data_from_fe
    report_data = []
    total_click = total_install = total_post_tutorial = total_post_level = total_post_first_purchase = total_all_event_a = total_gross_spend = 0
    fe_datas.each do |fe_record|
      campaign_name = fe_record['campaign_name']
      vendor_name = fe_record['vendor_name']
      click = fe_record.present? ? fe_record["click_count"].to_i : 0
      install = fe_record.present? ? fe_record["install_count"].to_i : 0
      post_tutorial = fe_record.present? ? fe_record["post_tutorial_count"].to_i : 0
      post_level = fe_record.present? ? fe_record["post_level_count"].to_i : 0
      post_first_purchase = fe_record.present? ? fe_record["post_first_purchase_count"].to_i : 0
      all_event_a = fe_record.present? ? fe_record["all_event_a_count"].to_i : 0
      gross_spend = fe_record.present? ? fe_record["gross_spend"].to_f : 0.0

      ecpi = install > 0 ? number_to_currency(gross_spend / install, precision: 2) : "$0.00"
      cvr = click > 0 ? number_to_percentage(install*100.0 / click, precision: 2) : "0.00%"
      post_tutorial_cvr = install > 0 ? number_to_percentage(post_tutorial*100.0 / install, precision: 2) : "0.00%"
      all_event_a_cpa = all_event_a > 0 ? number_to_currency(gross_spend / all_event_a, precision: 2) : "$0.00"
      post_level_cpa = post_level > 0 ? number_to_currency(gross_spend / post_level, precision: 2) : "$0.00"
      post_first_purchase_cpa = post_first_purchase > 0 ? number_to_currency(gross_spend / post_first_purchase, precision: 2) : "$0.00"

      report_data << HEADERS.zip([
        vendor_name,
        campaign_name,
        '',
        click,
        install,
        number_to_currency(gross_spend),
        ecpi,
        cvr,
        post_tutorial,
        post_tutorial_cvr,
        all_event_a,
        all_event_a_cpa,
        post_level,
        post_level_cpa,
        post_first_purchase,
        post_first_purchase_cpa
      ]).to_h

      total_click += click
      total_install += install
      total_post_tutorial += post_tutorial
      total_post_level += post_level
      total_post_first_purchase += post_first_purchase
      total_all_event_a += all_event_a
      total_gross_spend += gross_spend
    end

    total_ecpi = total_install > 0 ? number_to_currency(total_gross_spend / total_install, precision: 2) : "$0.00"
    total_cvr = total_click > 0 ? number_to_percentage(total_install*100.0 / total_click, precision: 2) : "0.00%"
    total_post_tutorial_cvr = total_install > 0 ? number_to_percentage(total_post_tutorial*100.0 / total_install, precision: 2) : "0.00%"
    total_all_event_a_cpa = total_all_event_a > 0 ? number_to_currency(total_gross_spend / total_all_event_a, precision: 2) : "$0.00"
    total_post_level_cpa = total_post_level > 0 ? number_to_currency(total_gross_spend / total_post_level, precision: 2) : "$0.00"
    total_post_first_purchase_cpa = total_post_first_purchase > 0 ? number_to_currency(total_gross_spend / total_post_first_purchase, precision: 2) : "$0.00"


    report_data << HEADERS.zip([
      'Total',
      '',
      '',
      total_click,
      total_install,
      number_to_currency(total_gross_spend),
      total_ecpi,
      total_cvr,
      total_post_tutorial,
      total_post_tutorial_cvr,
      total_all_event_a,
      total_all_event_a_cpa,
      total_post_level,
      total_post_level_cpa,
      total_post_first_purchase,
      total_post_first_purchase_cpa
    ]).to_h

    report_data
  end

  def get_data_from_fe
    non_agency_campaigns = []
    agency_campaigns = []
    datas = []
    datas = get_data_sql

    datas
  end

  def get_data_sql
    sql = <<-SQL
        SELECT
          campaign_id,
          campaign_name,
          vendor_name,
          sum(click_count) as click_count,
          sum(install_count) as install_count,
          sum(post_tutorial_count) as post_tutorial_count,
          sum(post_level_count) as post_level_count,
          sum(post_first_purchase_count) as post_first_purchase_count,
          sum(all_event_a_count) as all_event_a_count,
          sum(spend) as gross_spend
        FROM v4_campaigns_view
        WHERE calculate_date BETWEEN '#{start_date}' and '#{end_date}'
        AND status = 'normal'
        AND campaign_id IN (#{campaign_ids.join(',')})
        AND vendor_id NOT IN (#{test_vendor_ids.join(',')})
        GROUP by 1, 2, 3
        HAVING SUM(click_count) > 0 OR SUM(install_count) > 0 OR SUM(spend) > 0
        ORDER by 3, 2;
      SQL
    ConversionRecordRedshift.connection.execute(sql).to_a
  end

  def campaign_ids
    @test_campaign_ids ||= Campaign.joins(:client).where('clients.id': 118).pluck('campaigns.id')
  end

  def test_vendor_ids
    @test_vendor_ids ||= Vendor.where(for_test: true).pluck(:id)
  end
end
