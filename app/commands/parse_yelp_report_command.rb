# frezen_string_literal: true
require 'csv'

class ParseYelpReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'.freeze
  YELP_QUERY = 'from:<EMAIL> newer_than:10d has:attachment filename:csv subject:Yelp Report'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    user_messages = gmail.list_user_messages(user_id, q: YELP_QUERY)
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:invalid, "*No Email In Last One Day Received*")
      [0, 0, 0]
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    csv_data = attachment_content.data.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding("UTF-8"), '')    
    rows = CSV.parse(csv_data, headers: true)
    data = rows[rows.count - 1]
    if data && data['Date'] == date.to_s
      [data['Impressions'], data['Clicks'], data['Revenue']]  # [impressions, clicks, net_spend]
    else
      errors.add(:invalid, "No Data")
      [0, 0, 0]
    end
  end
end
