class MistplayPartnerSpendCommand
  prepend SimpleCommand

  attr_reader :start_date, :end_date, :jampp_reports, :kayzen_reports, :xiaomi_reports, :digital_turbine_reports, :af_api_installs, :google_mappings
  XIAOMI_MAPPING = {21801 => [221966, 221160], 21802 => [221111], 21803 => [221158]}


  def initialize(start_date: , end_date: )
    @start_date = start_date
    @end_date = end_date
    @kayzen_reports = {}
    @jampp_reports = {}
    @xiaomi_reports = {}
    @digital_turbine_reports = DigitalTurbineApiService.new.fetch_report(start_date: start_date, end_date: end_date)
    get_af_api_installs
    get_google_mappings
  end

  def call
    partner_spends = []
    click_urls.map do |click_url|
      (start_date.to_date..end_date.to_date).each do |date|
        partner_data = get_partner_data(click_url, date)
        net_spend = partner_data[:net_spend].to_f
        gross_spend = NetSpend.calculated_gross_spend(net_spend, click_url.margin_by_date(date))
        next if net_spend == 0 && gross_spend == 0

        partner_spends << {
          'click_url_id' => click_url.id,
          'date' => date.to_s,
          'campaign_name' => click_url.campaign.name,
          'vendor_name' => click_url.vendor.vendor_name,
          'gross_spend' => gross_spend.round(2),
          'net_spend' => net_spend.round(2),
        }
      end
    end

    partner_spends
  end

  def get_partner_data(click_url, date)
    res = {}
    if click_url.vendor_id == Vendor::JAMPP_ID
      jampp_campaign_ids = jampp_mappings.select { |m| m.campaign_id == click_url.campaign_id && m.vendor_id == click_url.vendor_id }.map(&:jampp_campaign_id)

      click_url_reports = jampp_reports(date).select { |report| jampp_campaign_ids.include?(report['campaignId']) }
      res[:net_spend] = click_url_reports.sum { |k| k['spend'].to_f }
    elsif click_url.vendor_id == Vendor::KAYZEN_ID
      kayzen_mapping = kayzen_mappings.find { |k| k['click_url_id'].to_i == click_url.id }
      kayzen_campaigns = kayzen_mapping['kayzen_campaigns'].to_s.split(',').map(&:downcase)
      _google_mappings = @google_mappings.select {|c|c['CLICK URL ID'].to_i == click_url.id}.map {|c| c['VENDOR CAMPAIGN NAME'].to_s.downcase }
      click_url_reports = kayzen_reports(date).select { |k| kayzen_campaigns.include?(k["campaign_name"].downcase) || _google_mappings.include?(k["campaign_name"].downcase) }
      res[:net_spend] = click_url_reports.sum {|k| k["enterprise_spend"].to_f.round(2) }
    elsif click_url.vendor_id == Vendor::DIGITALTURBINE_ID
      click_url_reports = digital_turbine(date)
      url_params = get_c_from_tracking_url(click_url)
      res[:net_spend] = click_url_reports.select{|r| r['Campaign Name'] == url_params['c']}.sum{|r| r['Spend'].to_f}
    elsif click_url.vendor_id == Vendor::XIAOMI_ID
      xiaomi_campaign_ids = XIAOMI_MAPPING[click_url.id].to_a
      res[:net_spend] = xiaomi_reports(date).select{|r| r['adCampaignId'].in?(xiaomi_campaign_ids)}.sum{|r| r['costDisplay'].to_f}
    elsif click_url.id == 21766
      install_count = AppsflyerAgencyReport.events_by_click_url(click_url, date)[:install]
      res[:net_spend] = install_count.to_i * click_url.net_cpi_by_date(date)
    else
      {}
    end

    res[:net_spend] = @af_api_installs[[click_url.id, date]].to_i * 10 + res[:net_spend].to_f

    res
  end

  def new_campaigns_list
      new_campaigns = []

      jampp_all_campaign_ids = JamppCampaignMapping.pluck(:jampp_campaign_id)
      kayzen_all_campaign_names = KayzenCampaignMapping.pluck(:kayzen_campaign_name)
      (start_date.to_date..end_date.to_date).each do |date|
        jampp_reports(date).each do |row|
          if row['campaign'].downcase.include?('mistplay') && jampp_all_campaign_ids.exclude?(row['campaignId'].to_i) && row['spend'].to_f > 0
            new_campaigns <<  { Partner: 'Jampp', campaign: row['campaign'], campaignId: row['campaignId'], net_spend: row['spend']}
          end
        end

        kayzen_reports(date).each do |row|
          campaign_name = row["campaign_name"] || row["app_object_store_id"]
          if kayzen_all_campaign_names.exclude?(campaign_name) && row['enterprise_spend'].to_f > 0
            new_campaigns <<  { Partner: 'Kayzen', campaign: campaign_name, net_spend: row['enterprise_spend'] }
          end
        end
      end
      new_campaigns
    end

  def click_urls
    @click_urls ||= ClickUrl.includes(:campaign, :vendor).joins(:campaign, :vendor).visible.where("vendors.for_test = false").where("campaigns.client_id = ?", Client::MISTPLAY).where("click_urls.direct_spend_input = true")
  end

  def jampp_mappings
    @jampp_mappings ||= JamppCampaignMapping.where(campaign_id: click_urls.pluck(:campaign_id).uniq, vendor_id: click_urls.pluck(:vendor_id).uniq).to_a
  end

  def kayzen_mappings
    @kayzen_mappings ||=  KayzenApiService.kayzen_campaign_mapping_records(click_urls.map{|c|c.id})
  end

  def get_af_api_installs
    return @af_api_installs if @af_api_installs.present?
    af_api_reports = AppsflyerApiServiceV3.new(af_app_id: 'com.mistplay.mistplay', client_id: Client::MISTPLAY, start_date:start_date, end_date:end_date).installs_report
    select_af_reports = af_api_reports.select {|c|/^10002/.match?(c['Adset'])}
    @af_api_installs = {}
    (start_date.to_date..end_date.to_date).each do |date|
      @af_api_installs[[21801, date]] = select_af_reports.select {|c| c['Country Code'] == 'UK' && c['Event Time'].to_date == date }.size
      @af_api_installs[[21803, date]] = select_af_reports.select {|c| c['Country Code'] == 'KR' && c['Event Time'].to_date == date }.size
      @af_api_installs[[21802, date]] = select_af_reports.select {|c| !c['Country Code'].in?(['KR', 'UK']) && c['Event Time'].to_date == date }.size
    end
  end

  def get_google_mappings
    api_url = "https://docs.google.com/spreadsheets/d/19Xqw2RefdNGR2UJs8iehItgD0O4SWmmdH5gZYMts9FY/export?gid=0&exportFormat=csv"
    datas = HTTParty.get(api_url)
    keys = datas[0]
    values = datas[1..-1]
    hash_datas = values.map{|row| keys.zip(row).to_h }

    @google_mappings = hash_datas
  end

  def digital_turbine(date)
   digital_turbine_reports.select{|r| r['Date'] == date.to_s}
  end

  def get_c_from_tracking_url(click_url)
    u = click_url.tracking_url
    if click_url.link_type == 'mmp_link' || click_url.link_type == 'agency_link_conversion'
      u = u.gsub('#', '')
      u = u.gsub('%', '')
    end
    query_string = URI(u).query
    Rack::Utils.parse_query(query_string)
  end

  def jampp_reports(date)
    return @jampp_reports[date] if @jampp_reports[date].present?
    params = {
      from: date.to_date,
      to: date.to_date + 1.day
    }
    @jampp_reports[date] = begin
      jampp_mappings.pluck(:client_id, :api_client_id, :api_client_secret).uniq.map do |client_id, api_client_id, api_client_secret|
        credential = {
          client_id: client_id,
          api_client_id: api_client_id,
          api_client_secret: api_client_secret
        }
        JamppGraphqlService.new.fetch_jampp_datas(credential, params, job_name = self.class.name)
      end.flatten
    end
  end

  def kayzen_reports(date)
    return @kayzen_reports[date] if @kayzen_reports[date].present?
    @kayzen_reports[date] = begin
      kayzen_mappings.map { |m| m["kayzen_report_id"] }.uniq.map do |kayzen_report_id|
        service = KayzenApiService.new(report_id: kayzen_report_id)
        service.fetch_csv_from_kayzen(date)
      end.flatten
    end
  end

  def xiaomi_reports(date)
    return @xiaomi_reports[date] if @xiaomi_reports[date].present?
    @xiaomi_reports[date] = XiaomiApiService.new(date, date).call
  end

end