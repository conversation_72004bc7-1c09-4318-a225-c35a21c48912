class MistplayDirectSpendCommand
  prepend SimpleCommand

  attr_reader :start_date, :end_date, :jampp_reports, :kayzen_reports


  def initialize(start_date: , end_date: )
    @start_date = start_date
    @end_date = end_date
  end

  def call
    direct_spends = []
    (start_date.to_date..end_date.to_date).each do |date|
      direct_spends += fetch_spends(date)
    end

    direct_spends.flatten
  end

  def fetch_spends(date)
    net_spends_res = NetSpend.connection.query(
      <<-SQL
        SELECT
          ns.click_url_id,
          ns.spend_date AS date,
          campaigns.name AS campaign_name,
          vendors.vendor_name AS vendor_name,
          SUM(ns.gross_spend_cents)/100.0 AS gross_spend,
          SUM(ns.net_spend_cents)/100.0 AS net_spend
        FROM net_spends ns
        LEFT JOIN click_url_histories AS cuh ON cuh.click_url_id = ns.click_url_id AND cuh.event_date = ns.spend_date
        LEFT JOIN campaigns ON campaigns.id = ns.campaign_id
        LEFT JOIN vendors ON vendors.id = ns.vendor_id
        WHERE spend_date = '#{date}' AND ns.click_url_id IN (#{click_url_ids.join(',')})
          AND cuh.direct_spend_input = true
        GROUP BY 1, 2, 3, 4
      SQL
    ).map{|r| [[r[0].to_i, r[1], r[2], r[3]], [r[4],r[5]]]}.to_h

    spends = net_spends_res.keys.uniq.map do |key|
      direct_spend_gross, direct_spend_net = net_spends_res[key]

      {'click_url_id' => key[0], 'date' => key[1], 'campaign_name' => key[2], 'vendor_name' => key[3], 'gross_spend' => direct_spend_gross.to_f, 'net_spend' => direct_spend_net.to_f }
    end
    spends
  end

  def click_url_ids
    @click_url_ids ||= ClickUrl.includes(:campaign, :vendor).joins(:campaign, :vendor).where("vendors.for_test = false").where("campaigns.client_id = ?", Client::MISTPLAY).map{|c|c.id}
  end

end