# frezen_string_literal: true

class UberMediaPlanWeeklyReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = if Rails.env.production?
                     '1GV1uSlwe46yl-vAPNK_M2zHPLfsQQGkqAjfeErf3r7k'
                   else
                     '1MQn1lP9teMpBxevjsnHk1UyNA1LkcBoUR3e3FH5Nhto'
                   end

  SHEET_NAME_ROW_DATA = '(Dev)Monday Raw data sheet'
  SHEET_ID_ROW_DATA = if Rails.env.production?
                     1446924818
                   else
                     0
                   end

  SHEET_ID_PIVOT = if Rails.env.production?
                     1891244205
                   else
                     436761627
                   end

  RESULT_ATTRIBUTES = [
    :report_date,
    :publisher_name,
    :product_name,
    :geo,
    :spends,
    :net_spends,
    :purchases,
    :agency_purchases,
  ].freeze
  Result = Struct.new(*RESULT_ATTRIBUTES)

  attr_reader :start_date, :end_date, :feedmob_service

  def initialize(start_date:, end_date:)
    @start_date = start_date.to_date
    @end_date = end_date.to_date
    @feedmob_service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    datas = fetch_datas
    existing_data = @feedmob_service.read_values(SHEET_NAME_ROW_DATA)
    datas.each do |data|
      search_hash = {0 => data[:report_date], 1 => data[:publisher_name], 2 => data[:product_name], 5 => data[:geo]}
      @feedmob_service.update_or_append_row2(SHEET_NAME_ROW_DATA, search_hash, data.values, exclude_columns: [], existing_data: existing_data)
      existing_data << data.values if @feedmob_service.find_row_index2(existing_data, search_hash).blank?
    end

    visible_values = [(Date.today - 14.days).beginning_of_week.strftime("%-d-%b"), (Date.today - 7.days).beginning_of_week.strftime("%-d-%b")]

    update_pivot_table(visible_values)

    update_pivot_change_spend(visible_values)
  end

  def fetch_datas
    uber_datas = UberInternalReportCommand.new(start_date: start_date, end_date: end_date).data
    select_uber_datas = uber_datas.select{|data| data.spends > 0 || data.net_spends > 0 || data.purchases > 0 || data.agency_purchases > 0 }
    filter_uber_datas = select_uber_datas.group_by do |data|
      [data.report_date, data.publisher_name, data.product_name, data.geo]
    end.map do |key, items|
      spends = items.sum{|data| data.spends }
      net_spends = items.sum{|data| data.net_spends }
      purchases = items.sum{|data| data.purchases }
      agency_purchases = items.sum{|data| data.agency_purchases }
      row = key + [spends, net_spends, purchases, agency_purchases]
      Result.new(*row)
    end

    filter_uber_datas.sort_by{|data| data.report_date }.map do |data|
      {
        report_date: data.report_date.to_date.strftime("%-m/%-d/%Y"),
        publisher_name: data.publisher_name.gsub(' ', '_'),
        product_name: data.product_name,
        gross_spends: data.spends,
        net_spends: data.net_spends,
        geo: data.geo,
        net_revenue: "=D{{ROW}}-E{{ROW}}",
        fe_first_purchases: data.purchases,
        agency_first_purchases: data.agency_purchases,
        total_events: data.purchases + data.agency_purchases,
        net_cpa: "=E{{ROW}}/J{{ROW}}",
        gross_cpa: "=D{{ROW}}/J{{ROW}}",
        week_starting: data.report_date.to_date.beginning_of_week.strftime("%-d-%b")
      }
    end
  end

  def update_pivot_table(visible_values)
    existing_data = @feedmob_service.read_values(SHEET_NAME_ROW_DATA)
    requests = [{
      update_cells: {
        rows: [{
          values: [{
            pivot_table: {
              source: {
                sheet_id: SHEET_ID_ROW_DATA,
                start_row_index: 2,
                start_column_index: 0,
                end_row_index: existing_data.size,
                end_column_index: 13
              },
              rows: [
                { source_column_offset: 2, show_totals: true, sort_order: 'ASCENDING' },
                { source_column_offset: 5, show_totals: true, sort_order: 'ASCENDING' },
                { source_column_offset: 1, show_totals: true, sort_order: 'ASCENDING' }
              ],
              columns: [
                { source_column_offset: 12, sort_order: 'ASCENDING', show_totals: false }
              ],
              criteria: {
                '12': {visible_values: visible_values }
              },
              values: [
                { summarize_function: 'SUM', source_column_offset: 3 },
                { summarize_function: 'SUM', source_column_offset: 9 }
              ],
              value_layout: 'HORIZONTAL'
            }
          }]
        }],
        start: {
          sheet_id: SHEET_ID_PIVOT,
          row_index: 2,
          column_index: 0
        },
        fields: 'pivotTable'
      }
    }]

    @feedmob_service.update_pivot_table(requests)
  end

  def update_pivot_change_spend(visible_values)
    existing_data = @feedmob_service.read_values(SHEET_NAME_ROW_DATA)
    select_datas = existing_data.select {|c| c.last.in?(visible_values)}
    index_max = select_datas.group_by {|c|[c[1]]}.keys.size + select_datas.group_by {|c|[c[5]]}.keys.size + select_datas.group_by {|c|[c[1], c[2], c[5]]}.keys.size + 8

    values = (6..1.5*index_max.to_i).each do |index|
      range = "(Dev)WoW Monday!H#{index}:K#{index}"
      values = ["=iferror((F#{index}-D#{index})/D#{index},0)", "=iferror((G#{index}-E#{index})/E#{index},0)", "=F#{index}-D#{index}", "=G#{index}-E#{index}"]

      retries = 0
      max_retries = 5
      retry_delay = 70
      begin
        if index <= index_max
          @feedmob_service.update_values(range, [values])
        else
          @feedmob_service.update_values(range, [['','','','']])
        end
      rescue Google::Apis::RateLimitError => e
        retries += 1
        if retries <= max_retries
          sleep(retry_delay)
          retry
        else
          raise "Max retries reached: #{e.message}"
        end
      end
    end
  end

end
