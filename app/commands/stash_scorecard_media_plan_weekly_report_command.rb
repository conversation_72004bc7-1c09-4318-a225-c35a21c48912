# frezen_string_literal: true

require 'googleauth'
require 'google/apis/sheets_v4'

class StashScorecardMediaPlanWeeklyReportCommand
  include ReportConfigConcern
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1Jvj6kJut6rpoMBJhzHkDcBgDtKm2HvZvwcRlRohR_dQ'.freeze
  SHEET_ID = '**********'.freeze
  NEED_HEADER_COLUMNS = {
    'A' => 'Date',
    'I' => 'tutorial',
    'K' => 'all event A (non cohort)',
    'M' => 'level',
    'O' => 'first purchase'
  }.freeze
  NEED_UPDATE_COLUMNS = {
    'A'  => 'PARTNER',
    'B' => 'CAMPAIGN',
    'C' => 'CHANNEL',
    'D' => 'CLICKS',
    'E' => 'INSTALLS',
    'F' => 'SPEND',
    'G' => 'eCPI',
    'H' => 'CVR',
    'I' => 'Account Created',
    'J' => 'Account Created CVR',
    'K' => 'User Chargeable',
    'L' => 'User Chargeable CPA',
    'M' => 'Deposit Initiated',
    'N' => 'Deposit Initiated eCPA',
    'O' => 'Deposit Complete',
    'P' => 'Deposit Complete eCPA'
  }.freeze

  attr_reader :date, :start_date, :end_date

  def initialize(date)
    @date = date.to_date
    @start_date = date.to_date.beginning_of_week
    @end_date = date.to_date.end_of_week
  end

  def call
    service = Google::Apis::SheetsV4::SheetsService.new
    service.authorization = authorizer

    range_name = "Partner Scorecard: Relaunch!A:A"
    response = service.get_spreadsheet_values(SPREADSHEET_ID, range_name)

    # 找到需要更新的序号
    date_str = "#{start_date.strftime('%b %e')} - #{end_date.strftime('%b %e')} (#{end_date.strftime('%Y')})"
    date_values = response.values
    row_indexes = date_values.each_index.select{|index| date_str == date_values[index][0]}.first
    # 直接加到sheet后面
    if row_indexes.blank?
      row_position = date_values.length + 2
      NEED_HEADER_COLUMNS.each do |col_num, col_name|
        range_name = "Partner Scorecard: Relaunch!#{col_num}#{row_position}"
        value = col_name == 'Date' ? date_str : col_name
        service.update_spreadsheet_value(SPREADSHEET_ID, range_name, { values: [[value]] }, value_input_option: 'USER_ENTERED')
      end
      update_font_size(service, row_position -1, row_position)

      row_position = date_values.length + 3
      range_name = "Partner Scorecard: Relaunch!A#{row_position}:P#{row_position}"
      service.update_spreadsheet_value(SPREADSHEET_ID, range_name, { values: [NEED_UPDATE_COLUMNS.values] }, value_input_option: 'USER_ENTERED')
      update_sheet_style(service, row_position - 1, row_position, 'center')

      data.each_with_index do |row, index|
        update_values = row.values
        row_position = date_values.length + 4 + index
        range_name = "Partner Scorecard: Relaunch!A#{row_position}:P#{row_position}"
        service.update_spreadsheet_value(SPREADSHEET_ID, range_name, { values: [update_values] }, value_input_option: 'USER_ENTERED')

        if row['PARTNER'] == 'Total'
          update_sheet_style(service, row_position - 1, row_position)
        else
          update_font_size(service, row_position -1, row_position)
        end
      end

      update_sheet_border(service, date_values.length + 2, date_values.length + 3 + data.size)
    else
      send_report_config_job_slack_alert("#{self.class.name}: Partner Scorecard: Relaunch #{date_str} 已经有数据，不能自动写入，请检查google sheet.", :star)
    end
  end

  def data
    @data ||= StashScorecardReportDataCommand.call(start_date, end_date).result
  end

  private

  def update_font_size(service, start_row_index, end_row_index)
    requests = {
      requests: [
        {
          repeat_cell: {
            range: {
              sheet_id: SHEET_ID,
              start_row_index: start_row_index,
              end_row_index: end_row_index,
              start_column_index: 0,
              end_column_index: 16
            },
            cell: {
              user_entered_format: {
                text_format: {
                  font_size: 7
                }
              }
            },
            fields: "userEnteredFormat(textFormat)"
          }
        }
      ]}
    service.batch_update_spreadsheet(SPREADSHEET_ID, requests)
  end

  def update_sheet_style(service, start_row_index, end_row_index, center = nil)
    requests = {
      requests: [
        {
          repeat_cell: {
            range: {
              sheet_id: SHEET_ID,
              start_row_index: start_row_index,
              end_row_index: end_row_index,
              start_column_index: 0,
              end_column_index: 16
            },
            cell: {
              user_entered_format: {
                background_color: { red: 0, green: 0, blue: 0 },
                horizontal_alignment: (center.present? ? "CENTER" : 'RIGHT'),
                text_format: {
                  foreground_color: {
                    red: 1,
                    green: 1,
                    blue: 1
                  },
                  font_size: 7,
                  bold: true
                }
              }
            },
            fields: "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
          }
        }
      ]}
    service.batch_update_spreadsheet(SPREADSHEET_ID, requests)
  end

  def update_sheet_border(service, start_row_index, end_row_index)
    requests = {
      requests: [
        {
          update_borders: {
            range: {
              sheet_id: SHEET_ID,
              start_row_index: start_row_index,
              end_row_index: end_row_index,
              start_column_index: 0,
              end_column_index: 16
            },
            top: {
              style: "SOLID",
              width: 1,
              color: {
                red: 0, green: 0, blue: 0
              }
            },
            bottom: {
              style: "SOLID",
              width: 1,
              color: {
                red: 0, green: 0, blue: 0
              }
            },
            right: {
              style: "SOLID",
              width: 1,
              color: {
                red: 0, green: 0, blue: 0
              }
            },
            inner_horizontal: {
              style: "SOLID",
              width: 1,
              color: {
                red: 0, green: 0, blue: 0
              }
            },
            inner_vertical: {
              style: "SOLID",
              width: 1,
              color: {
                red: 0, green: 0, blue: 0
              }
            },
            outer_horizontal: {
              style: "SOLID",
              width: 1,
              color: {
                red: 0, green: 0, blue: 0
              }
            },
            outer_vertical: {
              style: "SOLID",
              width: 1,
              color: {
                red: 0, green: 0, blue: 0
              }
            }
          }
        }
      ]
    }
  service.batch_update_spreadsheet(SPREADSHEET_ID, requests)
end

  def authorizer
    @authorizer ||= begin
      authorizer = Google::Auth::ServiceAccountCredentials.make_creds(
        json_key_io: File.open(client_secrets_file_path),
        scope: SCOPE
      )
      authorizer.fetch_access_token!
      authorizer
    end
  end

  def client_secrets_file_path
    File.join(current_path, "config", 'feedmob-4bbd49e741a5.json')
  end

  def current_path
    Dir["#{Rails.root}"]
  end
end
