# frezen_string_literal: true
require 'csv'

class ParseUberTechnologiesInmobiReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'.freeze
  INMOBI_QUERY = 'from:<EMAIL> newer_than:1d has:attachment filename:csv subject:Feedmob Uber Eats + Uber Rider Daily report'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    user_messages = gmail.list_user_messages(user_id, q: INMOBI_QUERY)
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:invalid, "*No Email In Last One Day Received*")
      []
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    csv_data = attachment_content.data.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding("UTF-8"), '')
    data = CSV.parse(csv_data, headers: true).map(&:to_h)
    select_datas = data.select { |row| row['Date'] == date }
    if select_datas.present?
      select_datas
    else
      errors.add(:invalid, "No Data")
      []
    end
  end
end
