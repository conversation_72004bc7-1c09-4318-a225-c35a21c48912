class BrigitSamsungMediaPlanDailyReportCommand
  SPREADSHEET_ID = "1p1nYJQe1NNUo-o2JxucxvQweFNF5mJyVMJwxB-QQzIo"
  # Local testing
  # SPREADSHEET_ID = "1zG1-2Hwf_c2imi2Fub1ZweXbQBLnhSUOMmCKpydTSMc"
  CLICK_URL_ID = 20926

  def initialize(date)
    @date = date.to_date
    @service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    return unless click_url.live?

    # Update tab Samsung Spend tracking
    date = @date.strftime("%m/%d/%Y")
    @service.update_or_append_row("Samsung Spend tracking ", 0, date, [
      date, # Date
      "", # Ignored
      agency_report.fetch("install").to_i, # AF Installs
      "", # Ignored
      "", # Ignored
      "", # Ignored
      in_app_report.fetch("purchase").to_i, # AF (new subscription)
    ], exclude_columns: [1, 3, 4, 5])


    # # Update tab Margin Tracking
    date = @date.strftime("%m/%d/%y")
    @service.update_or_append_row("Margin Tracking", 0, date, [
      date, # Date
      click_url.campaign.name, # Campaign
      click_url.vendor.name, # Vendor
      in_app_report.fetch("purchase").to_i, # AF (new subscription)
      "='Samsung Spend tracking '!C{{ROW}}", # AF Installs
      "='Samsung Spend tracking '!E{{ROW}}", # Samsung Install Report
    ])
  end

  private

  def click_url
    @click_url ||= ClickUrl.find(CLICK_URL_ID)
  end

  def agency_report
    @agency_report ||= AppsflyerAgencyReport.events_by_click_url(click_url, @date)
  end

  def in_app_report
    @in_app_report ||= AppsflyerInAppEvent.events_by_click_url(click_url, @date)
  end
end
