
# frezen_string_literal: true
class GenerateScopelyAdjustEventsCommand
  prepend SimpleCommand
  include MmpAggregateDataConcern

  SYSTEM_EMAIL = '<EMAIL>'

  attr_reader :start_date, :end_date, :app_id, :report_not_mappings

  def initialize(start_date: Date.yesterday.to_s, end_date: Date.yesterday.to_s, app_id: nil)
    @start_date = start_date
    @end_date = end_date
    @app_id = app_id
    @report_not_mappings = []
    @mmp_aggrate_datas = []
    @update_paid_actions = ClickUrl::BIND_ACTIONS_TOKEN.keys - [:impression, :click, :install]
  end

  def call
    scopely_datas = FetchScopelyAdjustEventsCommand.call(start_date: start_date, end_date: end_date, app_id: app_id).result
    return if scopely_datas.empty?

    (start_date.to_date..end_date.to_date).each do |date|
      save_report(date, scopely_datas)
    end
    save_mmp_aggregate_datas

    return if report_not_mappings.empty?

    SlackService.send_notification_to_star("#{self.class.name}: 在 #{start_date} ~ #{end_date}, adjust api 存在数据，但没有对应的 adjust campaign mapping，请确认以下列表是否需要添加 mapping。\n#{report_not_mappings.uniq.map { |r| r.join(', ') }.join("\n")}")
  end

  def save_report(date, campaign_datas)
    date_campaign_datas = campaign_datas.select { |c| c['day'].to_date.to_s == date.to_s }
    group_campaign_datas = date_campaign_datas.group_by {|c| [c['store_id'], c['campaign'], c['os_name'], c['partner_name'].to_s.downcase] }

    group_campaign_datas.each do |key, items|
      app_id, adjust_campaign_name, adjust_os_name, adjust_channel = key

      next if adjust_campaign_name.in?(exclude_data[app_id].to_a)

      impressions = items.sum{|c|c['impressions'].to_i}
      clicks = items.sum{|c|c['clicks'].to_i}
      installs = items.sum{|c|c['installs'].to_i}

      send_mapping_notification = AdjustCampaignMapping.send_adjust_mapping_notification(impressions, clicks, installs)

      click_url = AdjustCampaignMapping.find_mapped_click_url(adjust_campaign_name, adjust_channel, Client::SCOPELY_ID, false, send_mapping_notification)

      if click_url.blank?
        report_not_mappings << key if send_mapping_notification
        next
      end

      # build report data
      model = { }
      mmp_aggrate_data = model.dup

      event_mappings.keys.each do |feedmob_event|
        event_slugs = event_mappings[feedmob_event]
        mmp_aggrate_data["#{feedmob_event}_original".to_sym] = {}

        event_count = 0
        event_slugs.each do |adjust_event|
          item_event_count = items.sum{|c|c[adjust_event].to_i}
          mmp_aggrate_data["#{feedmob_event}_original".to_sym][adjust_event] = item_event_count
          event_count += item_event_count
        end

        model[feedmob_event.to_sym] = event_count
        mmp_aggrate_data[feedmob_event.to_sym] = event_count
      end

      @mmp_aggrate_datas << mmp_aggrate_data.merge({source_report_type: adjust_report_table_by(click_url).to_s, click_url_id: click_url.id, date: date})

      report = adjust_report_table_by(click_url).find_or_initialize_by(
        click_url_id: click_url.id,
        campaign_id: click_url.campaign_id,
        vendor_id: click_url.vendor_id,
        date: date,
        adjust_campaign_name: adjust_campaign_name,
        adjust_channel: adjust_channel,
        adjust_os_name: adjust_os_name,
        skan: false
      )

      Audited.audit_class.as_user(User.find_by(email: SYSTEM_EMAIL)) do
        report.update model
      end
    end
  end

  def adjust_report_table_by(click_url)
    click_url.link_type == 'fm_link' ? AdjustReport : AdjustAgencyReport
  end

  def event_mappings
    @event_mappings ||= AdjustEventMapping.find_by(client_id: Client::SCOPELY_ID, app_id: app_id).events_in_json
  end

  def exclude_data
    @exclude_data ||= TrackingConfig.find_by(config_type: "system", name: "EXCLUDE_SCOPELY_ADJUST_REPORT_DATA")&.value&.with_indifferent_access
  end
end
