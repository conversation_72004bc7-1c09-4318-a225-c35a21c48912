# frezen_string_literal: true
require 'zip'

class ParseVendorReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  LIFTOFF_QUERY = 'filename:Liftoff_GoPuff_Report_%s_%s.csv.zip'.freeze
  FIELDS = %w[date campaign app_id creative impressions clicks installs spend].freeze

  REPORT_MAPPINGS = [
    {
      vendor_id: 206,
      query: LIFTOFF_QUERY,
      click_url_divider: '_'
    }
  ].freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date.to_date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    REPORT_MAPPINGS.each do |mapping|
      parse_email(mapping[:vendor_id], mapping[:query], mapping[:click_url_divider])
    end
  end

  def generate_query(query_str)
    query_str % [(date - 1.day).to_s, date.to_s]
  end

  private

  def parse_email(vendor_id, query_str, click_url_divider)
    query = generate_query(query_str)
    user_messages = gmail.list_user_messages(user_id, q: query )
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:no_email, "*No Email In Last One Day Received*")
      0
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message, vendor_id: vendor_id, click_url_divider: click_url_divider)
    end
  end

  def analyze_email(gmail:, user_id:, message:, vendor_id:, click_url_divider:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    Zip::File.open_buffer(attachment_content.data) do |zip|
      entry = zip.first
      content = entry.get_input_stream.read
      rows = CSV.parse(content, headers: true)
      mismatch = []
      header_valid = true
      rows&.first&.headers&.each do |header|
        if !FIELDS.include?(header.downcase.gsub(' ', '_'))
          header_valid = false
        end
      end
      if !header_valid
        SlackService.send_notification_to_channel("VendorReport vendor_id #{vendor_id} csv header invalid!", :mighty)
        return
      end
      rows.each do |row|
        h = row.to_h.transform_keys { |key| key.downcase.gsub(' ', '_') }
        click_url_id = h['campaign']&.split(click_url_divider)&.last
        click_url = ClickUrl.find_by(id: click_url_id)
        if click_url.blank?
          mismatch << h['campaign']
        else
          VendorReport.find_or_initialize_by(
            vendor_id: vendor_id,
            click_url_id: click_url_id,
            campaign_id: click_url.campaign_id,
            app_id: h['app_id'],
            creative: h['creative'],
            date: h['date']
          ).update(h)
        end
      end
      if mismatch.present?
        SlackService.send_notification_to_channel("VendorReport vendor_id #{vendor_id} can't mapping #{mismatch.uniq.join(', ')}", :mighty)
      end
    end
  end
end
