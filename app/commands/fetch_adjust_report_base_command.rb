# frezen_string_literal: true
class FetchAdjustReportBaseCommand
  prepend SimpleCommand
  include AdjustApiConcern

  API_HOST = 'https://automate.adjust.com'.freeze
  ACCESS_TOKEN = 'tHjmNx2vH6poxxyvzYpp'.freeze
  IPVANISH_ADJUST_ACCOUNT_ID = '24640'.freeze
  CLIENT_ID = 161 # IPVanish

  def fetch_adjust_data(start_date, end_date)
    base_metrics = %w[impressions clicks installs]
    event_metrics = event_mappings.map { |m| m.events_in_json.values.flatten.uniq }.flatten.uniq
    params = {
      date_period: "#{start_date}:#{end_date}",
      dimensions: 'day,store_id,app,os_name,partner_name,campaign,campaign_id_network,campaign_network',
      metrics: (base_metrics + event_metrics).join(','),
      attribution_type: 'all',
      attribution_source: 'first',
      utc_offset: '+00:00',
      os_names: 'ios,android',
      channel__in: adjust_api_channels("ipvanish")
    }
    res = Retriable.retriable(base_interval: 3) do
      HTTParty.get(
        "#{API_HOST}/control-center/reports-service/csv_report?#{params.to_query}",
        headers: {'Authorization'=> "Bearer #{ACCESS_TOKEN}", 'x-account-id' => IPVANISH_ADJUST_ACCOUNT_ID }
        )
    end

    csv_data = res.body.force_encoding('UTF-8').gsub("\xEF\xBB\xBF".force_encoding("UTF-8"), '')
    CSV.parse(csv_data, headers: true)
  end

  def event_mappings
    @event_mappings ||= AdjustEventMapping.where(client_id: CLIENT_ID).to_a
  end

  def mappings
    @mappings ||= AdjustCampaignMapping.joins(:vendor).includes(:campaign => :app).where("vendors.for_test IS FALSE").where(client_id: CLIENT_ID, skan: false).to_a
  end

  def ipvanish_dsp_click_url_by(mapping)
    click_urls = ClickUrl.where(campaign_id: mapping.campaign_id, vendor_id: mapping.vendor_id, status: %w[cu_normal active])
    if click_urls.count == 0
      click_url = ClickUrl.where(campaign_id: mapping.campaign_id, vendor_id: mapping.vendor_id, status: 'paused').order(:id).last
      if click_url.blank?
        click_url = ClickUrl.where(campaign_id: mapping.campaign_id, vendor_id: mapping.vendor_id, status: 'archived').order(:id).last
      end
    elsif click_urls.length == 1
      click_url = click_urls.first
    else
      # 16346 优先匹配最大的click url
      click_url = click_urls.order(:id).last
    end
    click_url
  end
end
