# frezen_string_literal: true

require 'roo'

class ParseSamsungReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  MAIL_QUERY = 'from:<EMAIL> newer_than:30d has:attachment filename:xlsx subject:<PERSON><PERSON>, Sur<PERSON>hark, Magnifi, Upside data'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    user_messages = gmail.list_user_messages(user_id, q: MAIL_QUERY)
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:no_email, "*No Email In Last One Day Received*")
      []
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    File.open(local_file_path, "wb"){|f| f.puts attachment_content.data }
    wb = Roo::Spreadsheet.open(local_file_path)
    sheet_name = date.to_date.strftime('%Y%m')
    sheet = wb.sheet(sheet_name)
    datas = sheet.parse(headers: true)
    datas.map {|data| data['Date'] = data[nil]; data.delete(nil); data }
  ensure
    clear_local_tmp_files
  end

  def clear_local_tmp_files
    sh "rm -rf #{local_file_path}"
  end

  def local_file_path
    "/tmp/samsung_mail.xlsx"
  end
end
