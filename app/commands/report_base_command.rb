require 'zip'
require 'csv'

class ReportBaseCommand
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  def client_id
    @client_id ||= Client.where(name: client_name).pluck(:id).presence || Client.find_by(id: default_client_id)
  end

  def campaign_ids
    @campaign_ids ||= Campaign.unscoped.where(client_id: client_id).pluck(:id)
  end

  def vendor_ids
    @vendor_ids ||= Vendor.where(id: Client.where(id: client_id).map { |c| c.vendor_ids }.flatten.uniq, for_test: false).pluck(:id)
  end

  def date_range
    if respond_to? :start_date
      "#{start_date} to #{end_date}"
    else
      "#{end_date} to #{end_date}"
    end
  end

  def os(campaign_name)
    return 'iOS' if campaign_name.to_s.downcase =~ /ios|iphone/
    return 'Android' if campaign_name.to_s.downcase.include?('and')
  end

  def geo(campaign_name)
    match = campaign_name.match(/iOS_(\w{2})_|Android_(\w{2})_|mweb_(\w{2})_/)
    match ? (match[1] || match[2] || match[3]) : nil
  end

  def country(campaign_name, campaign_id)
    campaign_name.gsub(/\s/, '') =~ /(?:iOS|Android)[_-]([A-Z]{2,})/
    _country = Regexp.last_match(1)
    if _country.blank? || Country[_country].nil?
      _country = approved_countries(campaign_id)
    end
    _country.gsub('GB', 'UK')
  end

  def approved_countries(campaign_id)
    campaign = campaign(campaign_id)
    return '' if campaign.blank?
    campaign.click_urls.map(&:approved_countries).flatten.uniq.join(', ')
  end

  def app_name(campaign_name)
    campaign_name.gsub(/\s/, '') =~ /^([a-zA-Z0-9]+)[_-](?:iOS|Android)[_-]([A-Z]{2,})$/

    name = Regexp.last_match(1).to_s.upcase
    mappings_name = singular_report_name_mappings.dig(name)

    raise Exceptions::DailyReportError, "Could not find #{name} anywhere" if mappings_name.blank?

    mappings_name
  end

  def singular_report_name_mappings
    @singular_report_name_mappings ||=
      SingularReportNameMapping.mappings_by(client_id: client_id).transform_keys{ |key| key.to_s.upcase }
  end

  def campaign(campaign_id)
    Rails.cache.fetch("daily-report-campaign:#{campaign_id}", expires_in: 5.minutes) { Campaign.find_by(id: campaign_id) }
  end

  def reporting_campaign_name(campaign_id, campaign_name)
    campaign = campaign(campaign_id)
    return campaign_name if campaign.blank?
    campaign.reporting_campaign_name_switch ? campaign.reporting_campaign_name : campaign_name
  end

  def cpi_group?
    true
  end

  def statuses(status_group = 'default')
    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :value).map { |name| NewConversionRecord::STATUS_ENUM[name] }
    return status_list unless status_group == 'default'

    cpi_group? ? [1, 3, 5, 6] : [1, 3]
  end

  def v2_statuses(status_group = 'default')
    @v2_statuses ||= begin
      status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :value)
      return status_list unless status_group == 'default'

      cpi_group? ? [:normal, :injected, :over_cap, :manual_stopped] : [:normal, :injected]
    end
  end

  def spend_statuses(status_group = 'default')
    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :spend_calculation)
    status_list.map { |name| NewConversionRecord::STATUSES[name] }
  end

  def v2_spend_statuses(status_group = 'default')
    NewConversionRecord::STATUS_GROUPS.dig(status_group, :spend_calculation)
  end

  def install_statuses(status_group = 'default')
    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :value)
    status_list.map { |name| NewConversionRecord::STATUSES[name] }
  end

end
