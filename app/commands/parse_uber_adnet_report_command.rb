# frozen_string_literal: true
require 'csv'

class ParseUberAdnetReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'.freeze
  ADNET_QUERY = 'from:<EMAIL> newer_than:1d has:attachment filename:csv subject:Ad.net - Feedmob Daily Report'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    user_messages = gmail.list_user_messages(user_id, q: ADNET_QUERY)
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:invalid, "*No Email In Last One Day Received*")
      [0, 0]
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    csv_data = attachment_content.data.force_encoding('UTF-8')
    data = CSV.parse(csv_data, headers: true)
    select_data = data.find { |row| row['Date'] == date.to_s }
    if select_data.present?
      [select_data['Clicks'].to_i, select_data['Spend'].to_f]  # [clicks, net_spend]
    else
      errors.add(:invalid, "No Data")
      [0, 0]
    end
  end
end
