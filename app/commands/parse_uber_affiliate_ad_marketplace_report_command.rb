# frezen_string_literal: true
require 'csv'
require 'zip'

class ParseUberAffiliateAdMarketplaceReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  OOB_URI = 'urn:ietf:wg:oauth:2.0:oob'.freeze
  ADMARKETPLACE_QUERY = 'from:<EMAIL> newer_than:60d has:attachment filename:zip subject:Your Scheduled adMarketplace report is attached'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    query = "#{ADMARKETPLACE_QUERY} #{date.to_s}"
    user_messages = gmail.list_user_messages(user_id, q: query)
    @message = user_messages.messages.select do |m|
      email = gmail.get_user_message user_id, m.id
      email.snippet.include?('Uber_AMP_CPC')
    end.first if user_messages.messages.present?

    if @message.blank?
      errors.add(:invalid, "*No Email In Last One Day Received*")
      []
    else
      analyze_email(gmail: gmail, user_id: user_id, message: @message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    select_datas = []
    Zip::File.open_buffer(attachment_content.data) do |zip|
      entry = zip.first
      content = entry.get_input_stream.read
      data = CSV.parse(content, headers: true).map(&:to_h)
      select_datas = data.select { |row| row['Date'] == date }

      if select_datas.blank?
        errors.add(:invalid, "No Data")
      end
    end

    select_datas
  end
end
