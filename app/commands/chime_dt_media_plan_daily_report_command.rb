# frezen_string_literal: true

require 'googleauth'
require 'google/apis/sheets_v4'

class ChimeDtMediaPlanDailyReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1PrfXLrcGIwqx9-0Ke2EZgdq4-nirq_4e4V5RqA38hAU'.freeze
  NEED_UPDATE_COLUMNS = {
    'A'  => 'Date',
    'B' => 'CAMPAIGN',
    'C' => 'INSTALL',
    'D' => 'Client CPI',
    'E' => 'Registrations',
    'F' => 'CVR2 Install to Reg',
    'G' => 'eCPE',
    'H' => 'SPEND Gross',
    'I' => 'CPP Bid',
    'J' => 'Preloads From Digital Turbine',
    'K' => 'Spend from Digital Turbine (NET)',
    'L' => 'DT eCPI (NET CPI)',
    'M' => 'DT eCPE (NET CPE)',
    'N' => 'CVR Pre-load to install',
    'O' => 'CVR2 Pre-load to Reg',
    'P' => 'Profit/Loss',
    'Q' => 'Margin',
  }.freeze

  attr_reader :date

  def initialize(date)
    @date = date.to_date
  end

  def call
    service = Google::Apis::SheetsV4::SheetsService.new
    service.authorization = authorizer

    header_range = '2021-2023 Digital Turbine Margin Sheet!A1:R1'
    
    response = service.get_spreadsheet_values(SPREADSHEET_ID, header_range)
    headers = response.values.first if response.values

    if headers != ChimeDtReportDataCommand::HEADERS
      SlackService.send_notification_to_channel("#{self.class.name}: Chime Digital Turbine Media Plan Google Sheet Header 不一致，无法自动录入，请检查google sheet, #{headers.join(',')}", :mighty)
      return
    end

    range_name = "2021-2023 Digital Turbine Margin Sheet!A:A"
    response = service.get_spreadsheet_values(SPREADSHEET_ID, range_name)

    # 找到需要更新的序号
    date_str = @date.strftime("%-m/%-d/%y")
    date_values = response.values.flatten
    row_indexes = date_values.each_index.select{|index| date_str == date_values[index]}
    # 直接加到sheet后面
    if row_indexes.blank?
      data.each_with_index do |row, index|
        NEED_UPDATE_COLUMNS.each do |col_num, col_name|
          range_name = "2021-2023 Digital Turbine Margin Sheet!#{col_num}#{date_values.length + 1 + index}"
          if col_name == 'Date'
            value = date_str
          else
            value = row[col_name]
          end
          service.update_spreadsheet_value(SPREADSHEET_ID, range_name, { values: [[value]] }, value_input_option: 'USER_ENTERED')
        end
      end
    else # 更新原来的行
      if row_indexes.length != data.length
        SlackService.send_notification_to_channel("#{self.class.name}: Chime Digital Turbine Media Plan Google Sheet #{date} 的行数和数据行数不一致，不能直接覆盖，请检查google sheet", :mighty)
        return
      end

      slack_info = []
      data.each_with_index do |row, index|
        diff_values = {}
        NEED_UPDATE_COLUMNS.each do |col_num, col_name|
          range_name = "2021-2023 Digital Turbine Margin Sheet!#{col_num}#{row_indexes[index]+1}"
          value = row[col_name]
          value = value.strftime("%-m/%-d/%y") if value.is_a?(Date)
          value_in_sheet = service.get_spreadsheet_values(SPREADSHEET_ID, range_name).values.flatten.first
          if value.to_s != value_in_sheet.to_s
            diff_values[col_name] = value
          end
        end
        slack_info << diff_values if diff_values.present?
      end
      if slack_info.present?
        SlackService.send_notification_to_channel("#{self.class.name}: Chime Digital Turbine Media Plan Google Sheet #{date} 已有数据，且原有数据和现在预填入的值不一致，不能直接覆盖。预填入的值: #{slack_info}", :mighty)
      end
    end
  end

  def data
    @data ||= ChimeDtReportDataCommand.call(date).result
  end

  private

  def authorizer
    @authorizer ||= begin
      authorizer = Google::Auth::ServiceAccountCredentials.make_creds(
        json_key_io: File.open(client_secrets_file_path),
        scope: SCOPE
      )
      authorizer.fetch_access_token!
      authorizer
    end
  end

  def client_secrets_file_path
    File.join(current_path, "config", 'feedmob-4bbd49e741a5.json')
  end

  def current_path
    Dir["#{Rails.root}"]
  end
end
