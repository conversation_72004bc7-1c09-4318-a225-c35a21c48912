# frezen_string_literal: true

class BinanceMediaPlanDailyReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = if Rails.env.production?
                     '1mhEKh0pcV3AFLy0bza-0H8FLOLYtzhJa4DkuslRWNCw'
                   else
                     '1G6dhrAF1Q30U0yYjYFyGwP0uEMmqg1yotRgdnO860iA'
                   end

  SHEET_NAME = 'Margin and Performance Tracking'
  attr_reader :date, :start_date, :end_date

  def initialize(date)
    @date = date.to_date
    @service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    datas = fetch_datas
    datas.each do |data|
      search_hash = {0 => data['Date'], 1 => data['Click URL']}
      @service.update_or_append_row2(SHEET_NAME, search_hash, data.values, exclude_columns: [])
    end
  end

  def fetch_datas
    response = @service.get_sheet_headers(SHEET_NAME)
    sheet_headers = response.first
    @data ||= BinanceReportDataCommand.call(date,sheet_headers).result
  end
end
