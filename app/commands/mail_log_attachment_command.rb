class MailLogAttachmentCommand
  prepend SimpleCommand

  def upload_stream(log_id, filename, stream)
    log_id = "email-attachments/#{log_id}" unless Rails.env.production?

    obj = Aws::S3::Object.new(bucket_name, "#{log_id}/#{filename}", client: s3_client)
    obj.upload_stream do |write_stream|
      write_stream << stream.force_encoding('UTF-8')
    end
    "s3://#{bucket_name}/#{log_id}/#{filename}"
  rescue Aws::S3::Errors::ServiceError => e
    SlackService.send_notification_to_star("#{self.class.name} s3 upload error: #{e.message}, #{log_id}/#{filename}")
  end

  private

  def bucket_name
    @bucket_name ||= Rails.env.production? ? "feedmob-email-attachments" : "feedmob-testing"
  end

  def s3_client
    @s3_client ||= Aws::S3::Client.new(
      region: ENV["AWS_S3_REGION"] || "us-east-1"
    )
  end
end
