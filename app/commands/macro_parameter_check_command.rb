class MacroParameterCheckCommand
  attr_reader :url, :supported, :skipped
  def initialize(url, supported = [], skipped = [])
    @url = url
    @supported = supported
    @skipped = skipped
  end

  def unsupported_macro
    url_current_macros = url_macros.map {|k| "{#{k}}"}
    url_current_macros - (url_current_macros & supported) - skipped
  end

  def unsupported_parameter
    url_paramters - (url_paramters & supported) - skipped
  end

  private

  def url_macros
    @url_macros ||= begin
      url.scan(/\{(.*?)\}/).flatten.uniq
    rescue => e
      Sentry.capture_exception(e, extra: {url: url, action: 'url macro check'})
      []
    end
    url.scan(/\{(.*?)\}/).flatten.uniq
  end

  def url_paramters
    @url_paramters ||= begin
      params_hash = Rack::Utils.parse_query URI(url).query
      params_hash.keys
    rescue => e
      Sentry.capture_exception(e, extra: {url: url, action: 'url parameter check'})
      []
    end
  end
end
