class ScheduleReport::CampaignsVendorIndexExportCommand < ScheduleReport::BaseExportCommand
  include ScheduleReport::V4SupportConcern
  
  RESULT_ATTRIBUTES = ([
    :vendor_id_s,
    :vendor_name,
    :campaign_id_s,
    :campaign_name,
  ] + BASE_ATTRIBUTES).freeze

  Result = ResultCustomStruct.new(*RESULT_ATTRIBUTES)

  private

  def fetch_data
    @data ||= Rails.cache.fetch(cache_key, expires_in: expires_in) do
      return [] unless campaign_id.present?

      results = ConversionRecordRedshift.connection.query(sql).to_a
      results.map { |result| Result.new(*result) }
    end
  end

  def sql
    @sql ||= <<-SQL
      SELECT vendor_id,
             vendor_name                               AS vendor_name,
             campaign_id,
             campaign_name                             AS campaign_name,
             #{base_sql}
      FROM v4_campaigns_view
      #{conditions}
      GROUP BY 1, 2, 3, 4
      ORDER BY 1, 3
    SQL
  end
end
