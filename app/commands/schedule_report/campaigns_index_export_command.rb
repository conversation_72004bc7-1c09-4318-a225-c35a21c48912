class ScheduleReport::CampaignsIndexExportCommand < ScheduleReport::BaseExportCommand
  include ScheduleReport::V4SupportConcern

  RESULT_ATTRIBUTES = ([
    :campaign_id_s,
    :campaign_name,
    :blocked_click_count,
  ] + BASE_ATTRIBUTES).freeze

  Result = ResultCustomStruct.new(*RESULT_ATTRIBUTES)

  private

  def fetch_data
    @data ||= Rails.cache.fetch(cache_key, expires_in: expires_in) do
      return [] unless campaign_id.present?

      results = ConversionRecordRedshift.connection.query(sql).to_a
      results.map { |result| Result.new(*result) }
    end
  end

  def sql
    @sql ||= <<-SQL
      SELECT campaign_id                                  AS campaign_id,
             campaign_name                                AS campaign_name,
             SUM(blocked_click_count)                     AS blocked_click_count,
             #{base_sql}
      FROM v4_campaigns_view
      #{conditions}
      GROUP BY 1, 2
      ORDER BY 2
    SQL
  end
end
