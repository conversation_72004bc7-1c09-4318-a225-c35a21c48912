class ScheduleReport::VendorsIndexExportCommand < ScheduleReport::BaseExportCommand
  include ScheduleReport::V4SupportConcern

  RESULT_ATTRIBUTES = ([
    :vendor_id_s,
    :vendor_name,
  ] + BASE_ATTRIBUTES).freeze

  Result = ResultCustomStruct.new(*RESULT_ATTRIBUTES)

  private

  def fetch_data
    Rails.cache.delete(cache_key) if clear_cache?
    @data ||= Rails.cache.fetch(cache_key, expires_in: expires_in) do
      return [] unless vendor_id.present?
      results = ConversionRecordRedshift.connection.query(sql).to_a
      results.map { |result| Result.new(*result) }
    end
  end

  def sql
    @sql ||= <<-SQL
      SELECT vendor_id                                  AS vendor_id,
             vendor_name                                AS vendor_name,
             #{base_sql}
      FROM v4_campaigns_view
      #{conditions}
      GROUP BY 1, 2
      ORDER BY 2
    SQL
  end
end
