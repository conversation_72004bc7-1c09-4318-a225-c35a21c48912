module ScheduleReport::CommandResultHelper
  class ResultCustomStruct < Struct
    def self.send_method_name(columns)
      columns.each do |method_name|
        define_method method_name do
          num = (send "#{method_name}_s")
          yield(num)
        end
      end
    end

    send_method_name([
      :impression_count,
      :click_count,
      :install_count,
      :all_install_count,
      :retained_count,
      :tutorial_count,
      :registration_count,
      :purchase_count,
      :first_purchase_count,
      :level_count,
      :open_count
    ]) do |num|
      num ? num.to_i : 0
    end

    def calculate_date
      return unless calculate_date_s.present?
      calculate_date_s.to_date
    end

    def campaign_id
      return unless campaign_id_s.present?
      campaign_id_s.to_i
    end

    def client_id
      return unless client_id_s.present?
      client_id_s.to_i
    end

    def vendor_id
      return unless vendor_id_s.present?
      vendor_id_s.to_i
    end

    def click_id
      return unless click_id_s.present?
      click_id_s.to_i
    end

    def unique_app_id
      "#{vendor_id}-#{click_id}-#{app_id}"
    end

    def original_app_id
      app_id
    end

    def spend
      return 0 unless spend_s.present?
      spend_s.to_d
    end

    def gross_spend
      return 0 unless gross_spend_s.present?
      gross_spend_s.to_d
    end

    def net_spend
      return 0 unless net_spend_s.present?
      net_spend_s.to_d
    end

    def purchase_amount
      return 0 unless purchase_amount_s.present?
      purchase_amount_s.to_d
    end

    def cpi
      install_count.zero? ? 0 : spend / install_count
    end

    def cpa(event)
      events = event_count(event)
      events.zero? ? 0 : spend / events
    end

    def cvr
      click_count.zero? ? 0 : install_count / click_count.to_d
    end

    def cvr2(event)
      events = event_count(event)
      install_count.zero? ? 0 : events / install_count.to_d
    end

    def event_count(event)
      send "#{event}_count"
    end

    def margin
      gross_spend.zero? ? 0 : net_revenue / gross_spend
    end

    def net_revenue
      gross_spend - net_spend
    end
  end
end
