class ScheduleReport::ClientsDailyExportCommand < ScheduleReport::BaseExportCommand
  include ScheduleReport::V4SupportConcern

  RESULT_ATTRIBUTES = ([
    :client_id_s,
    :client_name,
    :calculate_date_s,
  ] + BASE_ATTRIBUTES).freeze

  Result = ResultCustomStruct.new(*RESULT_ATTRIBUTES)

  private

  def fetch_data
    @data ||= Rails.cache.fetch(cache_key, expires_in: expires_in) do
      query_results = ConversionRecordRedshift.connection.query(sql).to_a
      query_results.each do |r|
        (1..11).each{ |n| r[n] = r[n].to_i }
        (12..15).each{ |n| r[n] = r[n].present? ? r[n].to_d : 0 }
        r << (client_names[r[1]] ? client_names[r[1]][:client_id] : nil)
        r << (client_names[r[1]] ? client_names[r[1]][:client_name] : nil)
      end
      results = query_results.group_by{|r| [r[-2], r[-1], r[0]]}.map do |k, v|
        [
          k[0],
          k[1],
          k[2],
          v.transpose[2..-3].map{|n| n.reduce(:+)}
        ].flatten
      end.sort_by{|r| [r[0] || 0, r[2]]}
      results.map { |result| Result.new(*result) }
    end
  end

  def client_names
    @client_names ||= Rails.cache.fetch("clients-lookup-hash-#{options[:develop]}", expires_in: expires_in) do
      stmt = <<-EOQ
        SELECT campaigns.id AS campaign_id, clients.id AS client_id, clients.name AS client_name
        FROM campaigns
        LEFT OUTER JOIN clients ON campaigns.client_id = clients.id
        #{!options[:develop] ? 'WHERE clients.for_test IS FALSE' : ''}
      EOQ
      res = Campaign.connection.query(stmt)
      res.each_with_object({}){|r, hash| hash[r[0]] = {client_id: r[1], client_name: r[2]}}
    end
  end

  def sql
    @sql ||= <<-SQL
      SELECT calculate_date,
             campaign_id,
             #{base_sql}
      FROM v4_campaigns_view
      #{conditions}
      GROUP BY 1, 2
    SQL
  end
end
