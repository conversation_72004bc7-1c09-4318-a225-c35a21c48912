class ScheduleReport::CapsExportCommand < ScheduleReport::BaseExportCommand
  include ScheduleReport::V4SupportConcern

  RESULT_ATTRIBUTES = ([
    :vendor_id_s,
    :vendor_name,
    :campaign_id_s,
    :campaign_name,
    :calculate_date_s
  ] + BASE_ATTRIBUTES + [:max_cap, :target_cap, :bind_action]).freeze

  Result = ResultCustomStruct.new(*RESULT_ATTRIBUTES)

  private

  def fetch_data
    @report_data ||= ConversionRecordRedshift.connection.query(sql).map do |result|
      row = Result.new(*result)

      key = "#{row.vendor_id}:#{row.campaign_id}:#{row.calculate_date}"
      revisioned_click_url = revisioned_click_url_by(row.campaign_id, row.vendor_id, row.calculate_date)
      next if revisioned_click_url.blank?

      row.max_cap, row.target_cap, row.bind_action = cap_data[key].presence || caps_from_click_url(revisioned_click_url)
      row
    end.compact
  end

  def sql
    @sql ||= <<-SQL
      SELECT vendor_id,
             vendor_name                               AS vendor_name,
             campaign_id,
             campaign_name                             AS campaign_name,
             calculate_date,
             #{base_sql}
      FROM v4_campaigns_view
      #{conditions}
      GROUP BY 1, 2, 3, 4, 5
      ORDER BY 5
    SQL
  end

  def cap_data
    @cap_data ||= begin
      click_urls_data = ClickUrl.connection.query(cap_sql)
      click_urls_data.each_with_object({}) do |value, c|
        date, vendor_id, campaign_id, id, max_cap, target_cap, bind_action = value
        key = "#{vendor_id}:#{campaign_id}:#{date}"
        c[key] = [max_cap, target_cap, ClickUrl.bind_actions.invert[bind_action]]
      end
    end
  end

  def cap_sql
    @cap_sql ||= <<-SQL
      SELECT DATE(effective_at) AS effective_at, c.vendor_id, c.campaign_id, c.id, c.hard_cap, c.advertiser_cap, c.bind_action
      FROM click_urls AS c LEFT OUTER JOIN click_url_cap_change_logs AS cccl ON c.id = cccl.click_url_id
      #{cap_conditions}
      GROUP BY 1, 2, 3, 4
    SQL
  end

  def cap_conditions
    @query = begin
      sql_condition = []
      sql_condition << "effective_at >= '#{start_date}' AND effective_at <= '#{end_date}'"
      sql_condition << "vendor_id IN (#{vendor_id.join(',')})"           if vendor_id.present?
      sql_condition << "campaign_id IN (#{campaign_id.join(',')})"       if campaign_id.present?
      sql_condition << 'status IN (4,99,100)'
      return unless sql_condition.present?
      "WHERE #{sql_condition.join(' AND ')}"
    end
  end

  def cap_cache_key
     @cap_cache_key ||= begin
      digest = Digest::MD5.hexdigest(cap_sql)
      "schedule-report-caps-data-#{digest}"
    end
  end

  def revisioned_click_url_by(campaign_id, vendor_id, date)
    ClickUrl
      .unscoped
      .where(campaign_id: campaign_id, vendor_id: vendor_id)
      .order(updated_at: :desc).detect do |click_url|
      revisioned_click_url = click_url&.get_reversion_by(date: date.to_date)
      revisioned_click_url.status == 'cu_normal'
    end
  end

  def caps_from_click_url(revisioned_click_url)
    [revisioned_click_url.hard_cap, revisioned_click_url.advertiser_cap, revisioned_click_url.bind_action]
  end

  def events
    # Compatible with older versions
    if options[:track_type].present?
      Array[options[:track_type]]
    elsif options[:conversion].kind_of?(Array)
      options[:conversion]
    else
      Array[options[:conversion]]
    end
  end
end
