class ScheduleReport::ClientsIndexExportCommand < ScheduleReport::BaseExportCommand
  include ScheduleReport::V4SupportConcern

  RESULT_ATTRIBUTES = ([
    :client_id_s,
    :client_name,
  ] + BASE_ATTRIBUTES).freeze

  Result = ResultCustomStruct.new(*RESULT_ATTRIBUTES)

  private

  def fetch_data
    Rails.cache.delete(cache_key) if clear_cache?
    @data ||= Rails.cache.fetch(cache_key, expires_in: expires_in) do
      query_results = ConversionRecordRedshift.connection.query(sql).to_a
      client_results = []
      query_results.each do |r|
        (0..10).each{ |n| r[n] = r[n].to_i }
        (11..14).each{ |n| r[n] = r[n].present? ? r[n].to_d : 0 }
        r << (client_names[r[0]] ? client_names[r[0]][:client_id] : nil)
        r << (client_names[r[0]] ? client_names[r[0]][:client_name] : nil)
        client_results << r
      end
      results = client_results.group_by{|r| [r[-2], r[-1]]}.map do |k, v|
        [
          k[0],
          k[1],
          v.transpose[1..-3].map{|n| n.reduce(:+)}
        ].flatten
      end
      results.map { |result| Result.new(*result) }
    end
  end

  def client_names
    @client_names ||= Rails.cache.fetch("clients-lookup-hash-#{options[:develop]}", expires_in: expires_in) do
      stmt = <<-EOQ
        SELECT campaigns.id AS campaign_id, clients.id AS client_id, clients.name AS client_name
        FROM campaigns
        LEFT OUTER JOIN clients ON campaigns.client_id = clients.id
        #{!options[:develop] ? 'WHERE clients.for_test IS FALSE' : ''}
      EOQ
      res = Campaign.connection.query(stmt)
      res.each_with_object({}){|r, hash| hash[r[0]] = {client_id: r[1], client_name: r[2]}}
    end
  end

  def sql
    @sql ||= <<-SQL
      SELECT campaign_id,
             #{base_sql}
      FROM v4_campaigns_view
      #{conditions}
      GROUP BY 1
      ORDER BY 1
    SQL
  end
end
