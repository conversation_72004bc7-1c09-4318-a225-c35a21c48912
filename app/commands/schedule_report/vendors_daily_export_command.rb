class ScheduleReport::VendorsDailyExportCommand < ScheduleReport::BaseExportCommand
  include ScheduleReport::V4SupportConcern

  RESULT_ATTRIBUTES = ([
    :vendor_id_s,
    :vendor_name,
    :calculate_date_s,
  ] + BASE_ATTRIBUTES).freeze

  Result = ResultCustomStruct.new(*RESULT_ATTRIBUTES)

  private

  def fetch_data
    Rails.cache.delete(cache_key) if clear_cache?
    @data ||= Rails.cache.fetch(cache_key, expires_in: expires_in) do
      return [] unless vendor_id.present?
      results = ConversionRecordRedshift.connection.query(sql).to_a
      results.map { |result| Result.new(*result) }
    end
  end

  def sql
    @sql ||= <<-SQL
      SELECT vendor_id                                  AS vendor_id,
             vendor_name                                AS vendor_name,
             calculate_date                             AS calculate_date,
             #{base_sql}
      FROM v4_campaigns_view
      #{conditions}
      GROUP BY 1, 2, 3
      ORDER BY 1, 3
    SQL
  end
end
