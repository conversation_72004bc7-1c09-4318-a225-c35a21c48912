module ScheduleReport::V4SupportConcern
  extend ActiveSupport::Concern

  def spend_statuses
    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :spend_calculation)
    status_list = status_list + ['over_cap'] if options[:include_overcap].present?
    status_list
  end


  def install_statuses
    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :value)
    status_list = status_list + ['over_cap'] if options[:include_overcap].present?
    status_list
  end

  def statuses
    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :value)
    return status_list unless status_group == 'default'

    cpi_group? ? [:normal, :injected, :over_cap, :manual_stopped] : [:normal, :injected]
  end
end
