class ScheduleReport::SourcesIndexExportCommand < ScheduleReport::BaseExportCommand

  RESULT_ATTRIBUTES = [
    :source_id,
    :vendor_id_s,
    :vendor_name,
    :campaign_id_s,
    :campaign_name,
    :click_id_s,
    :paid_action,
    :app_id,
    :app_name,
    :impression_count_s,
    :click_count_s,
    :install_count_s,
    :retained_count_s,
    :tutorial_count_s,
    :registration_count_s,
    :purchase_count_s,
    :first_purchase_count_s,
    :level_count_s,
    :open_count_s,
    :spend_s,
    :gross_spend_s,
    :net_spend_s,
    :purchase_amount_s,
    :title
  ].freeze

  Result = ResultCustomStruct.new(*RESULT_ATTRIBUTES)

  def call
    offset = 0
    csv_stream = Enumerator.new do |yielder|
      yielder << CSV.generate_line(csv_header)
      csv_has_data = false
      per_page = 10000
      loop do
        rows = ConversionRecordRedshift.connection.execute(sql(per_page, offset))
        rows.each do |row|
          csv_row = csv_report_row(Result.new(*row.values))
          next if all_zero?(csv_row)
          yielder << CSV.generate_line(csv_row)
          csv_has_data = true
        end
        offset += per_page
        break if rows.to_a.size < per_page
      end
      SlackService.send_notification_to_channel("Schedule Export: #{@template.name}, ID: #{@template.id} has no data.", :mighty) if !csv_has_data
    end
    [csv_stream, data_to_compare]
  end

  private

  def data_to_compare
    return if filter_id_blank?
    result = connection.query(aggregated_sql)[0]
    {
      click: result[0].to_i,
      install: result[1].to_i,
      spend: result[2]&.to_f
    }.with_indifferent_access
  end

  def all_zero?(csv_row)
    value_indexes = []
    csv_header.each_with_index do |header, index|
      value_indexes << index unless [
        'CAMPAIGN',
        'VENDOR',
        'APP NAME',
        'APP ID',
        'PAID ACTION',
        'TITLE'
      ].include?(header)
    end
    value_indexes.all? do |index|
      value_is_zero?(csv_row[index], csv_header[index])
    end
  end

  def value_is_zero?(value, header_str)
    if header_str =~ /CVR|MARGIN/
      value == '0 %'
    else
      value.zero?
    end
  end

  def csv_header
    header.map do |h|
      case h
        when 'events' then events.map { |event| "#{event} EVENT".upcase }
        when 'cpa'    then events.map { |event| "#{event} CPA".upcase }
        when 'cvr2'   then events.map { |event| "#{event} CVR2".upcase }
        when 'purchase_amount' then 'PURCHASE REVENUE'
        else
          h.gsub('_', ' ').upcase
      end
    end.flatten
  end

  def csv_report_row(result)
    header.each_with_object([]) do |column, row|
      if result.vendor_id.blank? && %w(campaign vendor app_name app_id)
        value = ''
      else
        value = case column
                when 'campaign'    then result.campaign_name
                when 'vendor'      then result.vendor_name
                when 'app_name'    then result.app_name
                when 'app_id'      then options[:template_id].to_i == 3502 ? result.original_app_id : result.unique_app_id
                when 'impressions' then result.impression_count
                when 'clicks'      then result.click_count
                when 'installs'    then result.install_count
                when 'events'      then events.map { |event| result.event_count(event) }
                when 'spend'       then result.spend.round(2)
                when 'margin'      then to_percentage(result.margin)
                when 'net_revenue' then result.net_revenue.round(2)
                when 'cpi'         then result.cpi.round(2)
                when 'cpa'         then events.map { |event| result.cpa(event).round(2) }
                when 'cvr'         then to_percentage(result.cvr)
                when 'cvr2'        then events.map { |event| to_percentage(result.cvr2(event)) }
                when 'purchase_amount' then result.purchase_amount.round(2)
                end
      end
      row << value
    end.flatten
  end

  def sources_view
    "v4_sources_view"
  end

  def conditions
    @conditions ||= begin
                      sql_condition = []

                      sql_condition << "calculate_date >= '#{start_date}' AND calculate_date <= '#{end_date}'"
                      sql_condition << "campaign_id IN (#{campaign_id.join(',')})"          if campaign_id.present?
                      sql_condition << "vendor_id IN (#{vendor_id.join(',')})"              if vendor_id.present?
                      sql_condition << "status IN ('#{transform_statuses(statuses).join("','")}')" if statuses.present?
                      sql_condition << "app_name = '#{options[:app_name]}'"                 if options[:app_name].present?

                      app_id_condition = if options[:app_id].present?
                                           app_id_exact_match? ? "app_id = '#{options[:app_id]}'" : "app_id LIKE '%#{options[:app_id]}%'"
                                         end
                      sql_condition << app_id_condition if app_id_condition.present?

                      return if sql_condition.blank?
                      "WHERE #{sql_condition.join(' AND ')}"
                    end
  end

  def sort_desc?
    sort_param = options[:sort]
    return false unless sort_param.present?

    sort_param.start_with?('-')
  end

  def app_id_exact_match?
    options[:app_id_exact_match] == 'true'
  end

  def sql(per_page, offset)
    <<-SQL
      SELECT
        source_id                                                            AS source_id,
        vendor_id                                                            AS vendor_id,
        vendor_name                                                          AS vendor_name,
        campaign_id                                                          AS campaign_id,
        campaign_name                                                        AS campaign_name,
        click_id                                                             AS click_id,
        paid_action                                                          AS paid_action,
        app_id                                                               AS app_id,
        app_name                                                             AS app_name,
        impression_count                                                     AS impression_count,
        click_count                                                          AS click_count,
        install_count                                                        AS install_count,
        retained_count                                                       AS retained_count,
        tutorial_count                                                       AS tutorial_count,
        registration_count                                                   AS registration_count,
        purchase_count                                                       AS purchase_count,
        first_purchase_count                                                 AS first_purchase_count,
        level_count                                                          AS level_count,
        open_count                                                           AS open_count,
        spend                                                                AS spend,
        gross_spend                                                          AS gross_spend,
        net_spend                                                            AS net_spend,
        purchase_amount                                                      AS purchase_amount
      FROM (
        SELECT source_id                                                     AS source_id,
               vendor_id                                                     AS vendor_id,
               vendor_name                                                   AS vendor_name,
               campaign_id                                                   AS campaign_id,
               campaign_name                                                 AS campaign_name,
               click_id                                                      AS click_id,
               paid_action                                                   AS paid_action,
               app_id                                                        AS app_id,
               app_name                                                      AS app_name,
               SUM(impression_count)                                         AS impression_count,
               SUM(click_count)                                              AS click_count,
               SUM(#{filter_field_by_status('install_count', transform_statuses(install_statuses))}) AS install_count,
               SUM(#{normalize :retained_count})                             AS retained_count,
               SUM(#{normalize :tutorial_count})                             AS tutorial_count,
               SUM(#{normalize :registration_count})                         AS registration_count,
               SUM(#{normalize :purchase_count})                             AS purchase_count,
               SUM(#{normalize :first_purchase_count})                       AS first_purchase_count,
               SUM(#{normalize :level_count})                                AS level_count,
               SUM(#{normalize :open_count})                                 AS open_count,
               SUM(#{filter_field_by_status(spend, transform_statuses(spend_statuses))})      AS spend,
               SUM(#{filter_field_by_status(:spend, transform_statuses(spend_statuses))})     AS gross_spend,
               SUM(#{filter_field_by_status(:net_spend, transform_statuses(spend_statuses))}) AS net_spend,
               SUM(#{normalize_purchase_amount})                             AS purchase_amount
        FROM #{sources_view}
        #{conditions}
        GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9
      ) AS facts
      ORDER BY #{sort_field} #{sort_desc? ? 'desc' : 'asc'}
      LIMIT #{per_page}
      OFFSET #{offset}
    SQL
  end

  def aggregated_sql
    @aggregated_sql ||= <<-SQL
      SELECT
             SUM(click_count) AS click_count,
             SUM(#{filter_field_by_status('install_count', transform_statuses(install_statuses))}) AS install_count,
             SUM(#{filter_field_by_status(spend, transform_statuses(spend_statuses))})      AS spend
      FROM #{sources_view}
      #{conditions}
    SQL
  end

  def connection
    RedshiftConversionRecord.connection
  end
end
