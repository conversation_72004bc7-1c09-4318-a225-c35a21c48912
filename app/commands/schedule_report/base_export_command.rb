require "csv"

class ScheduleReport::BaseExportCommand < ScheduleReport::ApplicationCommand
  prepend SimpleCommand

  BASE_ATTRIBUTES = [
    :impression_count_s,
    :click_count_s,
    :install_count_s,
    :retained_count_s,
    :tutorial_count_s,
    :registration_count_s,
    :purchase_count_s,
    :first_purchase_count_s,
    :level_count_s,
    :open_count_s,
    :spend_s,
    :gross_spend_s,
    :net_spend_s,
    :purchase_amount_s
  ].freeze

  def call
    csv_stream = CSV.generate(headers: true) do |csv|
      csv << csv_header

      if filter_id_blank?
        content = "template name: #{@template.name}, template ID: #{@template.id} was exported with blank."
        NotificationMailer.notify('Schedule export data scope alert', content, MailGroup.mapping_mails("ScheduleReport::BaseExportCommand##{__callee__}"))
      else
        csv_has_data = false
        fetch_data.each do |result|
          csv_row = result_to_csv(result)
          next if all_zero?(csv_row)
          csv << csv_row
          csv_has_data = true
        end
        SlackService.send_notification_to_channel("Schedule Export: #{@template.name}, ID: #{@template.id} has no data.", :mighty) if fetch_data.blank? || !csv_has_data
      end
    end
    [csv_stream, data_to_compare]
  end

  protected

  def data_to_compare
    return if filter_id_blank?
    result = fetch_data.map { |r| [r.click_count, r.install_count, r.spend] }.transpose.map(&:sum)
    {
      click: result[0],
      install: result[1],
      spend: result[2]&.to_f
    }.with_indifferent_access
  end

  def all_zero?(csv_row)
    value_indexes = []
    csv_header.each_with_index do |header, index|
      value_indexes << index unless [
        'CAMPAIGN NAME',
        'CLIENT NAME',
        'VENDOR NAME',
        'DATE',
        'PAID ACTION',
      ].include?(header)
    end
    value_indexes.all? do |index|
      value_is_zero?(csv_row[index], csv_header[index])
    end
  end

  def value_is_zero?(value, header)
    if header =~ /CVR|MARGIN/
      value == '0 %'
    else
      value.zero?
    end
  end

  def daily_header
    if export_daily
      if header.include?('vendor') || header.include?('client') || header.include?('campaign')
        @daily_header ||= header.insert(1, 'date')
      else
        @daily_header ||= header.unshift('date')
      end
    elsif self.class.name == 'ScheduleReport::CampaignsVendorExportCommand'
      @daily_header ||= header.unshift('date')
    else
      @daily_header ||= header
    end
  end

  def csv_header
    daily_header.map do |h|
      case h
      when 'campaign'    then 'CAMPAIGN NAME'
      when 'client'      then 'CLIENT NAME'
      when 'vendor'      then 'VENDOR NAME'
      when 'paid_action' then 'PAID ACTION'
      when 'events'      then events.map { |event| "#{event} EVENT".upcase }
      when 'cpa'         then events.map { |event| "#{event} CPA".upcase }
      when 'cvr2'        then events.map { |event| "#{event} CVR2".upcase }
      when 'purchase_amount' then 'PURCHASE REVENUE'
      else
        h.gsub('_', ' ').upcase
      end
    end.flatten
  end

  def result_to_csv(result)
    daily_header.each_with_object([]) do |column, row|
      value = case column
                when 'campaign'    then first_column(result, 'campaign')
                when 'client'      then first_column(result, 'client')
                when 'vendor'      then first_column(result, 'vendor')
                when 'date'        then result.calculate_date
                when 'max_cap'     then result.max_cap || 0
                when 'target_cap'  then result.target_cap || 0
                when 'impressions' then result.impression_count
                when 'clicks'      then result.click_count
                when 'blocked_clicks' then result.blocked_click_count.to_i
                when 'installs'    then result.install_count
                when 'events'      then events.map { |event| result.event_count(event) }
                when 'spend'       then result.spend.round(2)
                when 'gross'       then result.gross_spend.round(2)
                when 'net'         then result.net_spend.round(2)
                when 'margin'      then to_percentage(result.margin)
                when 'net_revenue' then result.net_revenue.round(2)
                when 'cpi'         then result.cpi.round(2)
                when 'cpa'         then events.map { |event| result.cpa(event).round(2) }
                when 'cvr'         then to_percentage(result.cvr)
                when 'cvr2'        then events.map { |event| to_percentage(result.cvr2(event)) }
                when 'purchase_amount' then result.purchase_amount.round(2)
                when 'paid_action'     then result.bind_action
              end
      row << value
    end.flatten
  end

  def first_column(result, name)
    result.send("#{name}_id".to_sym).present? ? result.send("#{name}_name".to_sym) : ''
  end

  def to_percentage(num)
    "#{(num * 100).round(2)} %"
  end

  def base_sql
    <<-SQL
      SUM(impression_count)                      AS impression_count,
      SUM(click_count)                           AS click_count,
      SUM(#{install_count})                      AS install_count,
      SUM(#{normalize :retained_count})          AS retained_count,
      SUM(#{normalize :tutorial_count})          AS tutorial_count,
      SUM(#{normalize :registration_count})      AS registration_count,
      SUM(#{normalize :purchase_count})          AS purchase_count,
      SUM(#{normalize :first_purchase_count})    AS first_purchase_count,
      SUM(#{normalize :level_count})             AS level_count,
      SUM(#{normalize :open_count})              AS open_count,
      SUM(#{normalize_spend spend})              AS spend,
      SUM(#{normalize_spend :spend})             AS gross_spend,
      SUM(#{normalize_spend :net_spend})         AS net_spend,
      SUM(#{normalize_purchase_amount})          AS purchase_amount
    SQL
  end
end
