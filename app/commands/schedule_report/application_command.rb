class ScheduleReport::ApplicationCommand
  include ScheduleReport::CommandResultHelper
  
  attr_reader :start_date, :end_date, :export_daily, :options, :header, :events

  def initialize(start_date, end_date, events, options = {})
    @start_date = start_date
    @end_date   = end_date 
    @header     = options[:export_fields]
    @events     = Array(events)
    @export_daily = options[:export_daily]
    @template = options[:template]
    @options    = options
  end

  protected

  def spend_statuses
    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :spend_calculation)
    status_list = status_list + ['over_cap'] if options[:include_overcap].present?
    status_list.map { |name| NewConversionRecord.statuses[name] }
  end

  def install_statuses
    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :value)
    status_list = status_list + ['over_cap'] if options[:include_overcap].present?
    status_list.map { |name| NewConversionRecord.statuses[name] }
  end

  def transform_statuses(statues_numbers)
    return statues_numbers unless statues_numbers&.any?
    statues_numbers.map { |n| NewConversionRecord.statuses.key(n) }
  end

  def install_count
    <<-SQL
    CASE
    WHEN status IN ('#{install_statuses.join("','")}')
    THEN install_count
    ELSE 0
    END
    SQL
  end

  def normalize_spend(field)
    <<-SQL
      CASE
      WHEN status IN ('#{spend_statuses.join("','")}')
      THEN #{field}
      ELSE 0
      END
    SQL
  end

  def filter_field_by_status(field, valid_status)
    <<-SQL
      CASE
      WHEN status IN ('#{valid_status.join("','")}')
      THEN #{field}
      ELSE 0
      END
    SQL
  end

  def status_group
    options[:status_group].presence || 'default'
  end

  def statuses
    @statuses ||= begin
                    status_list = NewConversionRecord::STATUS_GROUPS.dig(status_group, :value).map { |name| NewConversionRecord.statuses[name] }
                    return status_list unless status_group == 'default'

                    cpi_group? ? [1, 3, 5, 6] : [1, 3]
                  end
  end

  def filter_id_blank?
    filter_campaign? && campaign_id.blank? ||
      filter_vendor? && vendor_id.blank? ||
      filter_client? && client_ids.blank?
  end

  def filter_campaign?
    options[:parent_campaign_id].present? || options[:campaign_id].present? || options[:client_id].present? || options[:os].present? || options[:country].present?
  end

  def filter_vendor?
    options[:vendor_id].present? || options[:vendor_owner_id].present? || options[:vendor_collection].present?
  end

  def filter_client?
    options[:client_id].present? && options[:client_id] != 0
  end

  def campaign_id
    @campaign_id ||= begin
                       command_options = {
                         owner_id:        options[:owner_id],
                         client_id:       client_ids,
                         campaign_ids:    options[:campaign_id],
                         parent_campaign_id: options[:parent_campaign_id],
                         develop:         options[:develop]
                       }
                       CampaignIdExtractionCommand.call(command_options).result
                     end
  end

  def vendor_id
    @vendor_id ||= begin
                     command_options = {
                       vendor_id:         options[:vendor_id],
                       vendor_collection: options[:vendor_collection],
                       client_id:         client_ids,
                       develop:           options[:develop],
                       excluded_vendor_id: options[:excluded_vendor_id],
                     }

                     VendorIdExtractionCommand.call(command_options).result
                   end
  end

  def client_ids
    @client_ids ||= options[:client_id]
  end

  def cohort?
    return true unless options[:cohort].present?
    options[:cohort] == 'cohort'
  end

  def gross?
    return true unless options[:spend].present?
    options[:spend] == 'gross'
  end

  def spend
    gross? ? :spend : :net_spend
  end

  def normalize(field)
    cohort? ? "post_#{field}" : field
  end

  def normalize_purchase_amount
    cohort? ? 'revenue_30' : 'purchase_amount'
  end

  def cpi_group?
    return true unless options[:cost_group].present?
    options[:cost_group].to_s == 'cpi'
  end

  def clear_cache?
    options[:clear_cache].present?
  end

  def expires_in
    start_time = Time.zone.now
    end_time   = 1.hours.from_now.beginning_of_hour
    (end_time - start_time).round
  end

  def conditions
    @conditions ||= begin
                      sql_condition = []

                      sql_condition << "calculate_date >= '#{start_date}' AND calculate_date <= '#{end_date}'"
                      sql_condition << "campaign_id IN (#{campaign_id.join(',')})"          if campaign_id.present?
                      sql_condition << "vendor_id IN (#{vendor_id.join(',')})"              if vendor_id.present?
                      sql_condition << "status IN ('#{statuses.join("','")}')"              if statuses.present?

                      return unless sql_condition.present?
                      "WHERE #{sql_condition.join(' AND ')}"
                    end
  end

  def cache_key
    current_class_name = self.class.name

    @cache_key ||= begin
                     digest = Digest::MD5.hexdigest(sql)
                     "dashboard-v2-#{current_class_name}-#{digest}"
                   end
  end

  def sort_field
    sort_param = options[:sort] || '-install_count'
    sort_param.start_with?('-') ? sort_param[1..-1] : sort_param
  end
end
