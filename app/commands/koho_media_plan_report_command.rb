# frezen_string_literal: true

class KohoMediaPlanReportCommand
  include FileUtils
  include ActionView::Helpers::NumberHelper

  SCOPE = Google::Apis::SheetsV4::AUTH_SPREADSHEETS
  SPREADSHEET_ID = '1T5sbGb2CV8NhscGnNBodmB_GZjYqkdTTiw4XvN73S2o'.freeze
  # SPREADSHEET_ID = '1p3AKasx2V0rk3f5n5oecatbdMpyXHgz9qox0yX9s8mQ'.freeze  # test
  
  attr_reader :date, :start_date, :end_date

  def initialize(date)
    @date = date.to_date
    @service = GoogleSheetsService.new(SPREADSHEET_ID)
  end

  def call
    datas = fetch_datas
    datas.each do |data|
      search_hash = {0 => data['Date'], 1 => data['CAMPAIGN on AF']}
      @service.update_or_append_row2("Margin", search_hash, data.values, exclude_columns: [])
    end
  end

  def fetch_datas
    @data ||= KohoReportDataCommand.call(date).result
  end
end
