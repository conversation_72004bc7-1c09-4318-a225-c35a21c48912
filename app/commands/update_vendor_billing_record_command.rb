class UpdateVendorBillingRecordCommand
  prepend SimpleCommand

  attr_reader :vendor_id,
    :vendor_name,
    :start_date,
    :end_date,
    :billing_month,
    :is_historical

  def initialize(options = {})
    @start_date    = options[:start_date]&.to_date
    @end_date      = options[:end_date]&.to_date
    @billing_month = options[:billing_month]
    @vendor_id     = options[:vendor_id]
    @vendor_name   = options[:vendor_name]
    @is_historical = options[:is_historical]
  end

  def call
    update_vendor_billing_record!
  end

  private

  def update_vendor_billing_record!
    vendor_billing_record.update!(
      vendor_name: vendor_name,
      clicks: clicks,
      installs:  installs,
      events: paid_events,
      gross_spends: gross_spends,
      net_spends: net_spends,
      total_makegoods: total_makegoods,
      total_invoice: total_invoice,
      non_billable_vendor_spend: non_billable_vendor_spend,
      total_clicks: total_clicks,
    )
  end

  def clicks
    results.each_with_object([]) do |item, array|
      array << item.click_count
    end.sum.to_i
  end

  def total_clicks
    results.each_with_object([]) do |item, array|
      array << item.total_clicks.to_i
    end.sum.to_i
  end

  def installs
    results.each_with_object([]) do |item, array|
      array << item.install_count
    end.sum.to_i
  end

  def gross_spends
    results.each_with_object([]) do |item, array|
      array << item.gross_spend.to_d
    end.sum.to_d
  end

  def net_spends
    results.each_with_object([]) do |item, array|
      array << item.net_spend.to_d
    end.sum.to_d
  end

  def results
    @results ||= VendorBillingIndexCommand.call(
      start_date,
      end_date,
      vendor_id: vendor_id
    ).result
  end

  def total_makegoods
    ClientMakegood.
      without_deleted.
      where(
        month: billing_month,
        vendor_id: vendor_id,
        campaign_id: campaign_ids
      ).sum(:net_amount)
  end

  def campaign_ids
    date = "#{billing_month}-01".to_date
    if is_historical?
      HistoricalRecord.connection.query(
        "select distinct(campaign_id) from campaigns_view where vendor_id = #{vendor_id}
        and calculate_date between '#{date}' AND '#{date.end_of_month}'"
      ).to_a.flatten
    else
      campaign_id_mapping(date)[vendor_id.to_s] || []
    end
  end

  def campaign_id_mapping(date)
    @campaign_id_mapping ||= Rails.cache.fetch("update_vendor_billing_record_command_campaign_id_mapping_#{date}", expires_in: 10.minutes) do
      RedshiftConversionRecord.connection.query(
        "select distinct vendor_id, campaign_id from arbitrage_v4_campaigns_view where calculate_date between '#{date}' AND '#{date.end_of_month}'"
      ).to_a.group_by{|item| item[0]}.map{|k,v| [k, v.map(&:last)]}.to_h
    end
  end

  def non_billable_vendor_spend
    AdvertiserSpend.
      where(
        vendor_id: vendor_id,
        spend_month: billing_month,
        status: 3,
        send_to_vendor: false,
        send_csv_log: false
        ).inject(0) do |sum, advertiser_spend|
          if advertiser_spend.action_count.zero?
            sum
          else
            sum +
            (advertiser_spend.net_spend || 0) *
            advertiser_spend.release_action_count /
            advertiser_spend.action_count
          end
        end
  end

  def campaigns_paid_actions
    @campaigns_paid_actions ||= Campaign.paid_actions(vendor_id)
  end

  def paid_events
    conditions = {
      report_date: start_date..end_date,
      status: NewConversionRecord::STATUS_GROUPS.dig("default", :value),
      vendor_id: vendor_id
    }

    events = 0

    campaigns_paid_actions.each do |paid_action, campaign_ids|
      events += V4StatRecordRedshift.where(conditions.merge(campaign_id: campaign_ids)).sum(paid_action.to_sym)
    end

    events
  end

  def total_invoice
    VendorInvoicedTotal.
      where.not(client_id: nil).
      where(month: billing_month, vendor_id: vendor_id).
      sum(:amount)
  end

  def vendor_billing_record
    VendorBillingRecord.
      find_or_create_by!(
        vendor_id: vendor_id,
        month: billing_month,
      )
  end

  def is_historical?
    is_historical == true
  end
end
