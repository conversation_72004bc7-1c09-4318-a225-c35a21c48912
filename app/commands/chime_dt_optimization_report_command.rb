class ChimeDtOptimizationReportCommand
  prepend SimpleCommand

  include AppsflyerAgencyMappings
  include AppsflyerSubParam5Checker

  include ActionView::Helpers::NumberHelper

  CLIENT_ID = 74
  CLICK_URL_ID = 19717
  AF_APP_ID = 'com.onedebit.chime'
  HEADERS = ['af_siteid', 'campaign_name', 'date', 'clicks', 'installs', 'reg', 'level', 'spend'].freeze

  attr_reader :start_date, :end_date

  def initialize(start_date:, end_date:)
    @start_date = start_date
    @end_date   = end_date
  end

  def call
    tmpfile = generate_report
    upload_key = "chime_dt_optimization_reports/#{file_name}"
    AwsService.upload_to_s3_with_bucket(bucket_name, tmpfile.path, upload_key)

    s3_url = Encodable::UrlEncode.call("https://#{bucket_name}.s3.amazonaws.com/#{upload_key}")

    content = <<-EOF
      Chime DT Optimization Report Download Url: <a href="#{s3_url}">#{s3_url}</a>
    EOF

    NotificationMailer.notify_subscribers("Chime DT Optimization Report #{start_date} - #{end_date}", content, subscription).deliver_now
    tmpfile.close!
  end

  def generate_report
    tmpfile = Tempfile.new(file_name)
    CSV.open(tmpfile.path, 'w+') do |csv|
      csv << HEADERS
      (start_date..end_date).each do |date|
        service = AppsflyerApiServiceV3.new(af_app_id: AF_APP_ID, client_id: CLIENT_ID, start_date: date, end_date: date)
        records = service.in_app_events_report
        events = {}
        records.each do |record|
          record = record.to_h
          event_name = record['Event Name']&.downcase
          ourl = record['Original URL']
          params = Rack::Utils.parse_query((URI(ourl)).query)
          next if params['af_siteid'].blank?
          af_siteid = params['af_siteid'][-1]
          click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
          next if click_url_id.to_i != click_url.id

          row = events[af_siteid]
          if row.present?
            events[af_siteid][:registration] =  get_in_app_events("REG", event_name, AF_APP_ID, row[:registration])
            events[af_siteid][:level] =  get_in_app_events("L", event_name, AF_APP_ID, row[:level])
          else
            events[af_siteid] = {
              registration: get_in_app_events("REG", event_name, AF_APP_ID, 0),
              level: get_in_app_events("L", event_name, AF_APP_ID, 0),
            }
          end
        end

        records = service.installs_report
        install_data = Hash.new(0)
        records.each do |record|
          click_url_id = AppsflyerInAppEvent.get_click_url_id_from_record(record)
          ourl = record['Original URL']
          params = Rack::Utils.parse_query((URI(ourl)).query)
          next if params['af_siteid'].blank?
          af_siteid = params['af_siteid'][-1]
          next if record['Media Source'].strip == 'feedmob_int' || click_url_id.to_i != click_url.id

          install_data[af_siteid] += 1
        end

        (events.keys + install_data.keys).uniq.each do |af_siteid|
          spend = click_url.gross_cpi_by_date(date) * install_data[af_siteid].to_i
          csv << [
            af_siteid,
            campaign_name,
            date,
            nil,
            install_data[af_siteid].to_i,
            events[af_siteid] ? events[af_siteid][:registration].to_i : 0,
            events[af_siteid] ? events[af_siteid][:level].to_i : 0,
            spend
          ]
        end
      end
    end
    tmpfile
  end

  def file_name
    "chime_dt_optimization_report_#{start_date}_#{end_date}.csv"
  end

  def bucket_name
    Rails.env.production? ? "tracking-download" : "tracking-env-stage"
  end

  def subscription
    Subscription.by_subject("Chime DT Optimization Report")
  end

  def click_url
    @click_url ||= ClickUrl.find(CLICK_URL_ID)
  end

  def campaign_name
    @campaign_name ||= Campaign.find_by(id: click_url.campaign_id).name
  end
end
