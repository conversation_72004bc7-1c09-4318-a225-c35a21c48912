class CheckAfNonAgencyReportDifferenceCommand
  prepend SimpleCommand
  include AppsflyerNonAgencyMappings

  attr_reader :af_app_id, :client_id, :from_date, :to_date, :date

  def initialize(af_app_id:, client_id:, from_date:, to_date: )
    @af_app_id = af_app_id
    @client_id = client_id
    @from_date = from_date
    @to_date = to_date
    @date = @to_date
  end

  def call
    results = []
    mapped_multiple_click_urls = {}

    service = AppsflyerApiServiceV3.new(af_app_id: af_app_id, client_id: client_id, start_date: from_date, end_date: to_date)
    records = service.partners_by_date_report_non_agency(via_api: true)
    return results if records.nil?

    allowed_click_url_ids = app_id_click_urls(client_id)
    unarchived_click_url_mappings = unarchived_mappings(client_id, mapped_multiple_click_urls, to_date)
    archived_click_url_mappings = archived_mappings(client_id)
    universal_click_url_mappings = universal_mappings(client_id)

    records.each do |record|
      agency = record["Agency/PMD (af_prt)"].to_s.strip
      next if !agency.in?(['None', 'Feedmob'])
      next if record['Total Cost'].present? # 过滤用于上传 cost 的记录

      pid          = get_pid(record)
      date         = get_date(record)
      platform     = get_platform
      c            = get_c(record)
      spend        = get_spend(record)
      impression   = get_impression(record)
      click        = get_click(record)
      install      = get_install(record)

      click_url_id = AppsflyerNonAgencyReport.map_click_url_based_on_rule(client_id, af_app_id, platform, c)
      click_url_id = unarchived_click_url_mappings[[pid, c, platform]] if click_url_id.blank?
      click_url_id = archived_click_url_mappings[[pid, c, platform]] if click_url_id.blank?
      click_url_id = universal_click_url_mappings[[pid, c]] if click_url_id.blank?

      click_url_id = get_mapped_click_url_id(date, client_id, af_app_id, pid, c)

      click_url = ClickUrl.find_by(id: click_url_id)
      next if click_url_id.blank? || !click_url.fm_link? || click_url.vendor.for_test || click_url.archived? || not_belong_to_app?(click_url_id, allowed_click_url_ids)

      mapped_multiple_click_urls[[pid, c, platform]]&.delete(click_url_id) if install == 0

      result = find_difference(
        date,
        click_url_id,
        pid,
        c.force_encoding(Encoding::UTF_8),
        spend,
        click,
        impression,
        install
      )

      results << result if result.present?
    end

    results
  end

  private

  def get_pid(record)
    record["Media Source (pid)"].strip
  end

  def get_date(record)
    record["Date"].strip
  end

  def get_c(record)
    record["Campaign (c)"].strip
  end

  def get_spend(record)
    record["Total Cost"] == 'N/A' ? 0 : record["Total Cost"]
  end

  def get_impression(record)
    record["Impressions"] == 'N/A' ? 0 : record["Impressions"]
  end

  def get_click(record)
    record["Clicks"] == 'N/A' ? 0 : record["Clicks"]
  end

  def get_install(record)
    record["Installs"] == 'N/A' ? 0 : record["Installs"]
  end

  def get_platform
    if af_app_id.start_with?("id")
      'iOS'
    elsif af_app_id.start_with?("com")
      'Android'
    end
  end

  def find_difference(date, click_url_id, pid, c, spend, click, impression, install)
    conditions = { date: date, pid: pid, c: c, af_app_id: af_app_id }
    aar = AppsflyerNonAgencyReport.where(conditions.merge(click_url_id: click_url_id)).first
    return if aar&.click.to_i == click.to_i && aar&.impression.to_i == impression.to_i && aar&.install.to_i == install.to_i

    diff_click = (aar&.click.to_i - click.to_i).abs
    max_click = [aar&.click.to_i, click.to_i].max
    diff_click_percent = max_click.zero? ? 0 : diff_click / max_click
    if aar&.impression.to_i != impression.to_i ||
      aar&.install.to_i != install.to_i ||
      (diff_click > threshold_in_config[:click_count_threshold].to_i && diff_click_percent > threshold_in_config[:click_percent_threshold].to_f)

      {
        af_app_id: af_app_id,
        pid: pid,
        c: c,
        click_url_id: click_url_id,
        date: date,
        data: {
          api_click: click.to_i,
          db_click:  aar&.click.to_i,
          api_impression: impression.to_i,
          db_impression: aar&.impression.to_i,
          api_install: install.to_i,
          db_install:  aar&.install.to_i
        }
      }
    end
  end

  def compare_data(date, click_url_id, pid, c, spend, click, impression, install)
    aar = AppsflyerNonAgencyReport.find_by(
      date: date,
      click_url_id: click_url_id,
      pid: pid,
      c: c
    )

    {
      af_app_id: af_app_id,
      pid: pid,
      c: c,
      click_url_id: click_url_id,
      date: date,
      data: {
        api_click: click.to_i,
        db_click:  aar&.click&.to_i,
        api_impression: impression.to_i,
        db_impression: aar&.impression&.to_i,
        api_install: install.to_i,
        db_install:  aar&.install&.to_i
      }
    }
  end

  def app_id_click_urls(client_id)
    sql = ActiveRecord::Base.sanitize_sql([<<~SQL, client_id])
      SELECT DISTINCT cu.id
      FROM click_urls cu
      LEFT JOIN campaigns c on c.id = cu.campaign_id
      WHERE c.client_id = ?
    SQL

    ClickUrl.connection.query(sql).flatten
  end

  def get_mapped_click_url_id(date, client_id, af_app_id, pid, c)
    sql = ActiveRecord::Base.sanitize_sql([<<~SQL, date: date, client_id: client_id, af_app_id: af_app_id, pid: pid, c: c])
        SELECT mapped_click_url_id
        FROM api_pull_logs
        WHERE job_name = 'DownloadAppsFlyerNonAgencyReportJob'
        AND event_date = :date
        AND client_id = :client_id
        AND af_app_id = :af_app_id
        AND pid = :pid
        AND c = :c
    SQL
    ApiPullLog.connection.query(sql).flatten.first
  end

  def not_belong_to_app?(click_url_id, allowed_click_url_ids)
    allowed_click_url_ids.present? && !allowed_click_url_ids.include?(click_url_id)
  end

  def threshold_in_config
    @threshold_in_config ||= TrackingConfig.find_by(config_type: 'system', name: 'CHECK_AF_SPEND_REPORT_THRESHOLD').value.with_indifferent_access
  end
end
