class SyncPostbackDataCommand
  prepend SimpleCommand

  attr_reader :event_date, :uuids, :conditions

  def initialize(event_date, uuids: [])
    @event_date = event_date
    @uuids = uuids

    @conditions = { event_date: event_date, postback_type: 'send_to_vendor' }
    @conditions = @conditions.merge(conversion_uuid: uuids) if uuids.present?
  end

  def call
    postback_logs = ResendConversionPostbackLogRedshift.where(conditions)

    postback_logs.each do |log|
      update_daily_conversion_postback_data(log)
    end

    "Synced #{postback_logs.length} postback logs"
  end

  private

  def update_daily_conversion_postback_data(log)
    record = NewDailyConversionRecord.table_by_date(log.event_date).find_by(uuid: log.conversion_uuid)

    if record.present?
      record.memo = record.memo.merge(postback_time: log.postback_time)
      record.save
    end

    info = record&.info
    if info.present?
      info.response_code = log.response_code
      info.response_msg = log.response_message
      info.memo = info.memo.merge(postback_time: log.postback_time)
      info.save
    end

    update_conversion_postback_data(record, log)
  end

  def update_conversion_postback_data(cr, log)
    return if cr.blank? || log.blank?

    conversion_record = NewConversionRecord.by_date(cr.event_time.to_date).find_by(
      conversion_id: cr.conversion_id,
      status: cr.status,
      track_type: cr.track_type,
      repeated: cr.repeated
    )

    conversion_record.update!(
      response_code: log.response_code,
      response_msg: log.response_message,
      memo: conversion_record.memo.merge(postback_time: log.postback_time),
      imported: true
    ) if conversion_record.present?

    body =[
      {
        index: { _id: cr.uuid, data: cr.as_indexed_json },
        create: { _id: cr.uuid, data: cr.as_indexed_json }
      }
    ]

    ElasticsearchClient.conversion_record.bulk(
      index: 'conversion_records',
      type: 'conversion_record',
      body: body,
      pipeline: 'monthlyindex'
    )
  end
end
