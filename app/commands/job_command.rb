class JobCommand
  prepend SimpleCommand

  attr_reader :date, :job_name, :args

  def initialize(date: , job_name: , args: nil)
    @date = date
    @job_name = job_name
    @args = args
  end

  def call
    {
      triggered: triggered?,
      finished: finished_job?
    }
  end

  private


  def triggered?
    current_job_logs.present?
  end

  def finished_job?
    current_job_logs.present? && current_job_logs.first&.dig("_source", "error_messages").blank?
  end

  def current_job_logs
    @current_job_logs ||=
      begin
        if job_name.present?
          WorkerLogsIndex.get_job_by(job_name: job_name, date: date, args: args).dig("hits", "hits")
        else
          []
        end
      end
  end
end
