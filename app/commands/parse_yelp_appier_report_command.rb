# frezen_string_literal: true

require 'roo'

class ParseYelpAppierReportCommand < GmailReportBaseCommand
  prepend SimpleCommand

  YELP_APPIER_QUERY = 'from:<EMAIL> newer_than:1d has:attachment filename:xlsx subject:Appier Media Report -- Yelp IOS SKAN Daily Report'.freeze

  attr_reader :user_id, :date, :gmail

  def initialize(date)
    @date = date
    @user_id = 'me'
    @gmail = Google::Apis::GmailV1::GmailService.new
    @gmail.authorization = get_credentials
  end

  def call
    parse_email
  end

  private

  def parse_email
    user_messages = gmail.list_user_messages(user_id, q: YELP_APPIER_QUERY)
    message = user_messages.messages&.first
    if message.blank?
      errors.add(:no_email, "*No Email In Last One Day Received*")
      [0, 0, 0, 0]
    else
      analyze_email(gmail: gmail, user_id: user_id, message: message)
    end
  end

  def analyze_email(gmail:, user_id:, message:)
    email = gmail.get_user_message user_id, message.id
    attachment_id = email.payload.parts.last.body.attachment_id
    attachment_content = gmail.get_user_message_attachment user_id, email.id, attachment_id
    File.open(local_file_path, "wb"){|f| f.puts attachment_content.data }
    wb = Roo::Spreadsheet.open(local_file_path)
    last_row_index = wb.sheet('Daily Performance').last_row
    data = wb.sheet('Daily Performance').row(last_row_index - 1)
    if data && data[0] == date
      [data[2], data[3], data[4], data[9]]  # [impressions, clicks, actions, spend]
    else
      errors.add(:email_no_data, "No Data for #{date}")
      [0, 0, 0, 0]
    end
  ensure
    clear_local_tmp_files
  end

  def clear_local_tmp_files
    sh "rm -rf #{local_file_path}"
  end

  def local_file_path
    "/tmp/yelp_appier.xlsx"
  end
end
